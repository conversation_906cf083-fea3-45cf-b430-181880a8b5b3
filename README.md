# CreatorVerse Backend

FastAPI backend for CreatorVerse application with JSON-based configuration management.

## Tech Stack

- **Language**: Python 3.11
- **Framework**: FastAPI
- **Package Manager**: uv (fast Python package installer and resolver)
- **Static Analysis**: Flake8 (linting) + Autopep8 (formatting) + MyPy (type checking)
- **Configuration**: JSON-based configuration management

See [CODE_QUALITY_UPDATE.md](docs/CODE_QUALITY_UPDATE.md) for details on our code quality management approach.

## Development Environment Setup

This project now uses UV for package management and virtual environments. To set up your development environment:

### Windows

```bash
setup_env.bat
```

### Unix/Linux/Mac

```bash
./setup_env.sh
```

For more details about the environment setup, see the setup scripts in the repository root.

## Configuration Management

This project uses JSON files for configuration instead of environment variables, making it easy to switch between different environments.

### Available Configurations

- `default.json` - Default fallback configuration
- `development.json` - Development environment
- `testing.json` - Testing environment
- `production.json` - Production environment (should be excluded from git)

### Switching Configurations

1. **Using environment variable:**
   ```bash
   export APP_ENV=development
   python main.py
   ```

2. **Using the switch script:**
   ```bash
   python switch_config.py development
   ```

3. **Programmatically:**
   ```python
   from app.core.config_manager import config_manager
   settings = config_manager.load_config("production")
   ```

### Creating New Configurations

1. Create a new JSON file in the `config/` directory
2. Copy the structure from `default.json`
3. Modify the values as needed
4. Use the configuration name (without .json) when switching

### Configuration Structure

```json
{
  "PROJECT_NAME": "Your Project Name",
  "VERSION": "1.0.0",
  "API_V1_STR": "/api/v1",
  "DATABASE_URL": "your-database-url",
  "SECRET_KEY": "your-secret-key",
  "ACCESS_TOKEN_EXPIRE_MINUTES": 30,
  "ALGORITHM": "HS256",
  "ALLOWED_ORIGINS": ["http://localhost:3000"],
  "ENVIRONMENT": "development",
  "DEBUG": true
}
```

## Getting Started

### Prerequisites
- Python 3.11+
- uv package manager ([installation guide](https://docs.astral.sh/uv/getting-started/installation/))

### Installation

1. **Install dependencies using uv:**
   ```bash
   uv pip install -e .
   ```

2. **Choose your configuration:**
   ```bash
   python switch_config.py development
   ```

3. **Create database:** 
   ```bash
   python create_db.py
   ```

4. **Run the application:**
   ```bash
   python main.py
   ```

> **📖 For detailed IDE setup and development guidelines, see [DEVELOPMENT_GUIDELINES.md](docs/DEVELOPMENT_GUIDELINES.md)**

## Development Workflow

### Code Quality & Static Analysis

This project uses Ruff for linting and formatting, plus mypy for type checking. Use the provided scripts for consistent code quality:

#### Quick Commands

**Windows:**
- **Format code:** `format.bat` - Applies Ruff formatting and auto-fixes
- **Lint code:** `lint.bat` - Runs Ruff linting and mypy type checking
- **Full check:** `check.bat` - Comprehensive quality check (format, lint, type check, startup test)
- **Start dev server:** `start_dev.bat` - Starts development server with auto-reload
- **Start prod server:** `start_prod.bat` - Starts production server

**Linux/macOS:**
- **Format code:** `./format.sh` - Applies Ruff formatting and auto-fixes
- **Lint code:** `./lint.sh` - Runs Ruff linting and mypy type checking
- **Full check:** `./check.sh` - Comprehensive quality check (format, lint, type check, startup test)
- **Start dev server:** `./start_dev.sh` - Starts development server with auto-reload
- **Start prod server:** `./start_prod.sh` - Starts production server

> **Note:** Make shell scripts executable first: `chmod +x *.sh`

#### Manual Commands

```bash
# Format code
ruff format .

# Auto-fix linting issues
ruff check --fix .

# Run linting checks
ruff check .

# Type checking
mypy app --ignore-missing-imports

# Check formatting (without changes)
ruff format --check .

# Start development server
uvicorn main:app --reload --host 127.0.0.1 --port 8000

# Start production server
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Ruff Configuration

The project uses comprehensive Ruff configuration with the following rule sets:
- **E, W** - pycodestyle errors and warnings
- **F** - Pyflakes
- **UP** - pyupgrade (modern Python syntax)
- **B** - flake8-bugbear
- **SIM** - flake8-simplify
- **I** - isort (import sorting)
- **N** - pep8-naming
- **C4** - flake8-comprehensions
- **And more** - See configuration files (`.ruff.toml`, `mypy.ini`, etc.) for full configuration details

## API Endpoints

### Common Endpoints
- `GET /` - Welcome message with environment info
- `GET /health` - Health check with configuration info
- `GET /v1/master/roles` - Get available user roles
- `POST /v1/oauth/initiate` - Initiate OAuth flow (Google/Instagram)
- `GET /v1/oauth/callback` - Handle OAuth callback
- `POST /v1/auth/login/email` - Send login OTP via email
- `POST /v1/auth/verify-login-otp` - Verify login OTP and get tokens
- `POST /v1/logout` - Logout user and invalidate tokens
- `POST /v1/token/refresh` - Refresh access token

### Influencer Authentication
- `POST /v1/influencer/auth/register` - Register new influencer and send OTP
- `POST /v1/influencer/auth/register/verify-otp` - Verify registration OTP

### Brand Authentication & Organization Management
- `POST /v1/brand/auth/request-otp` - Request OTP for brand registration (blocks consumer domains)
- `POST /v1/brand/auth/verify-otp` - Verify OTP and handle organization creation/joining
- `GET /v1/brand/auth/domains/{domain}/organizations` - Get all verified organizations for domain
- `POST /v1/brand/auth/organizations/{org_id}/join` - Join existing organization as member
- `POST /v1/brand/auth/organizations` - Create new organization on verified domain
- `POST /v1/brand/auth/organizations/{org_id}/transfer-owner` - Transfer organization ownership
- `GET /v1/brand/auth/profile` - Get brand user profile with organizations
- `GET /v1/brand/auth/domains/verify?token={token}` - Verify domain ownership via email token

### Organization Flow Examples

#### First User from Domain (Auto-Owner)
1. `POST /v1/brand/auth/request-otp` - <EMAIL> requests OTP
2. `POST /v1/brand/auth/verify-otp` - Creates user + organization + domain claim + owner membership
3. Domain verification email sent automatically

#### Subsequent Users (Join/Create)
1. `POST /v1/brand/auth/request-otp` - <EMAIL> requests OTP  
2. `POST /v1/brand/auth/verify-otp` - Creates user, returns available organizations
3. `GET /v1/brand/auth/domains/brandx.com/organizations` - List verified organizations
4. `POST /v1/brand/auth/organizations/{org_id}/join` - Join as member OR
5. `POST /v1/brand/auth/organizations` - Create new organization (if domain verified)

#### Domain Verification (Manual)
1. User clicks verification link in email
2. `GET /v1/brand/auth/domains/verify?token=xyz` - Verifies domain ownership
3. Creates organization with user as owner

### Security Features
- **Consumer Domain Blocking**: Gmail, Yahoo, Hotmail, etc. blocked for brand registration
- **OTP Rate Limiting**: Max 5 attempts, 15-minute lockout after failures
- **Domain Uniqueness**: One domain claim per organization
- **Organization Code Uniqueness**: Per domain organization code constraints
- **Owner Transfer Audit**: Full ownership transfer history tracking
- **Cache-Aside Pattern**: Redis caching with database fallback for performance

## Project Structure

```
creatorverse_backend/
├── app/
│   ├── api/
│   │   ├── api_v1/
│   │   │   ├── endpoints/
│   │   │   │   ├── auth.py      # Authentication endpoints
│   │   │   │   ├── creators.py  # Creator management
│   │   │   │   └── users.py     # User management
│   │   │   └── api.py          # API router
│   │   └── deps.py             # Dependencies
│   ├── core/
│   │   ├── config.py           # Configuration models
│   │   ├── config_manager.py   # Configuration management
│   │   └── security.py         # Security utilities
│   └── schemas/                # Pydantic models
├── config/                     # JSON configuration files
├── *.bat                      # Development scripts (Windows)
├── *.sh                       # Development scripts (Linux/macOS)
├── main.py                    # Application entry point
└── requirements.txt         # Project dependencies
```

## Notes

- This microservice is part of the CreatorVerse project and handles user-related operations only
- Uses custom packages for logging, database operations (SQLAlchemy), and Redis
- No migration tools like Alembic are used
- All code follows mypy typing constraints
- Ruff is used instead of multiple static analysis tools (black, isort, flake8)
