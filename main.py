from collections.abc import Async<PERSON>enerator
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError

from app.api.api_v1.api import api_router
from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis
from app.core.exceptions import (
    CreatorVerseError,
    creatorverse_exception_handler,
    http_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.middleware import DEFAULT_ENDPOINT_LIMITS, RateLimitMiddleware
from app.utilities.startup_tasks import  initialize_all_startup_tasks_sync
from app.utilities.response_handler import create_success_response, create_health_response



@asynccontextmanager
async def lifespan(app_fast: FastAPI) -> AsyncGenerator[None, None]:
    """Lifespan context manager for FastAPI application."""
    # Startup
    print(f"Application starting up...{app_fast}")
    # Initialize all startup tasks
    try:
        redis_client = get_locobuzz_redis()
        db_conn = get_database()

        # Initialize all startup tasks
        initialize_all_startup_tasks_sync(redis_client, db_conn)

    except Exception as e:
        print(f"Failed to initialize startup tasks: {e}")

    yield
    # Shutdown
    print("Application shutting down...")
    # Graceful shutdown of all loggers
    # await EnhancedLoggerShutdownManager.shutdown_all()


app = FastAPI(
    title="CreatorVerse Users Backend API",
    version="1.0.0",
    description="CreatorVerse Users Backend API",
    lifespan=lifespan
)

# Register exception handlers for centralized error handling
app.add_exception_handler(CreatorVerseError, creatorverse_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*", "ngrok-skip-browser-warning"],
)

# Rate limiting middleware
app.add_middleware(
    RateLimitMiddleware,
    default_max_requests=100,  # General rate limit
    default_window_seconds=60,  # 1 minute window
    endpoint_limits=DEFAULT_ENDPOINT_LIMITS,  # Specific limits for auth endpoints
)

# Include API router
app.include_router(api_router, prefix="/v1")


@app.get("/")
def read_root():
    return {"message": "FastAPI is running via ngrok!"}


@app.get("/health")
async def health_check() -> dict[str, str | bool]:
    return create_health_response(
        status="healthy",
        additional_data={"environment": APP_CONFIG.environ}
    )