## Instruction for this project

1) This is a microservice that is a part of the project (creatorverse) this microservice only handles the operations related to users only
2) We will use the python package manager as the uv and python version 3.11
3) For logger, DB operation (sqlalchemy), redis We will use our own custom package
4) We will not use any migration tool like alembic or any other
5) All generated code must flow the mypy (typing constraints)
6) instead of adding many tools for static analysis, we will use the ruff
7) So whenever a new key is added in the redis, it has to follow the pattern CreatorVerse:feature:key
8) Do not do the formatting and linting after every code follow the guidelines in our project and toml file.
9) When in the edit mode, ask what changes need to be done, give me the full file name and only the required changes and nothing else.
10) We are using the redis from the app.utilities.redis_code with the decoded response as true so give me snippet or code with this consideration
11) when using the agenet mode, avoid the indentation and the syntax errors, and if they use the python -m py_compile to resolve the error one by one
12) whenever you want to use the logger, import it as from app.core.config import APP_CONFIG this AppConfig has logger
13) Bloom filters are good to check not in

14) In our project, if a brand is registering for the first time like from its domain no entry in the organization table, we can create one only in the case of the brand. If it is first from the domain, then we need to give him the role of the brand-admin

15) Always try to write the reusable function and use the proper naming convention and strategy.

16) Do not give me the code based on the .env file, give me the code based on the config.py file and the APP_CONFIG class in app/core/config.py

17) Don't add redis like this in the param redis_client: redis.Redis instead just redis_client No need to add the type hinting for the redis client in the parameters


18) Whenever creating a new function try to use the cache aside pattern if applicable

19) For every new function you need check if that already exists and following the cache aside pattern if not create the function that follows the cache aside pattern


20) whenver defing the redis_client in the param user redis_client = get_locobuzz_redis() and for the db_conn = get_datatabase() 

21) Beware of this 
```code
Root Cause Analysis (RCA)
The error occurs because:

Cache-aside pattern issue: The get_user_by_email_cache_aside function returns a User object that was retrieved outside the current transaction context.

Session boundary violation: In verify_login_otp_endpoint, you're calling create_otp_or_update_entry with a User object that's not bound to the current session.

Lazy loading failure: When the code tries to access user_data.is_active, SQLAlchemy attempts to lazy-load the attribute but can't because the User instance is detached from any session.
```

22) Always use the referene from models.md file for whatever fields are in the  models and the tables

23) Use the top level imports only in the files.