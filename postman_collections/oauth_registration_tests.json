{"info": {"name": "OAuth Registration Tests", "description": "Comprehensive tests for OAuth registration flows (Google, Instagram) with database and Redis verification", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8002", "type": "string"}, {"key": "provider", "value": "google", "type": "string"}, {"key": "user_type", "value": "influencer", "type": "string"}, {"key": "mock_oauth_code", "value": "mock_auth_code_12345", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "oauth_account_id", "value": "", "type": "string"}, {"key": "social_profile_id", "value": "", "type": "string"}], "item": [{"name": "Google OAuth Flow", "item": [{"name": "1. Initiate Google OAuth", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Auth URL returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('auth_url');", "    pm.expect(responseJson.auth_url).to.include('google');", "    pm.expect(responseJson.auth_url).to.include('oauth');", "});", "", "pm.test('State parameter included', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('state');", "    pm.collectionVariables.set('oauth_state', responseJson.state);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/oauth/initiate?provider={{provider}}&user_type={{user_type}}", "host": ["{{base_url}}"], "path": ["v1", "o<PERSON>h", "initiate"], "query": [{"key": "provider", "value": "{{provider}}"}, {"key": "user_type", "value": "{{user_type}}"}]}}}, {"name": "2. Complete Google OAuth Callback", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('OAuth registration successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('access_token');", "    pm.expect(responseJson).to.have.property('user');", "    pm.expect(responseJson).to.have.property('oauth_account');", "    ", "    // Store tokens and IDs for verification", "    pm.collectionVariables.set('access_token', responseJson.access_token);", "    pm.collectionVariables.set('user_id', responseJson.user.id);", "    pm.collectionVariables.set('oauth_account_id', responseJson.oauth_account.id);", "    ", "    if (responseJson.social_profile) {", "        pm.collectionVariables.set('social_profile_id', responseJson.social_profile.id);", "    }", "});", "", "pm.test('User data structure is correct', function () {", "    const responseJson = pm.response.json();", "    const user = responseJson.user;", "    ", "    pm.expect(user).to.have.property('id');", "    pm.expect(user).to.have.property('email');", "    pm.expect(user).to.have.property('user_type');", "    pm.expect(user.user_type).to.eql('influencer');", "    pm.expect(user.email_verified).to.be.true;", "});", "", "pm.test('OAuth account data is correct', function () {", "    const responseJson = pm.response.json();", "    const oauthAccount = responseJson.oauth_account;", "    ", "    pm.expect(oauthAccount).to.have.property('provider');", "    pm.expect(oauthAccount).to.have.property('provider_user_id');", "    pm.expect(oauthAccount.provider).to.eql('google');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/oauth/callback?code={{mock_oauth_code}}&state={{oauth_state}}&provider={{provider}}", "host": ["{{base_url}}"], "path": ["v1", "o<PERSON>h", "callback"], "query": [{"key": "code", "value": "{{mock_oauth_code}}"}, {"key": "state", "value": "{{oauth_state}}"}, {"key": "provider", "value": "{{provider}}"}]}}}, {"name": "3. Verify Database - Users Table (OAuth)", "event": [{"listen": "test", "script": {"exec": ["pm.test('OAuth user exists in database', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data.length).to.be.greaterThan(0);", "    ", "    const user = responseJson.data[0];", "    pm.expect(user.user_type).to.eql('influencer');", "    pm.expect(user.is_active).to.be.true;", "    pm.expect(user.email_verified).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"SELECT id, email, user_type, is_active, email_verified, created_at FROM users WHERE id = {{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/test/db-query", "host": ["{{base_url}}"], "path": ["test", "db-query"]}}}, {"name": "4. Verify Database - OAuth Accounts", "event": [{"listen": "test", "script": {"exec": ["pm.test('OAuth account created correctly', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data.length).to.be.greaterThan(0);", "    ", "    const oauthAccount = responseJson.data[0];", "    pm.expect(oauthAccount.user_id).to.eql(parseInt(pm.collectionVariables.get('user_id')));", "    pm.expect(oauthAccount.provider).to.eql('google');", "    pm.expect(oauthAccount.is_active).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"SELECT user_id, provider, provider_user_id, is_active, created_at FROM oauth_accounts WHERE user_id = {{user_id}} AND provider = 'google'\"\n}"}, "url": {"raw": "{{base_url}}/test/db-query", "host": ["{{base_url}}"], "path": ["test", "db-query"]}}}, {"name": "5. Verify Database - Social Profiles", "event": [{"listen": "test", "script": {"exec": ["pm.test('Social profile created for influencer', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    ", "    if (responseJson.data.length > 0) {", "        const socialProfile = responseJson.data[0];", "        pm.expect(socialProfile.user_id).to.eql(parseInt(pm.collectionVariables.get('user_id')));", "        pm.expect(socialProfile.platform).to.eql('google');", "        pm.expect(socialProfile.is_active).to.be.true;", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"SELECT user_id, platform, platform_user_id, is_active, created_at FROM social_profiles WHERE user_id = {{user_id}} AND platform = 'google'\"\n}"}, "url": {"raw": "{{base_url}}/test/db-query", "host": ["{{base_url}}"], "path": ["test", "db-query"]}}}, {"name": "6. Verify Database - User Auth Methods (OAuth)", "event": [{"listen": "test", "script": {"exec": ["pm.test('OAuth auth method recorded', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data.length).to.be.greaterThan(0);", "    ", "    const authMethod = responseJson.data[0];", "    pm.expect(authMethod.method_name).to.eql('oauth_google');", "    pm.expect(authMethod.is_primary).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"SELECT uam.user_id, uam.method_id, uam.is_primary, mam.method_name FROM user_auth_methods uam JOIN master_auth_methods mam ON uam.method_id = mam.id WHERE uam.user_id = {{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/test/db-query", "host": ["{{base_url}}"], "path": ["test", "db-query"]}}}]}, {"name": "Instagram OAuth Flow", "item": [{"name": "1. Setup Instagram Test Variables", "event": [{"listen": "prerequest", "script": {"exec": ["pm.collectionVariables.set('provider', 'instagram');", "pm.collectionVariables.set('user_type', 'influencer');", "pm.collectionVariables.set('mock_oauth_code', 'mock_instagram_code_67890');"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test('Variables set for Instagram test', function () {", "    pm.expect(pm.collectionVariables.get('provider')).to.eql('instagram');", "    console.log('Instagram OAuth test setup completed');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "2. Initiate Instagram OAuth", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Instagram auth URL returned', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('auth_url');", "    pm.expect(responseJson.auth_url).to.include('instagram');", "    pm.collectionVariables.set('oauth_state_ig', responseJson.state);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/oauth/initiate?provider=instagram&user_type=influencer", "host": ["{{base_url}}"], "path": ["v1", "o<PERSON>h", "initiate"], "query": [{"key": "provider", "value": "instagram"}, {"key": "user_type", "value": "influencer"}]}}}, {"name": "3. Complete Instagram OAuth <PERSON>back", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Instagram OAuth registration successful', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('access_token');", "    pm.expect(responseJson).to.have.property('user');", "    pm.expect(responseJson).to.have.property('social_profiles');", "    ", "    // Store for verification", "    pm.collectionVariables.set('access_token_ig', responseJson.access_token);", "    pm.collectionVariables.set('user_id_ig', responseJson.user.id);", "});", "", "pm.test('Instagram social profile enriched', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.social_profiles).to.be.an('array');", "    pm.expect(responseJson.social_profiles.length).to.be.greaterThan(0);", "    ", "    const instagramProfile = responseJson.social_profiles.find(p => p.platform === 'instagram');", "    pm.expect(instagramProfile).to.not.be.undefined;", "    pm.expect(instagramProfile).to.have.property('followers_count');", "    pm.expect(instagramProfile).to.have.property('following_count');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/v1/oauth/callback?code={{mock_oauth_code}}&state={{oauth_state_ig}}&provider=instagram", "host": ["{{base_url}}"], "path": ["v1", "o<PERSON>h", "callback"], "query": [{"key": "code", "value": "{{mock_oauth_code}}"}, {"key": "state", "value": "{{oauth_state_ig}}"}, {"key": "provider", "value": "instagram"}]}}}, {"name": "4. Verify Instagram Social Profile Data", "event": [{"listen": "test", "script": {"exec": ["pm.test('Instagram profile data enriched', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('data');", "    pm.expect(responseJson.data.length).to.be.greaterThan(0);", "    ", "    const instagramProfile = responseJson.data[0];", "    pm.expect(instagramProfile.platform).to.eql('instagram');", "    pm.expect(instagramProfile.followers_count).to.be.a('number');", "    pm.expect(instagramProfile.following_count).to.be.a('number');", "    pm.expect(instagramProfile.posts_count).to.be.a('number');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"SELECT platform, platform_user_id, followers_count, following_count, posts_count, profile_data FROM social_profiles WHERE user_id = {{user_id_ig}} AND platform = 'instagram'\"\n}"}, "url": {"raw": "{{base_url}}/test/db-query", "host": ["{{base_url}}"], "path": ["test", "db-query"]}}}]}]}