from fastapi import APIRouter

from app.api.api_v1.endpoints import auth_brands, auth_influencer, common
from app.api.api_v1.endpoints.user_profile import user_profile_router
from app.api.api_v1.endpoints.youtube_management import youtube_router
from app.api.api_v1.endpoints.instagram_management import instagram_router

api_router = APIRouter()

# Authentication endpoints
# Influencer authentication endpoints
api_router.include_router(
    auth_influencer.influencer_router,
    prefix="/influencer/auth",
    tags=["influencer-auth"],
)

# Brand authentication endpoints
api_router.include_router(
    auth_brands.brand_router,
    prefix="/brand/auth",
    tags=["brand-auth"],
)

# Common endpoints (OAuth, login, logout, etc.)
api_router.include_router(common.common_router, tags=["common"])

# User Profile endpoints
api_router.include_router(
    user_profile_router,
    prefix="/user/profile",
    tags=["User Profile"],
)

# YouTube Management endpoints
api_router.include_router(
    youtube_router,
    prefix="/youtube",
    tags=["YouTube Management"],
)

# Instagram Management endpoints
api_router.include_router(
    instagram_router,
    prefix="/instagram",
    tags=["Instagram Management"],
)
