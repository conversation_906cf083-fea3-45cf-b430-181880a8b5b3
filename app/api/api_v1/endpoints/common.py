import json
import uuid
from datetime import UTC, datetime, timedelta
from typing import Any

import redis
from database_helper.database.models import UserSession
from database_helper.database.sync_db2 import SyncDatabaseDB
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy import update

from app.api.deps import get_current_user, BLOCKED_CONSUMER_DOMAINS
from app.core.config import get_database, get_locobuzz_redis, APP_CONFIG
from app.core.database_helper.users_helper import get_client_info
from app.services.centralized_otp_service import CentralizedOTPService, OTPType
from app.core.exceptions import (
    BusinessLogicError,
    ServiceUnavailableError
)
from app.core.redis_keys import RedisKeys, RedisConfig
from app.core.security import create_access_token, create_refresh_token
from app.schemas import ClientInfo
from app.schemas.auth import OAuthInitiateRequest, \
    OtpRequest, LoginRequest, LogoutRequest, CommonResendOtpRequest
from app.schemas.token import RefreshTokenData
from app.utilities.bloom_filter import get_bloom_filter_manager
from app.utilities.jwt_oauth_utilities import issue_tokens_for_user_with_uuid_role
from app.utilities.oauth_flow_manager import get_oauth_flow_manager
from app.utilities.oauth_service import get_oauth_service
from app.utilities.rbac_service import get_rbac_service
from app.utilities.response_handler import (
    create_master_data_response,
    create_logout_response,
    create_otp_success_response,
    create_auth_success_response,
    create_oauth_initiate_response
)
from app.utilities.validation_functions import check_email_exists_in_system

common_router = APIRouter()


def validate_brand_domain_for_login(email: str, role_uuid: str, redis_client) -> None:
    """Validate that email domain is not a consumer domain for brand roles"""
    # Get role name from cache or database to check if it's a brand role
    roles_cache = redis_client.hgetall(RedisKeys.rbac_roles())
    role_name = roles_cache.get(role_uuid, "").lower()

    # If it's a brand role, validate domain
    if 'brand' in role_name:
        domain = email.split('@')[-1].lower()
        if domain in BLOCKED_CONSUMER_DOMAINS:
            raise BusinessLogicError(
                f"Consumer domains ({domain}) are not allowed for brand login. Please use a business email address."
            )


@common_router.get(
    "/master/roles",
    status_code=status.HTTP_200_OK
)
async def get_master_roles(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
        Get master roles from Redis cache.
        If not found, fetch from database and cache it.
    """
    try:
        roles = redis_client.hgetall(RedisKeys.rbac_roles())
        if not roles:
            # Fetch from database if not in cache
            with db_conn.transaction() as session:
                from database_helper.database.models import MasterRole
                roles_query = session.query(MasterRole).all()
                roles = {str(role.id): role.role_name for role in roles_query}
                # Cache the roles in Redis
                if roles:
                    redis_client.hset(RedisKeys.rbac_roles(), mapping=roles)
                    redis_client.expire(RedisKeys.rbac_roles(), RedisConfig.RBAC_TTL)

        return create_master_data_response(
            data=roles,
            message="Master roles retrieved successfully"
        )
    except redis.RedisError as e:
        APP_CONFIG.logger.error(f"Redis error while fetching roles: {str(e)}")
        raise ServiceUnavailableError("Redis", "Unable to access roles data. Please try again later.")


@common_router.post(
    "/oauth/initiate",
    status_code=status.HTTP_200_OK,
)
async def initiate_oauth(
        request: OAuthInitiateRequest,
        redis_client=Depends(get_locobuzz_redis)
) -> dict[str, Any]:
    """
    Initiate OAuth flow for Google or Instagram.
    Returns (auth_url, state).
    Supports both influencer and brand user types.
    Checks if user is already registered and returns appropriate message.
    """
    try:

        # Validate provider
        provider = request.provider.lower()
        if provider not in ["google", "instagram", "facebook"]:  # can get from the config if needed
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported OAuth provider. Supported providers: google, instagram",
            )

        APP_CONFIG.logger.debug(
            f"Initiating OAuth flow for provider: {provider}, user_type: {request.role_uuid}"
        )

        # Use OAuthFlowManager for enhanced role_uuid support
        oauth_flow_manager = get_oauth_flow_manager(redis_client)

        # Use the role_uuid parameter if provided, otherwise user_type is used to look up a role
        auth_url, state = await oauth_flow_manager.initiate_oauth(
            provider=provider,
            user_role=request.role_uuid  # OAuthFlowManager will look up the appropriate role_uuid
        )

        APP_CONFIG.logger.info(f"OAuth URL generated successfully for provider: {provider}")

        return create_oauth_initiate_response(
            auth_url=auth_url,
            state=state,
            message="OAuth flow initiated"
        )

    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to initiate OAuth for provider {request.provider}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate OAuth flow"
        )


@common_router.get(
    "/oauth/callback",
    status_code=status.HTTP_200_OK,
)
async def oauth_callback(
        code: str = Query(...),
        state: str = Query(...),
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
) -> dict[str, Any]:
    """
    Handle OAuth callback after user authenticates at Google or Instagram.
    We only get `code` and `state` from Google/Instagram. The actual provider
    is deduced by popping the stored state data from Redis/in-memory.
    
    The stored data can be in format:
    - Legacy format: "provider:user_type"
    - Enhanced format: "provider:user_type:role_uuid"
    """
    oauth_service = get_oauth_service(redis_client)

    # 1) Pop stored state data from Redis/in-memory using state
    stored = await oauth_service.pop_oauth_state(state)
    if not stored:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired OAuth state.",
        )

    # 2) Parse state data
    # Format can be "provider:user_type" or "provider:user_type:role_uuid"
    parts = stored.split(":")
    if len(parts) < 2:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid OAuth state format.",
        )

    provider = parts[0]
    # User type is always the second part, regardless of format
    user_role = parts[1]  # This can be a UUID for influencer or brand

    try:
        if provider == "google":
            token_response = await oauth_service.handle_google_oauth_callback(
                code=code,
                state=state,
                redirect_uri=APP_CONFIG.oauth_google_redirect_uri,
                db_conn=db_conn,
                role_uuid=user_role,
            )
            
            # Handle YouTube channel selection if needed
            if hasattr(token_response, 'extra_data') and token_response.extra_data:
                if token_response.extra_data.get('needs_youtube_channel_selection'):
                    user_id = token_response.extra_data.get('user_id')
                    if user_id:
                        from fastapi.responses import RedirectResponse
                        redirect_url = f"/api/v1/youtube/redirect?user_id={user_id}"
                        return RedirectResponse(url=redirect_url, status_code=302)
            
            # Convert OAuthTokenResponse to dict required by the endpoint
            return {
                "access_token": token_response.access_token,
                "refresh_token": token_response.refresh_token,
                "token_type": token_response.token_type,
                "expires_in": token_response.expires_in,
                "message": token_response.message or "Authentication successful"
            }
        elif provider == "instagram":
            token_response = await oauth_service.handle_instagram_oauth_callback(
                code=code,
                state=state,
                redirect_uri=APP_CONFIG.oauth_instagram_redirect_uri,
                db_conn=db_conn,
                role_uuid=user_role,
            )
            return {
                "access_token": token_response.access_token,
                "refresh_token": token_response.refresh_token,
                "token_type": token_response.token_type,
                "expires_in": token_response.expires_in,
                "message": token_response.message or "Authentication successful"
            }
        elif provider == "facebook":
            token_response = await oauth_service.handle_facebook_oauth_callback(
                code=code,
                state=state,
                redirect_uri=APP_CONFIG.oauth_instagram_redirect_uri,
                db_conn=db_conn,
                role_uuid=user_role,
            )
            
            # Handle Instagram page selection if needed
            if hasattr(token_response, 'extra_data') and token_response.extra_data:
                if token_response.extra_data.get('needs_instagram_page_selection'):
                    user_id = token_response.extra_data.get('user_id')
                    if user_id:
                        from fastapi.responses import RedirectResponse
                        redirect_url = f"/api/v1/instagram/redirect?user_id={user_id}"
                        return RedirectResponse(url=redirect_url, status_code=302)
            
            # Convert OAuthTokenResponse to dict required by the endpoint
            return {
                "access_token": token_response.access_token,
                "refresh_token": token_response.refresh_token,
                "token_type": token_response.token_type,
                "expires_in": token_response.expires_in,
                "message": token_response.message or "Authentication successful"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported OAuth provider: {provider}",
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth authentication failed: {str(e)}",
        )


@common_router.post(
    "/auth/login/email", status_code=status.HTTP_200_OK
)
async def login(
        user_data: LoginRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
    Initiate login for existing user and send OTP for email verification.
    Supports both influencer and brand users with proper domain validation.
    """
    try:
        user_email: str = str(user_data.email).strip().lower()

        # Validate brand domain if it's a brand role (only UUID roles supported)
        if user_data.role_uuid:
            validate_brand_domain_for_login(user_email, str(user_data.role_uuid), redis_client)

        # 1. Check if email exists in the system using Bloom filter
        await check_email_exists_in_system(db_conn, redis_client, user_email)

        # 2. Check existing OTP status with 30-second resend logic
        otp_service = CentralizedOTPService(redis_client, db_conn)
        otp_status = otp_service._check_otp_resend_status(user_email, force_resend=False)
        
        if not otp_status["can_send"]:
            return create_otp_success_response(
                email=str(user_data.email),
                message=otp_status["message"]
            )

        # 3. Generate OTP, hash it, save in database and send OTP email
        # Only use role_uuid (no more integer role support)
        if not user_data.role_uuid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="role_uuid is required for login"
            )

        # Request new OTP using centralized service
        result = await otp_service.request_otp(
            user_email=user_email,
            otp_type=OTPType.LOGIN,
            role_uuid=str(user_data.role_uuid),
            register_source=str(user_data.login_source),
            register_source_name=str(user_data.login_source_name)
        )

        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send login OTP. Please try again."
            )

        APP_CONFIG.logger.info(f"Login OTP sent successfully for user: {user_email}")
        return create_otp_success_response(
            email=str(user_data.email),
            message="Login OTP sent. Please verify your email with the OTP sent."
        )

    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error during login OTP generation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed due to server error: {str(e)}",
        )


@common_router.post(
    "/auth/resend-login-otp",
    status_code=status.HTTP_200_OK
)
async def resend_login_otp(
        resend_data: CommonResendOtpRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
    Resend login OTP with 30-second cooldown.
    Works for both influencer and brand users with proper domain validation.
    """
    try:
        user_email: str = str(resend_data.email).strip().lower()

        # Validate brand domain if it's a brand role
        if resend_data.role_uuid:
            validate_brand_domain_for_login(user_email, str(resend_data.role_uuid), redis_client)

        # Check if email exists in the system using Bloom filter
        await check_email_exists_in_system(db_conn, redis_client, user_email)

        # Use centralized OTP service for resending login OTP
        otp_service = CentralizedOTPService(redis_client, db_conn)
        result = await otp_service.resend_otp(
            user_email=user_email,
            otp_type=OTPType.LOGIN,
            role_uuid=str(resend_data.role_uuid),
            register_source="web",
            register_source_name="website"
        )

        if not result.get("success"):
            if result.get("retry_after", 0) > 0:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=result.get("message", "Please wait before requesting a new OTP.")
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result.get("message", "Failed to resend OTP. Please try again.")
                )

        APP_CONFIG.logger.info(f"Login OTP resent successfully for user: {user_email}")
        return create_otp_success_response(
            email=resend_data.email,
            message=result.get("message", "OTP resent successfully. Please check your email.")
        )

    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error during login OTP resend: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resend login OTP: {str(e)}",
        )


@common_router.post(
    "/auth/verify-login-otp",
    status_code=status.HTTP_200_OK,
)
async def verify_login_otp_endpoint(
        user_data: OtpRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        client_info: ClientInfo = Depends(get_client_info)
) -> dict[str, Any]:
    """
    Verify the OTP for the user.
    This can be used for login verification for both influencers and brands.
    """
    try:
        user_email = str(user_data.email).strip().lower()

        # 1. Check if email exists in our system using bloom filter
        bloom_filter_manager = get_bloom_filter_manager(redis_client)
        if not bloom_filter_manager.check_email_exists(user_email):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Email not found in system. Please register first."
            )

        # 2. Verify the OTP using centralized service
        otp_service = CentralizedOTPService(redis_client, db_conn)
        verification_result = await otp_service.verify_otp(
            user_email=user_email,
            otp=str(user_data.otp)
        )

        if not verification_result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired OTP"
            )

        # 3. Issue tokens for the user using UUID role support only
        with db_conn.transaction() as session:
            # Only use role_uuid (no more integer role support)
            if not user_data.role_uuid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="role_uuid is required for login verification"
                )

            # Create or update user entry within session context
            # Get existing user data for context
            from database_helper.database.models import User
            user_obj = session.query(User).filter(User.email == user_email).first()
            
            if not user_obj:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found after OTP verification"
                )

            # Use UUID-based token issuance
            token_response, _ = issue_tokens_for_user_with_uuid_role(
                str(user_obj.id),
                str(user_data.role_uuid),
                redis_client,
                session,
                **client_info.model_dump()
            )        # 4. OTP cleanup is handled automatically by the verify_otp method
        # No additional cleanup needed

        APP_CONFIG.logger.info(f"Login OTP verified successfully for user: {user_email}")
        return {
            "access_token": token_response.access_token,
            "refresh_token": token_response.refresh_token,
            "token_type": token_response.token_type,
            "expires_in": token_response.expires_in,
            "message": "Login successful"
        }

    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error during login OTP verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed due to server error: {str(e)}",
        )


@common_router.post(
    "/logout",
    status_code=status.HTTP_200_OK,
)
async def logout(
        payload: LogoutRequest = Body(...),
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: dict[str, Any] = Depends(get_current_user)
) -> dict[str, str]:
    """
    Logout user by invalidating their refresh token.
    Protected route that requires valid JWT token.
    """
    ref_token = payload.refresh_token  # Validate that the user from token matches the logout request
    token_user_id = current_user.get("sub", "")  # JWT subject contains user_id
    if not token_user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid token: missing user identifier",
        )
    session_id = current_user.get("ssid")  # Session ID from JWT

    # 1) Build the Redis key for the refresh token
    redis_key = RedisKeys.refresh_token(token_user_id, ref_token)
    try:
        token_data = redis_client.hgetall(redis_key)
    except redis.RedisError:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Unable to access Redis. Please try again later.",
        )

    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired refresh token",
        )

    # 2) Extract user_id from token_data
    user_id = token_data.get("user_id")

    # Validate user_id from token matches refresh token data
    if user_id != token_user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Token user does not match refresh token user"
        )

    # 3) Use Redis pipeline to delete multiple keys atomically
    try:
        pipe = redis_client.pipeline()

        # Delete refresh token key
        pipe.delete(redis_key)

        # Delete session key if session_id exists
        if session_id:
            session_key = RedisKeys.session_key(user_id, session_id)
            pipe.delete(session_key)

        # Delete user's cached roles key
        if user_id:
            user_roles_key = RedisKeys.rbac_user_roles(str(user_id))
            pipe.delete(user_roles_key)

        # Execute all deletions in pipeline
        pipe.delete(RedisKeys.refresh_token(user_id, ref_token))
        pipe.execute()

        APP_CONFIG.logger.info(f"Redis keys deleted for user {user_id} during logout")

    except redis.RedisError as e:
        APP_CONFIG.logger.error(f"Failed to remove tokens from Redis for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Failed to remove tokens from Redis. Please try again later.",
        )

    # 4) Update database to mark session as revoked
    try:
        with db_conn.transaction() as session:
            update_stmt = update(UserSession).where(
                UserSession.refresh_token == ref_token,
                UserSession.user_id == user_id,
            ).values(is_revoked=True)

            result = session.execute(update_stmt)

            if result.rowcount == 0:
                APP_CONFIG.logger.warning(f"No session found to revoke for user {user_id}")
            else:
                APP_CONFIG.logger.info(f"Session revoked in database for user {user_id}")

    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to revoke session in database for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Unable to remove refresh token from database. Please try again later.",
        )

    return create_logout_response("Successfully logged out")


@common_router.post(
    "/token/refresh", status_code=status.HTTP_200_OK
)
async def refresh_token(
        ref_data: RefreshTokenData,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: dict[str, Any] = Depends(get_current_user)
) -> dict[str, Any]:
    """
    Refresh access token using refresh token with proper session management.
    Implements token rotation and cache invalidation following cache-aside pattern.
    """
    try:
        # 1. Find refresh token in database first (authoritative source)
        with db_conn.transaction() as session:
            token_value = ref_data.refresh_token  # Use a different variable name to avoid collision

            session_record = session.query(UserSession).filter(
                UserSession.refresh_token == token_value,
                UserSession.is_revoked.is_(False),
                UserSession.expires_at > datetime.now(UTC)
            ).first()

            if not session_record:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired refresh token",
                )

            user_id = str(session_record.user_id)
            session_id = str(session_record.id)

            # 2. Get user roles and permissions using RBAC service
            rbac_service = get_rbac_service(redis_client, db_conn)
            user_roles = rbac_service.get_user_roles(session_record.user_id)

            if not user_roles:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User has no assigned roles",
                )

            # Get permissions for primary role
            primary_role = list(user_roles)[0]
            role_id = None
            for rid, rname in redis_client.hgetall(RedisKeys.rbac_roles()).items():
                if rname == primary_role:
                    role_id = rid
                    break

            if role_id:
                permissions = rbac_service.get_role_permissions(role_id)
            else:
                permissions = set()

            # 3. Create new tokens
            now_utc = datetime.now(UTC)
            new_session_id = str(uuid.uuid4())

            payload = {
                "iss": "creator.verse",
                "sub": user_id,
                "ssid": new_session_id,
                "roles": list(user_roles),
                "permissions": list(permissions),
            }

            new_access_token = create_access_token(payload)
            new_refresh_token = create_refresh_token()
            new_expires_at = now_utc + timedelta(days=7)

            # 4. Update database session record
            session_record.id = new_session_id
            session_record.access_token = new_access_token
            session_record.refresh_token = new_refresh_token
            session_record.issued_at = now_utc
            session_record.expires_at = new_expires_at
            session_record.is_revoked = False

            # 5. Delete old Redis keys and create new ones using pipeline
            pipe = redis_client.pipeline()

            # Delete old keys
            old_session_key = RedisKeys.session_key(user_id, session_id)
            old_refresh_key = RedisKeys.refresh_token(user_id, token_value)  # Use the variable instead of function name
            pipe.delete(old_session_key)
            pipe.delete(old_refresh_key)

            # Create new session cache
            new_session_key = RedisKeys.session_key(user_id, new_session_id)
            session_data = {
                "user_id": user_id,
                "access_token": new_access_token,
                "refresh_token": new_refresh_token,
                "issued_at": str(int(now_utc.timestamp())),
                "expires_at": str(int(new_expires_at.timestamp())),
                "ip_address": session_record.ip_address or "",
                "user_agent": session_record.user_agent or "",
                "is_revoked": "0",
            }
            pipe.hset(new_session_key, mapping=session_data)
            pipe.expire(new_session_key, RedisConfig.REFRESH_TOKEN_TTL)

            # Create new refresh token cache
            new_refresh_key = RedisKeys.refresh_token(user_id, new_refresh_token)
            refresh_data = {
                "user_id": user_id,
                "roles": json.dumps(list(user_roles)),
                "permissions": json.dumps(list(permissions)),
                "session_id": new_session_id,
            }
            pipe.hset(new_refresh_key, mapping=refresh_data)
            pipe.expire(new_refresh_key, RedisConfig.REFRESH_TOKEN_TTL)

            # Execute all Redis operations atomically
            pipe.execute()

            APP_CONFIG.logger.info(f"Token refreshed successfully for user {user_id}")

            return create_auth_success_response(
                access_token=new_access_token,
                refresh_token=new_refresh_token,
                user_id=user_id,
                message="Token refreshed successfully"
            )

    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Token refresh failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed due to server error",
        )
