from typing import Dict, Any, List, Optional
from datetime import datetime, UTC
from urllib.parse import urlencode

from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.responses import RedirectResponse

from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis
from app.api.deps import get_current_user
from app.schemas.youtube import (
    YouTubeChannelListResponse,
    YouTubeChannelInfo,
    YouTubeChannelSelectRequest
)
from app.services.youtube_service import get_youtube_service
from app.utilities.response_handler import create_success_response
from database_helper.database.models import SocialProfile, OAuthAccount

youtube_router = APIRouter()


@youtube_router.get(
    "/channels",
    response_model=YouTubeChannelListResponse,
    status_code=status.HTTP_200_OK
)
async def list_youtube_channels(
    current_user: Dict[str, Any] = Depends(get_current_user),
    redis_client=Depends(get_locobuzz_redis),
    db_conn=Depends(get_database)
) -> Dict[str, Any]:
    """
    List all YouTube channels accessible to the current user.
    
    This endpoint retrieves all YouTube channels the authenticated user has access to,
    allowing them to choose which channel to use with CreatorVerse.
    """
    try:
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token"
            )

        # Get OAuth tokens from database
        tokens = await get_google_oauth_tokens(user_id, db_conn)

        if not tokens:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No Google OAuth tokens found. Please connect your Google account first."
            )

        # Fetch channels using YouTube service
        youtube_service = get_youtube_service(redis_client)
        channels = await youtube_service.list_user_channels(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            expires_at=tokens["expires_at"]
        )

        # Convert to response model
        channel_info = [YouTubeChannelInfo(**channel) for channel in channels]
        return {"channels": channel_info}

    except ImportError as e:
        APP_CONFIG.logger.error(f"YouTube service not available: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="YouTube integration is not available"
        )
    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error listing YouTube channels: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list YouTube channels: {str(e)}"
        )


@youtube_router.post(
    "/channels/select",
    status_code=status.HTTP_200_OK
)
async def select_youtube_channel(
    request: YouTubeChannelSelectRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    redis_client=Depends(get_locobuzz_redis),
    db_conn=Depends(get_database)
) -> Dict[str, Any]:
    """
    Select a specific YouTube channel for the user's profile.
    
    This endpoint allows users to choose which YouTube channel to use with
    CreatorVerse from their available channels.
    """
    try:
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token"
            )

        # Get OAuth tokens from database
        tokens = await get_google_oauth_tokens(user_id, db_conn)

        if not tokens:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No Google OAuth tokens found. Please connect your Google account first."
            )

        # Verify channel exists and is accessible to user
        youtube_service = get_youtube_service(redis_client)
        
        # First check if the channel is in the user's list of channels
        channels = await youtube_service.list_user_channels(
            access_token=tokens["access_token"],
            refresh_token=tokens["refresh_token"],
            expires_at=tokens["expires_at"]
        )

        selected_channel = next(
            (c for c in channels if c["channel_id"] == request.channel_id),
            None
        )

        if not selected_channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Selected channel not found or not accessible to you"
            )

        # Update social profile with selected channel info
        with db_conn.transaction() as session:
            # Get OAuth account ID for the user's Google account
            oauth_account = session.query(OAuthAccount).filter_by(
                user_id=user_id,
                provider="google"
            ).first()

            if not oauth_account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Google account not found for user"
                )

            # Check if YouTube social profile exists
            social_profile = session.query(SocialProfile).filter_by(
                oauth_account_id=oauth_account.id,
                service="youtube"
            ).first()

            if social_profile:
                # Update existing profile
                social_profile.external_id = selected_channel["channel_id"]
                social_profile.username = None
                social_profile.display_name = selected_channel["title"]
                social_profile.avatar_url = selected_channel["thumbnail_url"]
                social_profile.follower_count = selected_channel["subscriber_count"]
                social_profile.post_count = selected_channel["video_count"]
                social_profile.raw_json = selected_channel
                social_profile.fetched_at = datetime.now(UTC)
            else:
                # Create new profile
                social_profile = SocialProfile(
                    oauth_account_id=oauth_account.id,
                    service="youtube",
                    external_id=selected_channel["channel_id"],
                    username=None,
                    display_name=selected_channel["title"],
                    avatar_url=selected_channel["thumbnail_url"],
                    follower_count=selected_channel["subscriber_count"],
                    post_count=selected_channel["video_count"],
                    raw_json=selected_channel,
                    fetched_at=datetime.now(UTC)
                )
                session.add(social_profile)

        # Invalidate any relevant caches
        cache_key = f"CreatorVerse:user:profile_completion:{user_id}"
        try:
            redis_client.delete(cache_key)
        except Exception as cache_err:
            APP_CONFIG.logger.warning(f"Failed to invalidate cache: {str(cache_err)}")

        return create_success_response(
            message="YouTube channel selected successfully",
            data={
                "channel": selected_channel
            }
        )

    except ImportError as e:
        APP_CONFIG.logger.error(f"YouTube service not available: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="YouTube integration is not available"
        )
    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error selecting YouTube channel: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to select YouTube channel: {str(e)}"
        )


@youtube_router.get(
    "/redirect", 
    status_code=status.HTTP_302_FOUND
)
async def youtube_channel_selection_redirect(
    request: Request,
    user_id: str,
    original_redirect: Optional[str] = None,
    redis_client=Depends(get_locobuzz_redis),
) -> Response:
    """
    Redirect endpoint for YouTube channel selection after OAuth.
    
    This endpoint handles redirecting the user to the channel selection UI
    after completing Google OAuth with YouTube scope.
    """
    try:
        # Use the API base path
        base_path = "/api/v1"
        
        # Build redirect URL with user ID for frontend to handle
        params = {
            "user_id": user_id,
        }
        
        if original_redirect:
            params["redirect_after"] = original_redirect
            
        channel_selection_path = f"{base_path}/youtube/channels?{urlencode(params)}"
        
        APP_CONFIG.logger.info(f"Redirecting to YouTube channel selection: {channel_selection_path}")
        
        return RedirectResponse(url=channel_selection_path)
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Error during YouTube redirect: {str(e)}")
        # Fallback to API root if something goes wrong
        return RedirectResponse(url="/api/v1")


async def get_google_oauth_tokens(user_id: str, db_conn) -> Optional[Dict[str, Any]]:
    """
    Helper function to get Google OAuth tokens for the given user
    
    Returns dict with access_token, refresh_token, and expires_at
    or None if not found
    """
    with db_conn.transaction() as session:
        # Query for OAuth tokens
        oauth_account = session.query(OAuthAccount).filter_by(
            user_id=user_id,
            provider="google"
        ).first()
        
        if not oauth_account:
            return None
            
        return {
            "access_token": oauth_account.access_token,
            "refresh_token": oauth_account.refresh_token,
            "expires_at": oauth_account.expires_at or datetime.now(UTC)
        }
