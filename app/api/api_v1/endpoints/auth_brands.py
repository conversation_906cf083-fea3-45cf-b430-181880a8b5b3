import secrets
from datetime import datetime, UTC
from typing import Any, Dict, List, Optional
from uuid import UUID

from database_helper.database.models import User, Organization, Brand, BrandMembership
from database_helper.database.sync_db2 import SyncDatabaseDB
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy import select

from app.api.deps import get_current_user, BLOCKED_CONSUMER_DOMAINS
from app.core.config import get_database, get_locobuzz_redis, APP_CONFIG
from app.core.database_helper.users_helper import (
    create_brand_user_with_organization_handling,
    get_client_info,
    get_organization_members_cache_aside
)
from app.core.database_helper.organization_helper import (
    get_organization_brands_cache_aside,
    check_and_create_organization_for_user
)
from app.services.centralized_otp_service import CentralizedOTPService, OTPType
from app.core.redis_keys import RedisKeys
from app.schemas import ClientInfo
from app.schemas.auth_brand import (
    BrandRegisterRequest,
    BrandOtpRequest,
    BrandOtpResponse,
    BrandProfileResponse,
    OrganizationCreateRequest,
    OwnershipTransferRequest,
    BrandRegistrationResponse,
    BrandResendOtpRequest
)
from app.schemas.brand import BrandCreate, BrandResponse
from app.schemas.brand_join_schemas import (
    BrandJoinRequest,
    BrandJoinRequestResponse,
    BrandJoinRequestsResponse,
    JoinRequestAction,
    JoinRequestActionResponse,
    OrganizationBrandsResponse
)
from app.utilities.bloom_filter import get_bloom_filter_manager
from app.utilities.domain_service import get_domain_service
from app.utilities.response_handler import (
    create_success_response
)
from app.utilities.validation_functions import verify_email_exists_parallel

brand_router = APIRouter()


def validate_brand_domain(email: str) -> None:
    """Validate that email domain is not a consumer domain"""
    domain = email.split('@')[-1].lower()
    if domain in BLOCKED_CONSUMER_DOMAINS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Consumer domains ({domain}) are not allowed for brand registration"
        )


@brand_router.post(
    "/request-otp",
    response_model=BrandOtpResponse,
    status_code=status.HTTP_200_OK
)
async def request_brand_otp(
        user_data: BrandRegisterRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database)) -> BrandOtpResponse:
    """
    Request OTP for brand registration/login.
    Blocks consumer domains and checks existing users.
    """
    user_email: str = str(user_data.email).strip().lower()

    # Validate domain is not consumer domain
    validate_brand_domain(user_email)

    # Check if email is already in the system using Bloom filter with DB verification
    bloom_filter_manager = get_bloom_filter_manager(redis_client)
    if bloom_filter_manager.check_email_exists(user_email):
        # Bloom filter indicates email might exist - verify with database
        # to handle false positives
        try:
            with db_conn.transaction() as session:
                existing_user = session.query(User).filter(User.email == user_email).first()
                if existing_user:
                    # Email actually exists in database
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Email already exists in the system. Please use login instead."
                    )
                # False positive from Bloom filter - continue with registration
                APP_CONFIG.logger.info(f"Bloom filter false positive for email: {user_email}")
        except HTTPException:
            raise
        except Exception as e:
            APP_CONFIG.logger.error(f"Database verification error for {user_email}: {str(e)}")
            # If DB check fails, allow registration to proceed (fail-open approach)

    # Check existing OTP status with 30-second resend logic
    otp_service = CentralizedOTPService(redis_client, None)  # Don't need db_conn for OTP check
    otp_status = otp_service._check_otp_resend_status(user_email, force_resend=False)
    
    if not otp_status["can_send"]:
        return BrandOtpResponse(
            message=otp_status["message"],
            email=user_data.email,
        )

    # Verify email existence via MX lookup
    is_correct, reason = verify_email_exists_parallel(user_email)
    if not is_correct:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid email: {reason}",
        )

    # Use centralized OTP service for brand registration OTP
    result = await otp_service.request_otp(
        user_email=user_email,
        otp_type=OTPType.BRAND_REGISTRATION,
        role_uuid=str(user_data.role_uuid),
        register_source=str(user_data.register_source),
        register_source_name=str(user_data.register_source_name)
    )

    if not result.get("success"):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send brand registration OTP. Please try again."
        )

    APP_CONFIG.logger.info(f"Brand OTP sent successfully for email: {user_email}")
    return BrandOtpResponse(
        message="OTP sent successfully. Please verify your email with the OTP sent.",
        email=user_data.email,
    )


@brand_router.post(
    "/verify-otp",
    response_model=BrandRegistrationResponse,
    status_code=status.HTTP_200_OK,
)
async def verify_brand_otp(
        otp_data: BrandOtpRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        client_info: ClientInfo = Depends(get_client_info)
) -> BrandRegistrationResponse:
    """
    Verify OTP for brand registration and handle organization creation/joining.
    Implements first-ever domain signup → auto-claim & owner logic.
    
    When a user registers with a corporate email:
    1. If they're the first user from that domain, create an organization and make them the brand-admin
       - Organization is automatically created with domain as identifier
       - Domain is auto-verified for first user
       - First user gets brand-admin role
       - A default brand is created for the organization
    2. If they're not the first user, add them to the existing organization
       - User gets regular member role in the organization
       - No automatic brand membership (admin needs to add them)
       - Domain verification is maintained at organization level
    """
    user_email = str(otp_data.email).strip().lower()

    # Validate Redis client
    if redis_client is None:
        APP_CONFIG.logger.error("Redis client is None in verify_brand_otp")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Service temporarily unavailable"
        )

    try:
        # Verify OTP using centralized service
        otp_service = CentralizedOTPService(redis_client, db_conn)
        redis_data = await otp_service.verify_otp(
            user_email=user_email,
            otp=str(otp_data.otp)
        )

        if not redis_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired OTP"
            )

        # Prepare data for user creation
        user_creation_data = {
            **client_info.model_dump(),
            **{k: v for k, v in redis_data.items() if k not in ['email', 'role']},
        }

        # Create user with organization handling - properly await async function
        response = await create_brand_user_with_organization_handling(
            db_conn, user_email, str(otp_data.role_uuid), redis_client, **user_creation_data
        )

        # OTP cleanup is handled automatically by the verify_otp method
        # No additional cleanup needed

        # Add to bloom filter with Redis client validation
        try:
            if redis_client:
                bloom_filter_manager = get_bloom_filter_manager(redis_client)
                bloom_filter_manager.add_email(user_email)
        except Exception as bloom_error:
            APP_CONFIG.logger.warning(f"Failed to add email to bloom filter: {str(bloom_error)}")

        # Send welcome email (async, non-blocking)
        try:
            from app.utilities.welcome_email_service import get_welcome_email_service
            welcome_service = get_welcome_email_service(redis_client)
            
            # Send welcome email for brand user
            welcome_result = await welcome_service.send_welcome_email_if_not_sent(
                email=user_email,
                user_role="brand"
            )
            
            if welcome_result.get("success"):
                APP_CONFIG.logger.info(f"Brand welcome email processing completed for {user_email}")
            else:
                APP_CONFIG.logger.warning(f"Brand welcome email failed for {user_email}: {welcome_result.get('message')}")
                
        except Exception as welcome_error:
            # Welcome email failure should not block registration
            APP_CONFIG.logger.error(f"Brand welcome email error for {user_email}: {str(welcome_error)}")

        APP_CONFIG.logger.info(f"Brand user created successfully: {user_email}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        from app.core.exceptions import OTPError, EmailAlreadyExistsError, EmailNotFoundError
        
        # Handle specific OTP-related errors
        if isinstance(e, OTPError):
            APP_CONFIG.logger.warning(f"OTP verification failed for {user_email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        
        # Handle email-related errors
        if isinstance(e, (EmailAlreadyExistsError, EmailNotFoundError)):
            APP_CONFIG.logger.warning(f"Email validation error for {user_email}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )
        
        # Generic error handling
        APP_CONFIG.logger.error(f"Error during brand OTP verification for {user_email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed due to internal error",
        )


@brand_router.post(
    "/create-brand",
    status_code=status.HTTP_201_CREATED
)
async def create_new_brand(
        brand_data: BrandCreate,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Create a new brand for the current user.
    Only the 'name' field is required, all other fields are optional.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        # Import the brand_helper utility
        from app.core.database_helper.brand_helper import create_brand_for_user
        
        # Convert Pydantic model to dict ensuring proper field mapping
        brand_dict = brand_data.model_dump(exclude_unset=True)
        
        # Validate required name field is present
        if not brand_dict.get("name"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Brand name is required"
            )
        
        brand = await create_brand_for_user(
            user_id=user_id,
            brand_data=brand_dict,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        APP_CONFIG.logger.info(f"Created brand '{brand['name']}' for user {user_id}")
        
        return create_success_response(
            message="Brand created successfully",
            data=brand
        )
    
    except ValueError as e:
        APP_CONFIG.logger.warning(f"Brand creation validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to create brand: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create brand"
        )


@brand_router.get(
    "/my-brands",
    status_code=status.HTTP_200_OK
)
async def get_my_brands(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all brands accessible to the current user.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        # Get user's brand memberships
        from app.core.database_helper.brand_helper import get_user_brands_cache_aside
        my_brands = await get_user_brands_cache_aside(
            user_id=user_id,
            include_inactive=False,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Brands retrieved successfully",
            data={
                "brands": my_brands,
                "total_count": len(my_brands)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get user's brands: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve brand information"
        )


@brand_router.get(
    "/brand/{brand_id}",
    status_code=status.HTTP_200_OK
)
async def get_brand_details(
        brand_id: UUID,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get detailed information about a specific brand.
    User must be a member of the brand.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        # Try to get from cache first
        cache_key = f"CreatorVerse:brand:details:{brand_id}"
        cached_data = None
        
        try:
            cached_json = redis_client.get(cache_key)
            if cached_json:
                import json
                cached_data = json.loads(cached_json)
                
                # Verify the user has access to this cached brand
                with db_conn.transaction() as check_session:
                    brand_role = check_session.query(BrandMembership).filter(
                        BrandMembership.brand_id == brand_id,
                        BrandMembership.user_id == UUID(user_id),
                        BrandMembership.status == "active"
                    ).first()
                    
                    if brand_role:
                        cached_data["user_role"] = brand_role.role
                        return create_success_response(
                            message="Brand details retrieved from cache",
                            data=cached_data
                        )
        except Exception as e:
            APP_CONFIG.logger.warning(f"Cache error in get_brand_details: {str(e)}")
        
        # If no cache hit or user doesn't have access, query DB
        with db_conn.transaction() as session:
            # Get the brand
            brand = session.get(Brand, brand_id)
            if not brand:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Brand not found"
                )
            
            # Check if user has a direct role in the brand
            brand_membership = session.query(BrandMembership).filter(
                BrandMembership.brand_id == brand_id,
                BrandMembership.user_id == user_id,
                BrandMembership.status == "active"
            ).first()
            
            if not brand_membership:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You must be a member of the brand to view brand details"
                )
            
            # Get brand members
            brand_members = session.query(
                User.id, 
                User.email,
                BrandMembership.role
            ).join(
                BrandMembership,
                User.id == BrandMembership.user_id
            ).filter(
                BrandMembership.brand_id == brand_id,
                BrandMembership.status == "active"
            ).all()
            
            members_data = [
                {
                    "user_id": str(member.id),
                    "email": member.email,
                    "role": member.role
                }
                for member in brand_members
            ]
            
            # Prepare response
            brand_data = {
                "id": str(brand.id),
                "name": brand.name,
                "description": brand.description,
                "logo_url": brand.logo_url,
                "website_url": brand.website_url,
                "contact_email": brand.contact_email,
                "is_active": brand.is_active,
                "created_at": str(brand.created_at),
                "user_role": brand_membership.role,
                "members": members_data
            }
            
            # Cache the result
            try:
                # Create a clean copy of brand data for caching
                cache_data = {**brand_data}
                cache_data.pop("user_role", None)
                
                import json
                redis_client.setex(cache_key, 1800, json.dumps(cache_data))
            except Exception as e:
                APP_CONFIG.logger.warning(f"Cache set error in get_brand_details: {str(e)}")
            
            return create_success_response(
                message="Brand details retrieved successfully",
                data=brand_data
            )
    
    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error retrieving brand details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve brand details"
        )


@brand_router.get(
    "/organization/brands",
    status_code=status.HTTP_200_OK
)
async def get_organization_brands(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all brands in the user's organization with member information.
    Shows member count, icons, and current user's role in each brand.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.brand_join_requests_helper import get_organization_brands_with_members_cache_aside
        
        brands_data = await get_organization_brands_with_members_cache_aside(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Organization brands retrieved successfully",
            data=brands_data
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get organization brands: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization brands"
        )


@brand_router.post(
    "/join-request",
    status_code=status.HTTP_201_CREATED
)
async def request_join_brand(
        join_request: BrandJoinRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Request to join a specific brand.
    Creates a pending request that brand admins can approve or reject.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.brand_join_requests_helper import request_to_join_brand
        
        result = await request_to_join_brand(
            brand_id=str(join_request.brand_id),
            user_id=user_id,
            message=join_request.message,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Brand join request submitted successfully",
            data=result
        )
    
    except ValueError as e:
        APP_CONFIG.logger.warning(f"Brand join request validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to create brand join request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit join request"
        )


@brand_router.get(
    "/brand/{brand_id}/join-requests",
    status_code=status.HTTP_200_OK
)
async def get_brand_join_requests(
        brand_id: UUID,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get pending join requests for a brand.
    Only brand admins and organization admins can view this.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        # Verify user has permission to view join requests
        with db_conn.transaction() as session:
            brand_membership = session.query(BrandMembership).filter(
                BrandMembership.brand_id == brand_id,
                BrandMembership.user_id == UUID(user_id),
                BrandMembership.status == "active",
                BrandMembership.role.in_(["brand_admin", "admin"])
            ).first()
            
            if not brand_membership:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions to view join requests"
                )
            
            # Get brand info
            brand = session.get(Brand, brand_id)
            if not brand:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Brand not found"
                )
            
            # Get join requests
            join_requests = session.query(
                User.id,
                User.email,
                BrandMembership.role,
                BrandMembership.status
            ).join(
                BrandMembership,
                User.id == BrandMembership.user_id
            ).filter(
                BrandMembership.brand_id == brand_id,
                BrandMembership.status.in_(["pending", "accepted"])
            ).all()
            
            requests_data = [
                {
                    "user_id": str(request.id),
                    "email": request.email,
                    "role": request.role,
                    "status": request.status
                }
                for request in join_requests
            ]
            
            return create_success_response(
                message="Join requests retrieved successfully",
                data={
                    "brand_id": str(brand_id),
                    "join_requests": requests_data,
                    "total_count": len(requests_data)
                }
            )
    
    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error retrieving join requests for brand {brand_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve join requests"
        )


@brand_router.post(
    "/brand/{brand_id}/join-request/{request_id}/action",
    status_code=status.HTTP_200_OK
)
async def respond_to_join_request(
        brand_id: UUID,
        request_id: UUID,
        action_data: JoinRequestAction,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Approve or reject a join request for a brand.
    Only brand admins and organization admins can perform this action.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.brand_join_requests_helper import (
            approve_join_request,
            reject_join_request
        )
        
        # Check action type
        if action_data.action_type == "approve":
            result = await approve_join_request(
                request_id=request_id,
                brand_id=brand_id,
                redis_client=redis_client,
                db_conn=db_conn
            )
        elif action_data.action_type == "reject":
            result = await reject_join_request(
                request_id=request_id,
                brand_id=brand_id,
                redis_client=redis_client,
                db_conn=db_conn
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid action type"
            )
        
        return create_success_response(
            message="Join request updated successfully",
            data=result
        )
    
    except ValueError as e:
        APP_CONFIG.logger.warning(f"Join request action validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to update join request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process join request"
        )


@brand_router.get(
    "/join-requests/me",
    status_code=status.HTTP_200_OK
)
async def get_my_join_requests(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all join requests made by the current user.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.brand_join_requests_helper import get_user_join_requests_cache_aside
        
        requests_data = await get_user_join_requests_cache_aside(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Join requests retrieved successfully",
            data={
                "join_requests": requests_data,
                "total_count": len(requests_data)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get user's join requests: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve join requests"
        )


@brand_router.get(
    "/brands/search",
    status_code=status.HTTP_200_OK
)
async def search_brands(
        query: str = Query(..., min_length=3, max_length=100),
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Search for brands by name or domain.
    Supports partial matches and wildcards.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.brand_helper import search_brands_by_name_or_domain
        
        # Sanitize and prepare query
        sanitized_query = query.strip().lower()
        
        # Perform search
        search_results = await search_brands_by_name_or_domain(
            search_query=sanitized_query,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Brand search completed",
            data={
                "brands": search_results,
                "total_count": len(search_results)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Brand search error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform brand search"
        )


@brand_router.get(
    "/brand/{brand_id}/members",
    status_code=status.HTTP_200_OK
)
async def get_brand_members(
        brand_id: UUID,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all active members of a brand.
    Includes member roles and status.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.brand_helper import get_brand_members_with_roles
        
        members_data = await get_brand_members_with_roles(
            brand_id=brand_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Brand members retrieved successfully",
            data={
                "members": members_data,
                "total_count": len(members_data)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get brand members: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve brand members"
        )


@brand_router.get(
    "/organizations/search",
    status_code=status.HTTP_200_OK
)
async def search_organizations(
        query: str = Query(..., min_length=3, max_length=100),
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Search for organizations by name or domain.
    Supports partial matches and wildcards.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.organization_helper import search_organizations_by_name_or_domain
        
        # Sanitize and prepare query
        sanitized_query = query.strip().lower()
        
        # Perform search
        search_results = await search_organizations_by_name_or_domain(
            search_query=sanitized_query,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Organization search completed",
            data={
                "organizations": search_results,
                "total_count": len(search_results)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Organization search error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform organization search"
        )


@brand_router.get(
    "/user/brands",
    status_code=status.HTTP_200_OK
)
async def get_user_brands(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all brands associated with the current user.
    Includes brands where the user is an admin or a member.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.brand_helper import get_all_brands_for_user
        
        brands_data = await get_all_brands_for_user(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="User brands retrieved successfully",
            data={
                "brands": brands_data,
                "total_count": len(brands_data)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get user's brands: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user brands"
        )


@brand_router.get(
    "/user/organizations",
    status_code=status.HTTP_200_OK
)
async def get_user_organizations(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all organizations associated with the current user.
    Includes organizations where the user is an admin or a member.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.organization_helper import get_all_organizations_for_user
        
        organizations_data = await get_all_organizations_for_user(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="User organizations retrieved successfully",
            data={
                "organizations": organizations_data,
                "total_count": len(organizations_data)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get user's organizations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user organizations"
        )


@brand_router.get(
    "/user/roles",
    status_code=status.HTTP_200_OK
)
async def get_user_roles(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all roles assigned to the current user across brands and organizations.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.role_helper import get_roles_for_user
        
        roles_data = await get_roles_for_user(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="User roles retrieved successfully",
            data={
                "roles": roles_data,
                "total_count": len(roles_data)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get user's roles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user roles"
        )


@brand_router.get(
    "/user/permissions",
    status_code=status.HTTP_200_OK
)
async def get_user_permissions(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get all permissions assigned to the current user.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.permission_helper import get_permissions_for_user
        
        permissions_data = await get_permissions_for_user(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="User permissions retrieved successfully",
            data={
                "permissions": permissions_data,
                "total_count": len(permissions_data)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get user's permissions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user permissions"
        )


@brand_router.get(
    "/settings/brand",
    status_code=status.HTTP_200_OK
)
async def get_brand_settings(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get brand-specific settings for the current user.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.settings_helper import get_brand_settings_for_user
        
        settings_data = await get_brand_settings_for_user(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Brand settings retrieved successfully",
            data=settings_data
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get brand settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve brand settings"
        )


@brand_router.get(
    "/settings/organization",
    status_code=status.HTTP_200_OK
)
async def get_organization_settings(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get organization-specific settings for the current user.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.settings_helper import get_organization_settings_for_user
        
        settings_data = await get_organization_settings_for_user(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        return create_success_response(
            message="Organization settings retrieved successfully",
            data=settings_data
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get organization settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organization settings"
        )


@brand_router.get(
    "/activity-logs",
    status_code=status.HTTP_200_OK
)
async def get_activity_logs(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user),
        limit: int = Query(50, ge=1, le=100),
        offset: int = Query(0, ge=0)
) -> Dict[str, Any]:
    """
    Get activity logs for the current user.
    Supports pagination with limit and offset.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.activity_log_helper import get_activity_logs_for_user
        
        logs_data = await get_activity_logs_for_user(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn,
            limit=limit,
            offset=offset
        )
        
        return create_success_response(
            message="Activity logs retrieved successfully",
            data={
                "logs": logs_data,
                "total_count": len(logs_data)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get activity logs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve activity logs"
        )


@brand_router.get(
    "/notifications",
    status_code=status.HTTP_200_OK
)
async def get_notifications(
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        current_user: Dict[str, Any] = Depends(get_current_user),
        read_status: Optional[bool] = Query(None),
        limit: int = Query(50, ge=1, le=100),
        offset: int = Query(0, ge=0)
) -> Dict[str, Any]:
    """
    Get notifications for the current user.
    Supports filtering by read status and pagination with limit and offset.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user session"
        )
    
    try:
        from app.core.database_helper.notification_helper import get_notifications_for_user
        
        notifications_data = await get_notifications_for_user(
            user_id=user_id,
            redis_client=redis_client,
            db_conn=db_conn,
            read_status=read_status,
            limit=limit,
            offset=offset
        )
        
        return create_success_response(
            message="Notifications retrieved successfully",
            data={
                "notifications": notifications_data,
                "total_count": len(notifications_data)
            }
        )
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to get notifications: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve notifications"
        )


@brand_router.get(
    "/health",
    status_code=status.HTTP_200_OK
)
async def health_check() -> Dict[str, str]:
    """
    Health check endpoint to verify if the service is running.
    """
    return {"status": "ok"}
