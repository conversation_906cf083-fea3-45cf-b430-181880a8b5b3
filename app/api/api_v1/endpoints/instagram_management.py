from typing import Dict, Any, List, Optional
from datetime import datetime, UTC
from urllib.parse import urlencode

from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.responses import RedirectResponse

from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis
from app.api.deps import get_current_user
from app.schemas.instagram import (
    InstagramPageListResponse,
    InstagramPageInfo,
    InstagramPageSelectRequest
)
from app.services.instagram_service import get_instagram_service
from app.utilities.response_handler import create_success_response
from database_helper.database.models import SocialProfile, OAuthAccount

instagram_router = APIRouter()


@instagram_router.get(
    "/pages",
    response_model=InstagramPageListResponse,
    status_code=status.HTTP_200_OK
)
async def list_instagram_pages(
    current_user: Dict[str, Any] = Depends(get_current_user),
    redis_client=Depends(get_locobuzz_redis),
    db_conn=Depends(get_database)
) -> Dict[str, Any]:
    """
    List all Instagram pages accessible to the current user.
    
    This endpoint retrieves all Instagram pages the authenticated user has access to
    through their connected Facebook pages, allowing them to choose which page 
    to use with CreatorVerse.
    """
    try:
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token"
            )

        # Get OAuth tokens from database
        tokens = await get_facebook_oauth_tokens(user_id, db_conn)

        if not tokens:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No Facebook OAuth tokens found. Please connect your Facebook account first."
            )

        # Fetch Instagram pages using Instagram service
        instagram_service = get_instagram_service(redis_client)
        pages = await instagram_service.list_user_instagram_pages(
            access_token=tokens["access_token"],
            user_id=tokens["provider_user_id"]
        )

        # Convert to response model
        page_info = [InstagramPageInfo(**page) for page in pages]
        return {"pages": page_info}

    except ImportError as e:
        APP_CONFIG.logger.error(f"Instagram service not available: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Instagram integration is not available"
        )
    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error listing Instagram pages: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list Instagram pages: {str(e)}"
        )


@instagram_router.post(
    "/pages/select",
    status_code=status.HTTP_200_OK
)
async def select_instagram_page(
    request: InstagramPageSelectRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
    redis_client=Depends(get_locobuzz_redis),
    db_conn=Depends(get_database)
) -> Dict[str, Any]:
    """
    Select a specific Instagram page for the user's profile.
    
    This endpoint allows users to choose which Instagram page to use with
    CreatorVerse from their available pages.
    """
    try:
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token"
            )

        # Get OAuth tokens from database
        tokens = await get_facebook_oauth_tokens(user_id, db_conn)

        if not tokens:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No Facebook OAuth tokens found. Please connect your Facebook account first."
            )

        # Verify page exists and is accessible to user
        instagram_service = get_instagram_service(redis_client)
        
        # First check if the page is in the user's list of pages
        pages = await instagram_service.list_user_instagram_pages(
            access_token=tokens["access_token"],
            user_id=tokens["provider_user_id"]
        )

        selected_page = next(
            (p for p in pages if p["instagram_id"] == request.instagram_id),
            None
        )

        if not selected_page:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Selected Instagram page not found or not accessible to you"
            )

        # Update social profile with selected page info
        with db_conn.transaction() as session:
            # Get OAuth account ID for the user's Facebook account
            oauth_account = session.query(OAuthAccount).filter_by(
                user_id=user_id,
                provider="facebook"
            ).first()

            if not oauth_account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Facebook account not found for user"
                )

            # Check if Instagram social profile exists
            social_profile = session.query(SocialProfile).filter_by(
                oauth_account_id=oauth_account.id,
                service="instagram"
            ).first()

            if social_profile:
                # Update existing profile
                social_profile.external_id = selected_page["instagram_id"]
                social_profile.username = selected_page["username"]
                social_profile.display_name = selected_page["name"]
                social_profile.avatar_url = selected_page["profile_picture_url"]
                social_profile.follower_count = selected_page["followers_count"]
                social_profile.post_count = selected_page["media_count"]
                social_profile.raw_json = selected_page
                social_profile.fetched_at = datetime.now(UTC)
            else:
                # Create new profile
                social_profile = SocialProfile(
                    oauth_account_id=oauth_account.id,
                    service="instagram",
                    external_id=selected_page["instagram_id"],
                    username=selected_page["username"],
                    display_name=selected_page["name"],
                    avatar_url=selected_page["profile_picture_url"],
                    follower_count=selected_page["followers_count"],
                    post_count=selected_page["media_count"],
                    raw_json=selected_page,
                    fetched_at=datetime.now(UTC)
                )
                session.add(social_profile)

        # Invalidate any relevant caches
        cache_key = f"CreatorVerse:user:profile_completion:{user_id}"
        try:
            redis_client.delete(cache_key)
        except Exception as cache_err:
            APP_CONFIG.logger.warning(f"Failed to invalidate cache: {str(cache_err)}")

        return create_success_response(
            message="Instagram page selected successfully",
            data={
                "page": selected_page
            }
        )

    except ImportError as e:
        APP_CONFIG.logger.error(f"Instagram service not available: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Instagram integration is not available"
        )
    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error selecting Instagram page: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to select Instagram page: {str(e)}"
        )


@instagram_router.get(
    "/redirect", 
    status_code=status.HTTP_302_FOUND
)
async def instagram_page_selection_redirect(
    request: Request,
    user_id: str,
    original_redirect: Optional[str] = None,
    redis_client=Depends(get_locobuzz_redis),
) -> Response:
    """
    Redirect endpoint for Instagram page selection after OAuth.
    
    This endpoint handles redirecting the user to the page selection UI
    after completing Facebook OAuth with Instagram page access.
    """
    try:
        # Use the API base path
        base_path = "/api/v1"
        
        # Build redirect URL with user ID for frontend to handle
        params = {
            "user_id": user_id,
        }
        
        if original_redirect:
            params["redirect_after"] = original_redirect
            
        page_selection_path = f"{base_path}/instagram/pages?{urlencode(params)}"
        
        APP_CONFIG.logger.info(f"Redirecting to Instagram page selection: {page_selection_path}")
        
        return RedirectResponse(url=page_selection_path)
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Error during Instagram redirect: {str(e)}")
        # Fallback to API root if something goes wrong
        return RedirectResponse(url="/api/v1")


async def get_facebook_oauth_tokens(user_id: str, db_conn) -> Optional[Dict[str, Any]]:
    """
    Helper function to get Facebook OAuth tokens for the given user
    
    Returns dict with access_token, refresh_token, expires_at, and provider_user_id
    or None if not found
    """
    with db_conn.transaction() as session:
        # Query for OAuth tokens
        oauth_account = session.query(OAuthAccount).filter_by(
            user_id=user_id,
            provider="facebook"
        ).first()
        
        if not oauth_account:
            return None
            
        return {
            "access_token": oauth_account.access_token,
            "refresh_token": oauth_account.refresh_token,
            "expires_at": oauth_account.expires_at or datetime.now(UTC),
            "provider_user_id": oauth_account.provider_user_id
        }
