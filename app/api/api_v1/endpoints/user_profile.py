from datetime import datetime, UTC
from typing import Any, Dict, Optional

from database_helper.database.models import User
from database_helper.database.sync_db2 import SyncDatabaseDB
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, EmailStr, Field

from app.api.deps import get_current_user
from app.core.config import get_database, get_locobuzz_redis, APP_CONFIG
from app.core.redis_keys import RedisKeys, RedisConfig
from app.schemas.user_profile import ProfileCompletionResponse, ProfileCompletionItem
from app.utilities.response_handler import create_success_response

user_profile_router = APIRouter()


class UserProfileUpdate(BaseModel):
    """Schema for user profile update request"""
    name: Optional[str] = Field(None, description="User's display name")
    phone_number: Optional[str] = Field(None, description="User's phone number")
    profile_image: Optional[str] = Field(None, description="URL to user's profile image")
    bio: Optional[str] = Field(None, description="User's short biography")
    location: Optional[str] = Field(None, description="User's location")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional profile metadata")


class UserProfileResponse(BaseModel):
    """Schema for user profile response"""
    id: str
    email: EmailStr
    name: Optional[str] = None
    phone_number: Optional[str] = None
    profile_image: Optional[str] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    website: Optional[str] = None
    status: str
    is_active: bool
    is_email_verified: bool
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True


@user_profile_router.get(
    "/me",
    status_code=status.HTTP_200_OK
)
async def get_my_profile(
    current_user: Dict[str, Any] = Depends(get_current_user),
    redis_client=Depends(get_locobuzz_redis),
    db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
    Get current user's profile information using cache-aside pattern.
    Works for both influencers and brands.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    # Try to get from cache first
    cache_key = f"CreatorVerse:user:profile_data:{user_id}"
    try:
        cached_profile = redis_client.hgetall(cache_key)
        if cached_profile and "id" in cached_profile:
            # Convert is_active and is_email_verified to bool
            cached_profile["is_active"] = cached_profile.get("is_active", "False") == "True"
            cached_profile["is_email_verified"] = cached_profile.get("is_email_verified", "False") == "True"
            
            # Convert metadata from string to dict if exists
            if "metadata" in cached_profile and cached_profile["metadata"]:
                import json
                try:
                    cached_profile["metadata"] = json.loads(cached_profile["metadata"])
                except json.JSONDecodeError:
                    cached_profile["metadata"] = {}
            
            return create_success_response(
                message="Profile retrieved successfully",
                data=UserProfileResponse(**cached_profile)
            )
    except Exception as e:
        APP_CONFIG.logger.warning(f"Cache retrieval error in get_my_profile: {str(e)}")
    
    # If not in cache, get from database
    with db_conn.transaction() as session:
        user = session.query(User).filter_by(id=user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Create response data
        response_data = {
            "id": str(user.id),
            "email": user.email,
            "name": user.name,
            "phone_number": user.phone_number,
            "profile_image": user.profile_image,
            "bio": getattr(user, 'bio', None),
            "location": getattr(user, 'location', None),
            "website": getattr(user, 'website', None),
            "status": user.status,
            "is_active": bool(user.is_active),
            "is_email_verified": bool(user.is_email_verified),
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            "metadata": user.metadata_json if hasattr(user, 'metadata_json') else {}
        }
        
        # Cache the profile data
        try:
            cache_data = {k: str(v) if v is not None else "" for k, v in response_data.items()}
            # Handle metadata separately for proper JSON conversion
            if response_data.get("metadata"):
                import json
                cache_data["metadata"] = json.dumps(response_data["metadata"])
            redis_client.hset(cache_key, mapping=cache_data)
            redis_client.expire(cache_key, RedisConfig.USER_PROFILE_TTL)
        except Exception as e:
            APP_CONFIG.logger.warning(f"Cache storage error in get_my_profile: {str(e)}")
        
        return create_success_response(
            message="Profile retrieved successfully",
            data=UserProfileResponse(**response_data)
        )


@user_profile_router.patch(
    "/me",
    status_code=status.HTTP_200_OK
)
async def update_my_profile(
    profile_data: UserProfileUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user),
    redis_client=Depends(get_locobuzz_redis),
    db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
    Update current user's profile information.
    Supports partial updates and works for both influencers and brands.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    # Filter out None values from the update data
    update_data = {k: v for k, v in profile_data.dict().items() if v is not None}
    
    if not update_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No valid update data provided"
        )
    
    # Add updated_at timestamp
    update_data["updated_at"] = datetime.now(UTC)
    
    # Special handling for metadata - merge instead of replace
    if "metadata" in update_data:
        APP_CONFIG.logger.info(f"Updating metadata for user {user_id}")
    
    try:
        with db_conn.transaction() as session:
            user = session.query(User).filter_by(id=user_id).first()
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Special handling for metadata - merge with existing metadata
            if "metadata" in update_data and hasattr(user, 'metadata_json'):
                existing_metadata = user.metadata_json or {}
                merged_metadata = {**existing_metadata, **update_data.pop("metadata")}
                user.metadata_json = merged_metadata
            
            # Update other fields
            for field, value in update_data.items():
                if hasattr(user, field):
                    setattr(user, field, value)
                else:
                    APP_CONFIG.logger.warning(f"Field '{field}' not found in User model, skipping")
            
            session.add(user)
            session.flush()
            
            # Invalidate caches
            cache_keys = [
                f"CreatorVerse:user:profile_data:{user_id}",
                RedisKeys.user_by_email(user.email)
            ]
            
            for key in cache_keys:
                try:
                    redis_client.delete(key)
                except Exception as e:
                    APP_CONFIG.logger.warning(f"Cache invalidation error for key {key}: {str(e)}")
            
            # Also invalidate profile completion cache
            invalidate_profile_completion_cache(user_id, redis_client)
            
            # Build response with all user data
            response_data = {
                "id": str(user.id),
                "email": user.email,
                "name": user.name,
                "phone_number": user.phone_number,
                "profile_image": user.profile_image,
                "bio": getattr(user, 'bio', None),
                "location": getattr(user, 'location', None),
                "website": getattr(user, 'website', None),
                "status": user.status,
                "is_active": bool(user.is_active),
                "is_email_verified": bool(user.is_email_verified),
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
                "metadata": user.metadata_json if hasattr(user, 'metadata_json') else {}
            }
            
            APP_CONFIG.logger.info(f"User profile updated successfully: {user_id}")
            return create_success_response(
                message="Profile updated successfully",
                data=UserProfileResponse(**response_data)
            )
            
    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error updating user profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile due to server error"
        )


@user_profile_router.patch(
    "/verify-phone",
    status_code=status.HTTP_200_OK
)
async def verify_phone_number(
    phone_number: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    redis_client=Depends(get_locobuzz_redis),
    db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
    Verify user's phone number after OTP verification.
    This endpoint would typically be called after a successful phone OTP verification.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    try:
        with db_conn.transaction() as session:
            user = session.query(User).filter_by(id=user_id).first()
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Update phone number and verification status
            user.phone_number = phone_number
            user.is_phone_verified = True
            user.phone_verified_at = datetime.now(UTC)
            user.updated_at = datetime.now(UTC)
            
            session.add(user)
            session.flush()
            
            # Invalidate caches
            cache_keys = [
                f"CreatorVerse:user:profile_data:{user_id}",
                RedisKeys.user_by_email(user.email)
            ]
            
            for key in cache_keys:
                try:
                    redis_client.delete(key)
                except Exception as e:
                    APP_CONFIG.logger.warning(f"Cache invalidation error for key {key}: {str(e)}")
            
            # Also invalidate profile completion cache
            invalidate_profile_completion_cache(user_id, redis_client)
            
            # Build response with all user data
            response_data = {
                "id": str(user.id),
                "email": user.email,
                "name": user.name,
                "phone_number": user.phone_number,
                "profile_image": user.profile_image,
                "bio": getattr(user, 'bio', None),
                "location": getattr(user, 'location', None),
                "website": getattr(user, 'website', None),
                "status": user.status,
                "is_active": bool(user.is_active),
                "is_email_verified": bool(user.is_email_verified),
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
                "metadata": user.metadata_json if hasattr(user, 'metadata_json') else {}
            }
            
            APP_CONFIG.logger.info(f"Phone number verified for user: {user_id}")
            return create_success_response(
                message="Phone number verified successfully",
                data=UserProfileResponse(**response_data)
            )
            
    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error verifying phone number: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify phone number due to server error"
        )


@user_profile_router.get(
    "/completion",
    status_code=status.HTTP_200_OK
)
async def get_profile_completion(
    current_user: Dict[str, Any] = Depends(get_current_user),
    redis_client=Depends(get_locobuzz_redis),
    db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
    Get user's profile completion percentage with detailed breakdown.
    Uses cache-aside pattern for performance.
    """
    user_id = current_user.get("sub")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    # Try to get from cache first
    cache_key = f"CreatorVerse:user:profile_completion:{user_id}"
    try:
        cached_data = redis_client.get(cache_key)
        if cached_data:
            import json
            completion_data = json.loads(cached_data)
            return create_success_response(
                message="Profile completion retrieved from cache",
                data=ProfileCompletionResponse(**completion_data)
            )
    except Exception as e:
        APP_CONFIG.logger.warning(f"Cache retrieval error in get_profile_completion: {str(e)}")
    
    # If not in cache, calculate profile completion
    with db_conn.transaction() as session:
        # Get user data
        user = session.query(User).filter_by(id=user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check for social profiles
        from database_helper.database.models import OAuthAccount, SocialProfile
        social_profiles = session.query(SocialProfile).join(
            OAuthAccount, OAuthAccount.id == SocialProfile.oauth_account_id
        ).filter(
            OAuthAccount.user_id == user_id
        ).all()
        
        # Initialize completion items list
        completion_items = [
            ProfileCompletionItem(
                label="Email Verified",
                is_completed=bool(user.is_email_verified),
                weight=15,
                description="Verify your email address"
            ),
            ProfileCompletionItem(
                label="Profile Picture",
                is_completed=bool(user.profile_image),
                weight=10,
                description="Add a profile picture"
            ),
            ProfileCompletionItem(
                label="Display Name",
                is_completed=bool(user.name),
                weight=10,
                description="Add your display name"
            ),
            ProfileCompletionItem(
                label="Phone Number",
                is_completed=bool(getattr(user, 'phone_number', None)),
                weight=10,
                description="Add and verify your phone number"
            ),
            ProfileCompletionItem(
                label="Bio",
                is_completed=bool(getattr(user, 'bio', None)),
                weight=15,
                description="Add a short biography"
            ),
            ProfileCompletionItem(
                label="Location",
                is_completed=bool(getattr(user, 'location', None)),
                weight=5,
                description="Add your location"
            ),
            ProfileCompletionItem(
                label="Website",
                is_completed=bool(getattr(user, 'website', None)),
                weight=5,
                description="Add your website or social media link"
            ),
        ]
        
        # Add social platform items
        youtube_connected = any(p.service.lower() == 'youtube' for p in social_profiles)
        instagram_connected = any(p.service.lower() == 'instagram' for p in social_profiles)
        
        completion_items.extend([
            ProfileCompletionItem(
                label="YouTube Connected",
                is_completed=youtube_connected,
                weight=15,
                description="Connect your YouTube account"
            ),
            ProfileCompletionItem(
                label="Instagram Connected",
                is_completed=instagram_connected,
                weight=15,
                description="Connect your Instagram account"
            )
        ])
        
        # Calculate overall completion
        total_weight = sum(item.weight for item in completion_items)
        completed_weight = sum(item.weight for item in completion_items if item.is_completed)
        
        completion_percentage = int((completed_weight / total_weight) * 100) if total_weight > 0 else 0
        completed_items_count = sum(1 for item in completion_items if item.is_completed)
        
        # Generate suggestions for incomplete items
        suggestions = [
            item.description for item in completion_items 
            if not item.is_completed and item.description
        ]
        
        # Create response
        response = ProfileCompletionResponse(
            completion_percentage=completion_percentage,
            completed_items=completed_items_count,
            total_items=len(completion_items),
            items=completion_items,
            suggestions=suggestions
        )
        
        # Cache the result
        try:
            import json
            redis_client.setex(
                cache_key, 
                RedisConfig.USER_PROFILE_TTL,  # 1 hour from redis_keys.py
                json.dumps(response.model_dump())
            )
        except Exception as e:
            APP_CONFIG.logger.warning(f"Cache storage error in get_profile_completion: {str(e)}")
        
        return create_success_response(
            message="Profile completion calculated successfully",
            data=response
        )


def invalidate_profile_completion_cache(user_id: str, redis_client) -> None:
    """
    Invalidate user's profile completion cache.
    Call this whenever user profile data is updated.
    """
    try:
        cache_key = f"CreatorVerse:user:profile_completion:{user_id}"
        redis_client.delete(cache_key)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Failed to invalidate profile completion cache: {str(e)}")
