from typing import Any

from database_helper.database.sync_db2 import SyncD<PERSON>baseD<PERSON>
from fastapi import APIRouter, Depends, HTTPException, status

from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis
from app.core.database_helper.users_helper import (
    create_new_user_with_uuid_role,
    get_client_info,
)
from app.core.exceptions import OTPError
from app.core.redis_keys import RedisKeys
from app.schemas import ClientInfo
from app.schemas.influencer_schema import (
    InfluencerOtpRequest,
    InfluencerRegisterRequest,
    InfluencerResendOtpRequest,
)
from app.utilities.bloom_filter import get_bloom_filter_manager
from app.utilities.response_handler import create_auth_success_response, create_otp_success_response
from app.utilities.role_validator import validate_role_for_influencer_registration
from app.utilities.validation_functions import validate_email_not_reserved, verify_email_exists_optimized
from app.services.centralized_otp_service import send_registration_otp, verify_any_otp, CentralizedOTPService, OTPType

# Router for authentication-related endpoints
influencer_router = APIRouter()


@influencer_router.post(
    "/register", status_code=status.HTTP_201_CREATED
)
async def register(
        user_data: InfluencerRegisterRequest,
        redis_client=Depends(get_locobuzz_redis)
) -> dict[str, Any]:
    """
    Register a new influencer user and send OTP for email verification.
    """  # 1. Normalize email and validate format
    user_email: str = str(user_data.email).strip().lower()

    # Early validation for reserved domains
    validate_email_not_reserved(user_email)

    # 2. Validate role for influencer registration
    role_name = validate_role_for_influencer_registration(str(user_data.role_uuid), redis_client)
    APP_CONFIG.logger.info(
        f"Role validation successful: {role_name} for influencer registration")  # 3. Check if email is already in the system using Bloom filter
    bloom_filter_manager = get_bloom_filter_manager(redis_client)
    if bloom_filter_manager.check_email_exists(user_email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already exists in the system. Please use login instead."
        )

    # 4. Check if OTP already exists and is valid using centralized service
    from app.services.centralized_otp_service import get_otp_service
    otp_service = get_otp_service(redis_client)
    has_valid_otp, remaining_seconds, message = otp_service._check_otp_status(user_email)

    if has_valid_otp and remaining_seconds:
        return create_otp_success_response(
            message=f"OTP already sent. Please check your email or wait {remaining_seconds} seconds before requesting a new one.",
            email=str(user_data.email)
        )

    # 5. Optimized email validation with environment awareness
    try:
        is_correct, reason = verify_email_exists_optimized(user_email, redis_client)
        APP_CONFIG.logger.info(f"Email validation completed for {user_email}: {reason}")
    except Exception as e:
        APP_CONFIG.logger.error(f"Email validation failed for {user_email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Email validation failed: {str(e)}",
        )

    # 6. Send OTP using centralized service
    try:
        result = await send_registration_otp(
            user_email=user_email,
            role_uuid=str(user_data.role_uuid),
            register_source=str(user_data.register_source),
            register_source_name=str(user_data.register_source_name)
        )
        
        APP_CONFIG.logger.info(f"Influencer OTP sent successfully for email: {user_email}")
        return create_otp_success_response(
            message=result["message"],
            email=str(user_data.email)
        )
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to send OTP for {user_email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send OTP: {str(e)}"
        )


@influencer_router.post(
    "/register/verify-otp",
    status_code=status.HTTP_200_OK,
)
async def verify_register_otp(
        otp_data: InfluencerOtpRequest,
        redis_client=Depends(get_locobuzz_redis),
        db_conn: SyncDatabaseDB = Depends(get_database),
        client_info: ClientInfo = Depends(get_client_info)
) -> dict[str, Any]:
    # 1. Normalize email
    user_email = str(otp_data.email).strip().lower()

    if not redis_client:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Service temporarily unavailable"
        )

    # 2. Check if email is already in the system using Bloom filter
    try:
        bloom_filter_manager = get_bloom_filter_manager(redis_client)
        if bloom_filter_manager.check_email_exists(user_email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists in the system. Please use login instead."
            )
    except Exception as e:
        APP_CONFIG.logger.warning(f"Bloom filter check failed: {str(e)}")

    try:
        # 3. Verify OTP using centralized service
        redis_data = await verify_any_otp(user_email, str(otp_data.otp))
        APP_CONFIG.logger.debug(f"OTP verification data: {redis_data}")

        # 4. Prepare data for user creation
        user_creation_data = {
            **client_info.model_dump(),
            **{k: v for k, v in redis_data.items() if k not in ['email', 'role']},
        }

        # 5. Create user with UUID role system using role from Redis data
        role_uuid = redis_data.get('role')
        if not role_uuid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role information missing from OTP data"
            )

        token_data = await create_new_user_with_uuid_role(
            db_conn, user_email, role_uuid, **user_creation_data
        )

        # 6. Clean up OTP from Redis
        try:
            redis_client.delete(RedisKeys.otp_key(user_email))
        except Exception as e:
            APP_CONFIG.logger.warning(f"Failed to delete OTP key: {str(e)}")

        # 7. Add to bloom filter
        try:
            bloom_filter_manager = get_bloom_filter_manager(redis_client)
            bloom_filter_manager.add_email(user_email)
        except Exception as e:
            APP_CONFIG.logger.warning(f"Failed to add email to bloom filter: {str(e)}")

        # 8. Send welcome email (async, non-blocking)
        try:
            from app.utilities.welcome_email_service import get_welcome_email_service
            welcome_service = get_welcome_email_service(redis_client)
            
            # Send welcome email with user role determination
            role_name = redis_data.get('role_name', 'influencer')  # Default to influencer
            welcome_result = await welcome_service.send_welcome_email_if_not_sent(
                email=user_email,
                user_role=role_name
            )
            
            if welcome_result.get("success"):
                APP_CONFIG.logger.info(f"Welcome email processing completed for {user_email}")
            else:
                APP_CONFIG.logger.warning(f"Welcome email failed for {user_email}: {welcome_result.get('message')}")
                
        except Exception as welcome_error:
            # Welcome email failure should not block registration
            APP_CONFIG.logger.error(f"Welcome email error for {user_email}: {str(welcome_error)}")

        # Extract user ID from JWT token for response
        from app.core.security import decode_access_token_jose
        try:
            payload = decode_access_token_jose(token_data.access_token)
            user_id = str(payload.get("sub", "unknown")) if payload else "unknown"
        except Exception as decode_error:
            APP_CONFIG.logger.warning(f"Could not decode token for user_id: {decode_error}")
            user_id = "unknown"

        APP_CONFIG.logger.info(f"Influencer registration completed successfully: {user_email}")
        return create_auth_success_response(
            access_token=token_data.access_token,
            refresh_token=token_data.refresh_token,
            user_id=user_id,
            message="Registration successful! Please login with your credentials."
        )
    except OTPError as e:
        APP_CONFIG.logger.warning(f"OTP verification failed for {user_email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error during influencer OTP verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed due to internal error"
        ) from e


@influencer_router.post(
    "/register/resend-otp",
    status_code=status.HTTP_200_OK
)
async def resend_influencer_otp(
        resend_data: InfluencerResendOtpRequest,
        redis_client=Depends(get_locobuzz_redis)
) -> dict[str, Any]:
    """
    Resend OTP for influencer registration with 30-second cooldown.
    Validates email and checks cooldown before sending new OTP.
    """
    user_email: str = str(resend_data.email).strip().lower()

    # Validate email format and domain
    validate_email_not_reserved(user_email)

    # Use centralized OTP service for resending
    otp_service = CentralizedOTPService(redis_client, None)
    result = await otp_service.resend_otp(
        user_email=user_email,
        otp_type=OTPType.REGISTRATION,
        role_uuid=str(resend_data.role_uuid),
        register_source="web",
        register_source_name="website"
    )

    if not result.get("success"):
        if result.get("retry_after", 0) > 0:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=result.get("message", "Please wait before requesting a new OTP.")
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("message", "Failed to resend OTP. Please try again.")
            )

    APP_CONFIG.logger.info(f"Influencer OTP resent successfully for email: {user_email}")
    return create_otp_success_response(
        message=result.get("message", "OTP resent successfully. Please check your email."),
        email=resend_data.email
    )
