from typing import Any
from uuid import UUID

from database_helper.database.models import OAuth<PERSON><PERSON>unt, SocialProfile
from database_helper.database.sync_db2 import SyncDatabaseDB
from fastapi import APIRouter, Depends, HTTPException, status

from app.api.api_v1.endpoints.user_profile import invalidate_profile_completion_cache
from app.api.deps import get_current_user
from app.core.config import get_database, APP_CONFIG
from app.schemas.social_profile import SocialProfileResponse
from app.utilities.response_handler import create_success_response

social_profiles_router = APIRouter()


@social_profiles_router.get(
    "/me",
    status_code=status.HTTP_200_OK
)
async def get_my_social_profiles(
        current_user: dict[str, Any] = Depends(get_current_user),
        db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
    Get all social profiles for the current authenticated user.
    """
    user_id = current_user.get("sub")
    try:
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid user token"
            )

        with db_conn.transaction() as session:
            # Get OAuth account IDs for the user
            oauth_accounts = session.query(OAuthAccount.id).filter_by(user_id=user_id).all()
            oauth_account_ids = [str(account.id) for account in oauth_accounts]

            if not oauth_account_ids:
                return create_success_response(
                    message="No social profiles found",
                    data=[]
                )

            # Get social profiles for these OAuth accounts
            social_profiles = session.query(SocialProfile).filter(
                SocialProfile.oauth_account_id.in_(oauth_account_ids)
            ).all()

            social_profiles_data = [SocialProfileResponse.from_orm(profile) for profile in social_profiles]
            return create_success_response(
                message="Social profiles retrieved successfully",
                data=social_profiles_data
            )

    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error fetching social profiles for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch social profiles"
        )


@social_profiles_router.get(
    "/{profile_id}",
    status_code=status.HTTP_200_OK
)
async def get_social_profile(
        profile_id: UUID,
        current_user: dict[str, Any] = Depends(get_current_user),
        db_conn: SyncDatabaseDB = Depends(get_database)
) -> dict[str, Any]:
    """
    Get a specific social profile by ID (must belong to current user).
    """
    try:
        user_id = current_user.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid user token"
            )

        with db_conn.transaction() as session:
            # Verify the profile belongs to the user
            profile = session.query(SocialProfile).join(OAuthAccount).filter(
                SocialProfile.id == profile_id,
                OAuthAccount.user_id == user_id
            ).first()

            if not profile:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Social profile not found"
                )

            profile_data = SocialProfileResponse.from_orm(profile)
            return create_success_response(
                message="Social profile retrieved successfully",
                data=profile_data
            )

    except HTTPException:
        raise
    except Exception as e:
        APP_CONFIG.logger.error(f"Error fetching social profile {profile_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch social profile"
        )


# Add this helper function to invalidate completion cache when profiles change
def update_profile_completion_after_social_change(user_id: str, redis_client) -> None:
    """Invalidate profile completion cache after social profile changes"""
    invalidate_profile_completion_cache(user_id, redis_client)
