from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.core.config import APP_CONFIG
from app.core.database import get_database
from app.utilities.redis_code import get_locobuzz_redis
from app.schemas.brand_membership import (
    BrandMembershipRequestSchema,
    BrandMembershipResponseSchema,
    MembershipRequestListSchema
)
from app.services.brand_membership_service import BrandMembershipService
from app.api.deps import get_current_user
from app.models.users import User

router = APIRouter()

@router.post("/request", response_model=BrandMembershipResponseSchema)
async def request_brand_membership(
    request_data: BrandMembershipRequestSchema,
    current_user: User = Depends(get_current_user),
    db_conn: Session = Depends(get_database),
    redis_client = Depends(get_locobuzz_redis)
):
    """
    Request membership to a brand organization.
    Creates a membership request with 'requested' status.
    """
    try:
        service = BrandMembershipService(db_conn, redis_client)
        result = await service.create_membership_request(
            user_id=current_user.id,
            brand_id=request_data.brand_id,
            requested_role=request_data.requested_role,
            message=request_data.message
        )
        return result
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to create brand membership request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create membership request"
        )

@router.get("/requests", response_model=List[MembershipRequestListSchema])
async def get_membership_requests(
    brand_id: str,
    current_user: User = Depends(get_current_user),
    db_conn: Session = Depends(get_database),
    redis_client = Depends(get_locobuzz_redis)
):
    """
    Get pending membership requests for a brand.
    Only accessible by brand-admin or organization admin.
    """
    try:
        service = BrandMembershipService(db_conn, redis_client)
        requests = await service.get_pending_requests(
            brand_id=brand_id,
            admin_user_id=current_user.id
        )
        return requests
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to fetch membership requests: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch membership requests"
        )

@router.post("/requests/{request_id}/accept")
async def accept_membership_request(
    request_id: str,
    current_user: User = Depends(get_current_user),
    db_conn: Session = Depends(get_database),
    redis_client = Depends(get_locobuzz_redis)
):
    """
    Accept a brand membership request.
    Only accessible by brand-admin or organization admin.
    """
    try:
        service = BrandMembershipService(db_conn, redis_client)
        result = await service.accept_membership_request(
            request_id=request_id,
            admin_user_id=current_user.id
        )
        return {"success": True, "message": "Membership request accepted", "data": result}
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to accept membership request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to accept membership request"
        )

@router.post("/requests/{request_id}/reject")
async def reject_membership_request(
    request_id: str,
    current_user: User = Depends(get_current_user),
    db_conn: Session = Depends(get_database),
    redis_client = Depends(get_locobuzz_redis)
):
    """
    Reject a brand membership request.
    Only accessible by brand-admin or organization admin.
    """
    try:
        service = BrandMembershipService(db_conn, redis_client)
        await service.reject_membership_request(
            request_id=request_id,
            admin_user_id=current_user.id
        )
        return {"success": True, "message": "Membership request rejected"}
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to reject membership request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reject membership request"
        )
