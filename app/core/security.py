import hashlib
import secrets
import uuid
from datetime import UTC, datetime, timedelta
from typing import Any

from passlib.context import Crypt<PERSON>ontext
from app.core.config import APP_CONFIG

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Option 3: Library-agnostic approach (recommended)
try:
    # Try to import jose first (commonly used with FastAPI)
    from jose import jwt, JWTError
    JWT_LIBRARY = "jose"
except ImportError:
    # Fall back to PyJWT
    import jwt
    from jwt import PyJWTError as JWTError
    JWT_LIBRARY = "pyjwt"

def create_access_token(
    data: dict[str, Any], expires_delta: timedelta | None = None
) -> str:
    """Create access token with user data"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(UTC) + expires_delta
    else:
        expire = datetime.now(UTC) + timedelta(
            minutes=APP_CONFIG.jwt_access_token_expire_minutes
        )

    to_encode.update({"exp": expire, "jti": str(uuid.uuid4())})
    encoded_jwt: str = jwt.encode(
        to_encode,
        APP_CONFIG.jwt_secret_key,
        algorithm=APP_CONFIG.jwt_algorithm
    )
    return encoded_jwt


def create_refresh_token() -> str:
    """Create a secure refresh token"""
    return secrets.token_urlsafe(32)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    is_valid: bool = pwd_context.verify(plain_password, hashed_password)
    return is_valid


def get_password_hash(password: str) -> str:
    hashed_pwd: str = pwd_context.hash(password)
    return hashed_pwd


def generate_csrf_state() -> str:
    """Generate a secure CSRF state token"""
    return secrets.token_urlsafe(32)


def hash_otp(otp: str) -> str:
    """
    Hashes the given OTP using SHA-256 with a secret salt (pepper).

    The salt is read from the environment variable OTP_HASH_SALT.
    Returns the hexdigest of SHA-256(salt + otp).
    """
    salt = APP_CONFIG.otp_hash_salt
    # Combine salt and OTP, then compute SHA-256
    combined = salt + otp
    hashed = hashlib.sha256(combined.encode("utf-8")).hexdigest()
    return hashed


def normalize_email(email: str) -> str:
    """Convert email to lowercase and strip surrounding whitespace."""
    return email.strip().lower()


def decode_access_token_jose(token: str) -> dict[str, Any] | None:
    """
    Decode and validate JWT access token using python-jose.
    
    Args:
        token: JWT token string to decode
        
    Returns:
        dict containing token payload if valid, None if invalid
    """
    try:
        payload = jwt.decode(
            token,
            APP_CONFIG.jwt_secret_key,
            algorithms=[APP_CONFIG.jwt_algorithm]
        )
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except JWTError:  # Import JWTError from jose, not jwt
        return None


def generate_verification_token() -> str:
    """Generate a secure verification token for domain verification"""
    import secrets
    return secrets.token_urlsafe(32)

