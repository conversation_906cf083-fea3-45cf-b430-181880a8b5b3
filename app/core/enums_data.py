from enum import Enum, IntEnum
from functools import lru_cache


class SourceRegister(IntEnum):
    """
    Enum for source register types.
    """
    EMAIL_OTP = 1
    PHONE_OTP = 2
    INSTAGRAM_OAUTH = 3
    FACEBOOK_OAUTH = 4
    GOOGLE_OAUTH = 5

    @classmethod
    @lru_cache(maxsize=4)
    def get_method_key(cls, value: int) -> str:
        """
        Get the method key string by its value for database queries.
        Returns format: email_otp, phone_otp, oauth_google, oauth_instagram, oauth_facebook
        """
        if value == cls.EMAIL_OTP.value:
            return "email_otp"
        if value == cls.PHONE_OTP.value:
            return "phone_otp"
        if value == cls.GOOGLE_OAUTH.value:
            return "oauth_google"
        if value == cls.INSTAGRAM_OAUTH.value:
            return "oauth_instagram"
        if value == cls.FACEBOOK_OAUTH.value:
            return "oauth_facebook"
        return "email_otp"  # Default fallback for unknown values

    @classmethod
    @lru_cache(maxsize=10)
    def get_enum_value(cls, register_source: str) -> int:
        """
        Convert string register_source to numeric enum value.
        For database compatibility where register_source is smallint.
        """
        if register_source in ["web", "email_otp", "1"]:
            return cls.EMAIL_OTP.value
        elif register_source in ["phone_otp", "2"]:
            return cls.PHONE_OTP.value
        elif register_source in ["google_oauth", "oauth_google", "3"]:
            return cls.GOOGLE_OAUTH.value
        elif register_source in ["instagram_oauth", "oauth_instagram", "4"]:
            return cls.INSTAGRAM_OAUTH.value
        elif register_source in ["facebook_oauth", "oauth_facebook", "5"]:
            return cls.FACEBOOK_OAUTH.value
        else:
            return cls.EMAIL_OTP.value  # Default to email OTP for unknown values


class UserRoleEnum(Enum):
    """
    Enum for user roles.
    """
    INFLUENCER = 1
    BRAND = 2
    BRAND_ADMIN = 3

    @classmethod
    def choices(cls):
        return [cls.INFLUENCER, cls.BRAND, cls.BRAND_ADMIN]

    # class method to get role name by value in lowercase
    @classmethod
    @lru_cache(maxsize=2)
    def get_role_name(cls, value: int) -> str:
        """
        Get the role name in lowercase by its value.
        """
        for role in cls:
            if role.value == value:
                return role.name.lower()
        raise ValueError(f"No role found for value: {value}")


class UserType(str, Enum):
    """User types supported in the system"""
    INFLUENCER = "influencer"
    BRAND = "brand"
    ADMIN = "admin"
