import os
import sys
from dataclasses import dataclass
from typing import Any

from database_helper.database.sync_db2 import SyncDatabaseDB
from locobuzz_python_configuration import create_configuration
from locobuzz_python_configuration.logger_config import setup_logger
from redis import Redis

from app.utilities.redis_code import initialize_redis

TOTAL_MEMBERS = 10  # Default count of members in an organization

DEFAULT_OTP_LENGTH = 4

@dataclass
class AppConfig:
    """Configuration class for the user microservice."""

    environ: str
    service_name: str
    logger: Any
    log_enabled: list[str]
    redis_obj: Redis
    database_url: str
    db_conn: SyncDatabaseDB
    # OAuth settings
    oauth_google_client_id: str = ""
    oauth_google_client_secret: str = ""
    oauth_google_redirect_uri: str = "S"
    google_scope_yt: str = "https://www.googleapis.com/auth/youtube.readonly"
    oauth_instagram_client_id: str = ""
    oauth_instagram_client_secret: str = ""
    oauth_instagram_redirect_uri: str = "http://localhost:8000/v1/common/oauth/callback"
    # Other settings
    MAILGUN_DOMAIN = "locobuzz.info"
    MAILGUN_SECRET = "************************************"
    EMAIL_SENDER_NAME = "CreatorVerse"
    swagger_username: str = "<EMAIL>"
    swagger_password: str = "swagger123"
    enable_swagger_auth: bool = True
    secret_key = "secret_key"
    # Centralized JWT configurations
    jwt_secret_key: str = "secret_key"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 20
    jwt_refresh_token_expire_days: int = 7
    # OTP security settings
    otp_hash_salt: str = "12345"
    redis_config = {
        "master_auth_methods_expire": 3600,  # 1 hour
        "rbac_expire": 7200,  # 2 hours
        "bloom_filter_expire": 86400,  # 24 hours
    }
    # OAuth Settings
    skip_profile_enrichment_for_existing_users: bool = True  # Controls whether to skip profile enrichment for existing users    # Email Validation Optimization Settings
    email_validation_enabled: bool = True  # Master switch for email validation
    email_smtp_validation_enabled: bool = True  # SMTP validation toggle
    email_validation_timeout: float = 2.0  # Reduced timeout for faster failures
    email_domain_cache_ttl: int = 3600  # Cache domain validation for 1 hour
    email_validation_environments: list[str] | None = None  # Environments where full validation is enabled

    def __post_init__(self) -> None:
        """Post-initialization to set environment-based email validation."""
        if self.email_validation_environments is None:
            self.email_validation_environments = ["production", "staging"]

        # Disable SMTP validation in development/test environments for speed
        if self.environ.lower() not in [env.lower() for env in self.email_validation_environments]:
            self.email_smtp_validation_enabled = False
            self.logger.info(f"SMTP email validation disabled for environment: {self.environ}")
        else:
            self.logger.info(f"SMTP email validation enabled for environment: {self.environ}")


def get_appsetting_file_path() -> str:
    """Get the path to appsettings.json relative to the current script."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    project_root = os.path.dirname(parent_dir)
    return os.path.join(project_root, "appsettings.json")


def _setup_logger(configuration: dict[str, Any], service_name: str) -> Any:
    """Setup logger configuration."""
    is_async_logger = configuration.get("_is_async_logger", False)
    log_level = configuration["_log_level"]
    log_type = configuration.get("_extra_properties", {}).get("log_type", "json")

    logger = setup_logger(
        service_name,
        async_mode=is_async_logger,
        log_level_str=log_level,
        log_type=log_type,
        sys_module=sys,
    )
    logger.info("Logger configured" + (" in async mode" if is_async_logger else ""))
    return logger


def _setup_redis(configuration: dict[str, Any], environ: str, logger: Any) -> Redis:
    """Setup Redis configuration."""
    redis_url = configuration.get("_redis_host")
    if not redis_url or redis_url in {"REDIS_SERVER", "", " "}:
        sys.exit(0)
        return None

    redis_obj = initialize_redis(redis_url, environ, logger)
    if not redis_obj:
        logger.warning("Exception while initializing Redis")
        sys.exit(0)

    logger.info("Redis configured successfully with CreatorVerse:feature:key pattern")
    return redis_obj


def _setup_database(
        configuration: dict[str, Any], logger: Any
) -> tuple[str, SyncDatabaseDB]:
    """Setup database configuration."""
    database_url = configuration.get("_extra_properties", {}).get("connection_string")
    if not database_url:
        raise ValueError("Database connection string not found in configuration")

    db_conn = SyncDatabaseDB(database_url)
    user_tables = ["tenants"]
    db_conn.initialize_tables2(user_tables)

    logger.info("Database initialized successfully with user tables")
    return database_url, db_conn


def configure_sync() -> AppConfig:
    """
    Synchronous configuration function that initializes all components.

    Returns:
        AppConfig object containing all configured components
    """
    configuration = create_configuration(
        file_path=get_appsetting_file_path(), required_components=["redis"]
    ).__dict__

    environ = configuration["_environ"]
    service_name = "CREATORVERSE_USER_SERVICE"

    # Setup components
    logger = _setup_logger(configuration, service_name)

    log_enabled = configuration.get("_log_enabled", "PRODUCTION").split(",")
    logger.info(f"Log enabled for environment: {log_enabled}")

    redis_obj = _setup_redis(configuration, environ, logger)
    database_url, db_conn = _setup_database(configuration, logger)

    logger.info(f"{service_name} settings initialized successfully")

    # Initialize configuration
    oauth_config = configuration.get("_extra_properties", {}).get("oauth", {})
    return AppConfig(
        environ=environ,
        service_name=service_name,
        logger=logger,
        log_enabled=log_enabled,
        redis_obj=redis_obj,
        database_url=database_url,
        db_conn=db_conn,
        oauth_google_client_id=oauth_config.get("GOOGLE_CLIENT_ID", ""),
        oauth_google_client_secret=oauth_config.get("GOOGLE_CLIENT_SECRET", ""),
        oauth_google_redirect_uri=oauth_config.get("GOOGLE_REDIRECT_URI", ""),
        oauth_instagram_client_id=oauth_config.get("INSTAGRAM_CLIENT_ID", ""),
        oauth_instagram_client_secret=oauth_config.get("INSTAGRAM_CLIENT_SECRET", ""),
        oauth_instagram_redirect_uri=oauth_config.get("INSTAGRAM_REDIRECT_URI", "")
    )


# Initialize configuration
APP_CONFIG = configure_sync()


def get_locobuzz_redis() -> Redis:
    """Get Redis object."""
    return APP_CONFIG.redis_obj


def get_database() -> SyncDatabaseDB:
    """Get the database connection object."""
    return APP_CONFIG.db_conn


def get_logger() -> Any:
    """Get logger object."""
    return APP_CONFIG.logger


def get_config() -> AppConfig:
    """Get the complete application configuration."""
    return APP_CONFIG
