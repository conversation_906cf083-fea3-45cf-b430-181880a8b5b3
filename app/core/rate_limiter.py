"""
Rate limiting middleware for CreatorVerse user microservice.
Uses Redis for distributed rate limiting with IP-based tracking.
"""

import time

from fastapi import Request, status
from redis.exceptions import RedisError

from app.core.config import get_locobuzz_redis
from app.core.exceptions import RateLimitError


def get_client_ip(request: Request) -> str:
    """
    Extract client IP address from request headers.

    Args:
        request: FastAPI request object

    Returns:
        Client IP address as string
    """
    x_forwarded_for = request.headers.get("x-forwarded-for")
    if x_forwarded_for:
        # If multiple IPs are forwarded, take the first one (the client)
        ip = x_forwarded_for.split(",")[0].strip()
    else:
        ip = request.client.host if request.client else "unknown"
    return ip


class RateLimiter:
    """
    Redis-based rate limiter for API endpoints.
    """

    def __init__(
        self,
        max_requests: int = 20,
        window_seconds: int = 60,
        key_prefix: str = "CreatorVerse:ratelimit:api",
    ):
        """
        Initialize rate limiter.

        Args:
            max_requests: Maximum number of requests allowed in the time window
            window_seconds: Time window in seconds for rate limiting
            key_prefix: Redis key prefix for rate limiting
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.key_prefix = key_prefix

    async def check_rate_limit(self, request: Request) -> None:
        """
        Check if request should be rate limited.

        Args:
            request: FastAPI request object

        Raises:
            HTTPException: If rate limit is exceeded
        """
        client_ip = get_client_ip(request)
        redis_key = f"{self.key_prefix}:{client_ip}"

        # Get Redis client from dependency
        redis_client = get_locobuzz_redis()

        try:
            current_time = int(time.time())
            window_start = current_time - self.window_seconds

            # Use Redis pipeline for atomic operations
            pipe = redis_client.pipeline()

            # Remove expired entries
            pipe.zremrangebyscore(redis_key, 0, window_start)

            # Count current requests in window
            pipe.zcard(redis_key)

            # Add current request
            pipe.zadd(redis_key, {str(current_time): current_time})

            # Set expiration
            pipe.expire(redis_key, self.window_seconds)

            # Execute pipeline
            results = pipe.execute()

            # Get current count (after removing expired entries)
            current_count = results[1]

            if current_count >= self.max_requests:
                # Calculate time until next allowed request
                oldest_request = redis_client.zrange(redis_key, 0, 0, withscores=True)
                if oldest_request:
                    oldest_time = int(oldest_request[0][1])
                    retry_after = oldest_time + self.window_seconds - current_time
                    retry_after = max(1, retry_after)  # At least 1 second
                else:                    retry_after = self.window_seconds

                raise RateLimitError(
                    max_requests=self.max_requests,
                    window_seconds=self.window_seconds,
                    retry_after=retry_after
                )
        except RedisError as e:
            print(f"Redis error in rate limiter: {str(e)}")
            # In case of Redis failure, allow the request to proceed
            # This ensures the API remains available even if Redis is down


# Global rate limiter instance
rate_limiter = RateLimiter(
    max_requests=20, window_seconds=60, key_prefix="CreatorVerse:ratelimit:global"
)


async def rate_limit_dependency(request: Request) -> None:
    """
    FastAPI dependency for rate limiting.

    Args:
        request: FastAPI request object

    Raises:
        HTTPException: If rate limit is exceeded
    """
    await rate_limiter.check_rate_limit(request)


class CustomRateLimiter:
    """
    Decorator class for custom rate limiting on specific endpoints.
    """

    def __init__(self, max_requests: int = 20, window_seconds: int = 60):
        """
        Initialize custom rate limiter.

        Args:
            max_requests: Maximum requests allowed in time window
            window_seconds: Time window in seconds
        """
        self.limiter = RateLimiter(max_requests, window_seconds)

    async def __call__(self, request: Request) -> None:
        """
        Rate limit check callable.

        Args:
            request: FastAPI request object
        """
        await self.limiter.check_rate_limit(request)


# Pre-configured rate limiters for different use cases
auth_rate_limiter = CustomRateLimiter(
    max_requests=10, window_seconds=60
)  # Stricter for auth
general_rate_limiter = CustomRateLimiter(
    max_requests=20, window_seconds=60
)  # General endpoints
