"""
Custom exceptions and centralized error handling for CreatorVerse Users API.
Provides consistent error response structure across all endpoints.
"""

from typing import Any, Optional, Dict, Union, Callable
from fastapi import Request, HTTPException, status
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
import traceback

from app.core.config import APP_CONFIG
from app.utilities.response_handler import create_error_response


class CreatorVerseError(Exception):
    """Base exception class for CreatorVerse application errors"""
    
    def __init__(
        self, 
        message: str, 
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details
        super().__init__(self.message)


class EmailValidationError(CreatorVerseError):
    """Exception for email validation failures"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            details=details
        )


class EmailAlreadyExistsError(CreatorVerseError):
    """Exception for duplicate email registration attempts"""
    
    def __init__(self, email: str):
        super().__init__(
            message=f"Email {email} already exists in the system. Please use login instead.",
            status_code=status.HTTP_409_CONFLICT,
            details={"email": email, "action": "login"}
        )


class EmailNotFoundError(CreatorVerseError):
    """Exception for email not found in system"""
    
    def __init__(self, email: str):
        super().__init__(
            message=f"Email {email} not found in system. Please register first.",
            status_code=status.HTTP_404_NOT_FOUND,
            details={"email": email, "action": "register"}
        )


class OTPError(CreatorVerseError):
    """Exception for OTP-related failures"""
    
    def __init__(self, message: str, status_code: int = status.HTTP_400_BAD_REQUEST):
        super().__init__(message=message, status_code=status_code)


class BusinessLogicError(CreatorVerseError):
    """Exception for business logic violations"""
    
    def __init__(self, message: str, status_code: int = status.HTTP_400_BAD_REQUEST):
        super().__init__(message=message, status_code=status_code)


class ServiceUnavailableError(CreatorVerseError):
    """Exception for service unavailable errors (Redis, DB, etc.)"""
    
    def __init__(self, service: str, details: Optional[str] = None):
        message = f"{service} service is temporarily unavailable. Please try again later."
        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            details={"service": service, "details": details}
        )


class RateLimitError(CreatorVerseError):
    """Exception for rate limit exceeded"""
    
    def __init__(self, max_requests: int, window_seconds: int, retry_after: int):
        message = f"Rate limit exceeded. Maximum {max_requests} requests per {window_seconds} seconds."
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details={
                "max_requests": max_requests,
                "window_seconds": window_seconds,
                "retry_after": retry_after
            }
        )


# Exception Handlers
def creatorverse_exception_handler(request: Request, exc: CreatorVerseError) -> JSONResponse:
    """Handle custom CreatorVerse exceptions"""
    APP_CONFIG.logger.warning(
        f"CreatorVerse error - {exc.__class__.__name__}: {exc.message}",
        extra={
            "status_code": exc.status_code,
            "details": exc.details,
            "endpoint": str(request.url)
        }
    )
    
    response_data = create_error_response(
        message=exc.message,
        details=exc.details
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=response_data
    )


def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle FastAPI HTTPException with consistent response format"""
    # Parse email validation errors from detail
    detail = str(exc.detail)
    
    # Handle email validation errors specifically
    if "Invalid email:" in detail:
        # Extract the actual error message after "Invalid email:"
        email_error = detail.replace("Invalid email: ", "")
        # Clean up the error message
        if "Rejected:" in email_error:
            # Extract meaningful error from SMTP rejection
            parts = email_error.split("Rejected: ")
            if len(parts) > 1:
                smtp_error = parts[1].strip()
                # Extract the main error message, removing technical details
                if "5.1.1" in smtp_error or "does not exist" in smtp_error.lower():
                    clean_message = "The email address does not exist. Please check for typos and try again."
                elif "5.7.1" in smtp_error or "blocked" in smtp_error.lower():
                    clean_message = "Email delivery blocked. Please try a different email address."
                elif "5.2.1" in smtp_error or "mailbox" in smtp_error.lower():
                    clean_message = "Mailbox unavailable. Please check the email address and try again."
                elif "timeout" in smtp_error.lower():
                    clean_message = "Email verification timed out. Please try again later."
                else:
                    clean_message = "Email verification failed. Please check the email address and try again."
            else:
                clean_message = "Email verification failed. Please check the email address and try again."
        else:
            clean_message = email_error
            
        response_message = clean_message
    else:
        response_message = detail
    
    # Log the error appropriately
    if exc.status_code >= 500:
        APP_CONFIG.logger.error(
            f"HTTP {exc.status_code} error: {exc.detail}",
            extra={"endpoint": str(request.url)}
        )
    elif exc.status_code >= 400:
        APP_CONFIG.logger.warning(
            f"HTTP {exc.status_code} error: {exc.detail}",
            extra={"endpoint": str(request.url)}
        )
    
    response_data = create_error_response(message=response_message)
    
    return JSONResponse(
        status_code=exc.status_code,
        content=response_data
    )


def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle Pydantic validation errors"""
    APP_CONFIG.logger.warning(
        f"Validation error: {exc.errors()}",
        extra={"endpoint": str(request.url)}
    )
    
    # Extract meaningful validation messages
    error_messages = []
    for error in exc.errors():
        field = " -> ".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        error_messages.append(f"{field}: {message}")
    
    response_message = "Validation failed: " + "; ".join(error_messages)
    
    response_data = create_error_response(
        message=response_message,
        details={"validation_errors": exc.errors()}
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=response_data
    )


def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle unexpected exceptions"""
    # Log the full traceback for debugging
    APP_CONFIG.logger.error(
        f"Unexpected error: {str(exc)}",
        extra={
            "endpoint": str(request.url),
            "exception_type": exc.__class__.__name__,
            "traceback": traceback.format_exc()
        }
    )
    
    # Return a generic error message to users
    response_data = create_error_response(
        message="An unexpected error occurred. Please try again later."
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=response_data
    )


# Helper functions for creating specific exceptions
def create_email_validation_error(reason: str) -> EmailValidationError:
    """Create an email validation error with cleaned up message"""
    reason_lower = reason.lower()
    
    if "rejected:" in reason:
        # Handle SMTP rejection errors
        if "5.1.1" in reason and ("does not exist" in reason_lower or "nosuchuser" in reason_lower):
            clean_message = "The email address does not exist. Please check for typos and try again."
        elif "5.1.1" in reason:
            clean_message = "Invalid email address. Please check and try again."
        elif "5.7.1" in reason or "blocked" in reason_lower:
            clean_message = "Email delivery blocked. Please try a different email address."
        elif "5.2.1" in reason or "mailbox" in reason_lower:
            clean_message = "Mailbox unavailable. Please check the email address and try again."
        elif "5.4.1" in reason or "relay" in reason_lower:
            clean_message = "Email server temporarily unavailable. Please try again later."
        else:
            clean_message = "Email verification failed. Please try again with a valid email address."
    elif "invalid email format" in reason_lower:
        clean_message = "Please enter a valid email address."
    elif "no mx records" in reason_lower:
        clean_message = "Email domain is not valid. Please check the email address."
    elif "timed out" in reason_lower or "timeout" in reason_lower:
        clean_message = "Email verification timed out. Please try again."
    elif "could not verify" in reason_lower:
        clean_message = "Unable to verify email address. Please check and try again."
    else:
        # Remove technical details and provide clean message
        clean_message = "Email verification failed. Please check the email address and try again."
    
    return EmailValidationError(clean_message)


def create_otp_error(error_type: str, **kwargs) -> OTPError:
    """Create specific OTP errors"""
    if error_type == "expired":
        return OTPError("OTP has expired. Please request a new one.")
    elif error_type == "invalid":
        return OTPError("Invalid OTP. Please check and try again.")
    elif error_type == "not_found":
        return OTPError("OTP not found or has expired. Please request a new one.")
    elif error_type == "too_many_attempts":
        lockout_time = kwargs.get("lockout_time", "5 minutes")
        return OTPError(
            f"Too many failed attempts. Please try again after {lockout_time}.",
            status_code=status.HTTP_429_TOO_MANY_REQUESTS
        )
    else:
        return OTPError("OTP verification failed. Please try again.")


def create_service_error(service: str, details: Optional[str] = None) -> ServiceUnavailableError:
    """Create service unavailable errors"""
    return ServiceUnavailableError(service, details)
