from uuid import UUID


class RedisKeys:
    """Centralized Redis keys for the CreatorVerse application."""

    @staticmethod
    def user_by_email(email: str) -> str:
        """
        Redis key for caching user data by email address.
        
        Usage: Cache user profile information for quick email-based lookups
        Data Type: Hash
        TTL: 7 days (USER_TTL)
        
        Actual Data Structure:
        {
            "id": "dd1cbca4-0891-474c-b74d-b329e49756a8",
            "email": "<EMAIL>",
            "phone_number": "",
            "status": "active",
            "is_active": "True",
            "is_email_verified": "True", 
            "name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>"
        }        """
        return f"CreatorVerse:user:email:{email}"

    @staticmethod
    def welcome_email_sent(email: str) -> str:
        """
        Redis key for tracking if a welcome email has been sent to a user.
        
        Usage: Prevent duplicate welcome emails during registration
        Data Type: String (timestamp)
        TTL: 30 days
        
        Sample Data:
        "2025-01-27T10:30:00Z"
        """
        return f"CreatorVerse:welcome_email:sent:{email}"

    @staticmethod
    def get_email_bloom_filter_key() -> str:
        """
        Redis key for email bloom filter to check if email exists.
        
        Usage: Probabilistic data structure to quickly check if an email might exist in the system
        Data Type: String (bloom filter bits)
        TTL: No expiration (persistent)
        
        Note: This key returns the base key. The actual implementation uses:
        - CreatorVerse:bloom:email:bitmap (string) - Binary bloom filter data
        - CreatorVerse:bloom:email:metadata (hash) - Bloom filter configuration
        
        Actual Metadata Structure:
        {
            "capacity": "100000",
            "error_rate": "0.001", 
            "bit_array_size": "1437758",
            "hash_count": "9",
            "element_count": "1"
        }
        """
        return "CreatorVerse:bloom:email"

    @staticmethod
    def master_auth_methods() -> str:
        """
        Redis key for caching master authentication methods configuration.
        
        Usage: Store available authentication methods for the application
        Data Type: Hash
        TTL: 24 hours (MASTER_AUTH_METHODS_TTL)
        
        Sample Data:
        {
            "google_oauth": "true",
            "github_oauth": "true", 
            "email_password": "true",
            "magic_link": "false"
        }
        """
        return "CreatorVerse:master:auth_methods"

    @staticmethod
    def organization_key(domain: str) -> str:
        """
        Redis key for caching organization data by domain.
        
        Usage: Cache organization information for domain-based lookups
        Data Type: Hash
        TTL: 1 hour (DOMAIN_ORG_TTL)
        
        Sample Data:
        {
            "id": "org-uuid",
            "name": "Example Corp",
            "domain": "example.com",
            "is_active": "true",
            "plan_type": "premium",
            "created_at": "2025-01-01T00:00:00Z"
        }
        """
        return f"CreatorVerse:organization:domain:{domain.lower()}"

    @staticmethod
    def organization_members(organization_id: str) -> str:
        """
        Redis key for caching organization members list.
        
        Usage: Cache list of users belonging to an organization for quick access
        Data Type: Set or List
        TTL: 30 minutes (ORGANIZATION_MEMBERS_TTL)
        
        Sample Data (Set):
        ["user-uuid-1", "user-uuid-2", "user-uuid-3"]
        
        Or as Hash with roles:
        {
            "user-uuid-1": "brand-admin",
            "user-uuid-2": "member", 
            "user-uuid-3": "viewer"
        }
        """
        return f"CreatorVerse:organization:members:{organization_id}"  # OTP related keys

    @staticmethod
    def otp_key(email: str) -> str:
        """
        Redis key for storing OTP (One-Time Password) data.
        
        Usage: Store OTP codes and attempt counts for email verification
        Data Type: Hash
        TTL: 15 minutes (OTP_TTL)
        
        Sample Data:
        {
            "code": "123456",
            "attempts": "2",
            "created_at": "2025-01-01T00:00:00Z",
            "email": "<EMAIL>",
            "max_attempts": "5"
        }
        """
        return f"CreatorVerse:otp:{email}"  # RBAC related keys

    @staticmethod
    def rbac_roles() -> str:
        """
        Redis key for caching all available roles in the system.
        
        Usage: Store master list of roles for RBAC system
        Data Type: Hash
        TTL: 1 hour (RBAC_TTL)
        
        Sample Data:
        {
            "role-uuid-1": "{'name': 'brand-admin', 'description': 'Brand Administrator'}",
            "role-uuid-2": "{'name': 'member', 'description': 'Organization Member'}",
            "role-uuid-3": "{'name': 'viewer', 'description': 'Read-only Access'}"
        }
        """
        return "CreatorVerse:rbac:roles"

    @staticmethod
    def rbac_permissions() -> str:
        """
        Redis key for caching all available permissions in the system.
        
        Usage: Store master list of permissions for RBAC system
        Data Type: Hash
        TTL: 1 hour (RBAC_TTL)
        
        Sample Data:
        {
            "perm-uuid-1": "{'name': 'user.create', 'description': 'Create Users'}",
            "perm-uuid-2": "{'name': 'user.read', 'description': 'Read User Data'}",
            "perm-uuid-3": "{'name': 'org.manage', 'description': 'Manage Organization'}"
        }
        """
        return "CreatorVerse:rbac:permissions"

    @staticmethod
    def rbac_user_roles(user_id: str | UUID) -> str:
        """
        Redis key for caching user's assigned roles.
        
        Usage: Store roles assigned to a specific user for quick authorization checks
        Data Type: Set or List
        TTL: 1 hour (USER_ROLES_TTL)
        
        Sample Data (Set):
        ["brand-admin", "member"]
        
        Or as Hash with additional metadata:
        {
            "brand-admin": "org-uuid-1",
            "member": "org-uuid-2"
        }
        """
        return f"CreatorVerse:rbac:user_roles:{user_id}"

    @staticmethod
    def rbac_user_permission(user_id: str | UUID) -> str:
        """
        Redis key for caching user's effective permissions.
        
        Usage: Store computed permissions for a user (combination of role permissions)
        Data Type: Set
        TTL: 1 hour (RBAC_TTL)
        
        Sample Data:
        ["user.create", "user.read", "user.update", "org.read", "campaign.create"]
        """
        return f"CreatorVerse:rbac:user_permission:{user_id}"

    @staticmethod
    def rbac_role_permissions(role_id: str | UUID) -> str:
        """
        Redis key for caching permissions assigned to a specific role.
        
        Usage: Store permissions that belong to a role for quick role-based checks
        Data Type: Set
        TTL: 1 hour (RBAC_TTL)
        
        Sample Data:
        ["user.read", "user.update", "campaign.create", "campaign.read"]        """
        return f"CreatorVerse:rbac:role_perms:{role_id}"

    # Token related keys
    @staticmethod
    def refresh_token(user_id: str, token: str) -> str:
        """
        Redis key for storing refresh token data.
        
        Usage: Store refresh token information for JWT token management
        Data Type: Hash
        TTL: 7 days (REFRESH_TOKEN_TTL)
        
        Actual Data Structure:
        {
            "user_id": "dd1cbca4-0891-474c-b74d-b329e49756a8",
            "roles": "[\"influencer\"]",
            "permissions": "[]"        }
        """
        return f"CreatorVerse:refresh_token:{user_id}:{token}"

    @staticmethod
    def session_key(user_id: str, session_id: str) -> str:
        """
        Redis key for storing user session data.
        
        Usage: Store active user session information for session management
        Data Type: Hash
        TTL: Based on session configuration
        
        Actual Data Structure:
        {
            "user_agent": "PostmanRuntime/7.44.0",
            "is_revoked": "0",
            "refresh_token": "mFGjXbSBOprZo678TQwZpTnexWXUVf92PFm2mhzdq24",
            "issued_at": "1749474983",
            "expires_at": "1750079783",
            "user_id": "dd1cbca4-0891-474c-b74d-b329e49756a8",            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
            "ip_address": "127.0.0.1"
        }
        """
        return f"CreatorVerse:session:{user_id}:{session_id}"

    # OAuth related keys
    @staticmethod
    def oauth_state(state: str) -> str:
        """
        Redis key for storing OAuth state parameter.
        
        Usage: Store OAuth state tokens to prevent CSRF attacks during OAuth flow
        Data Type: Hash
        TTL: 5 minutes (OAUTH_STATE_TTL)
        
        Sample Data:
        {
            "state": "random-state-string",
            "provider": "google",
            "redirect_uri": "https://app.example.com/callback",
            "created_at": "2025-01-01T00:00:00Z"
        }
        """
        return f"CreatorVerse:oauth:state:{state}"  # Tenant related keys

    @staticmethod
    def domain(domain: str) -> str:
        """
        Redis key for caching tenant domain information.
        
        Usage: Store tenant/domain configuration for multi-tenant architecture
        Data Type: Hash
        TTL: 7 days (TENANT_DOMAIN_TTL)
        
        Sample Data:
        {
            "domain": "example.com",
            "tenant_id": "tenant-uuid",
            "is_active": "true",
            "config": "{'features': ['oauth', 'sso']}"
        }
        """
        return f"CreatorVerse:{domain}"

    @staticmethod
    def domain_organization_cache(domain: str) -> str:
        """
        Redis key for caching domain to organization mapping.
        
        Usage: Quick lookup to find which organization owns a domain
        Data Type: String (organization ID) or Hash
        TTL: 1 hour (DOMAIN_ORG_TTL)
        
        Sample Data (String):
        "org-uuid-123"
        
        Or as Hash:
        {
            "organization_id": "org-uuid-123",
            "organization_name": "Example Corp",
            "cached_at": "2025-01-01T00:00:00Z"
        }        """
        return f"CreatorVerse:domain:org:{domain}"

    # Bloom filter related keys
    @staticmethod
    def bloom_filter_emails() -> str:
        """
        Redis key for email bloom filter (alternative key name).
        
        Usage: Probabilistic data structure to quickly check if an email exists
        Data Type: String (bloom filter bits)  
        TTL: No expiration (persistent)
        
        Note: This returns the same base key as get_email_bloom_filter_key()
        The actual implementation uses two keys:
        - CreatorVerse:bloom:email:bitmap (string) - Binary bloom filter data
        - CreatorVerse:bloom:email:metadata (hash) - Configuration and stats
        """
        return "CreatorVerse:bloom:emails"  # Master data keys

    @staticmethod
    def master_channels_all() -> str:
        """
        Redis key for caching all available channels.
        
        Usage: Store master list of all channels in the system
        Data Type: Hash or List
        TTL: No specific TTL (master data)
        
        Sample Data (Hash):
        {
            "channel-uuid-1": "{'name': 'Instagram', 'type': 'social', 'is_active': true}",
            "channel-uuid-2": "{'name': 'YouTube', 'type': 'video', 'is_active': true}",
            "channel-uuid-3": "{'name': 'TikTok', 'type': 'short_video', 'is_active': true}"
        }
        """
        return "CreatorVerse:master:channels:all"

    @staticmethod
    def master_channel(channel_id: str | UUID) -> str:
        """
        Redis key for caching individual channel data.
        
        Usage: Store specific channel information for quick lookups
        Data Type: Hash
        TTL: No specific TTL (master data)
        
        Sample Data:
        {
            "id": "channel-uuid",
            "name": "Instagram",
            "type": "social",
            "description": "Instagram social media platform",
            "is_active": "true",
            "created_at": "2025-01-01T00:00:00Z",
            "api_config": "{'base_url': 'https://api.instagram.com'}"
        }
        """
        return f"CreatorVerse:master:channel:{channel_id}"

    @staticmethod
    def master_inner_channels_all() -> str:
        """
        Redis key for caching all available inner channels.
        
        Usage: Store master list of all inner channels (sub-channels within main channels)
        Data Type: Hash or List
        TTL: No specific TTL (master data)
        
        Sample Data (Hash):
        {
            "inner-channel-uuid-1": "{'name': 'Stories', 'parent_channel': 'instagram', 'type': 'story'}",
            "inner-channel-uuid-2": "{'name': 'Reels', 'parent_channel': 'instagram', 'type': 'short_video'}",
            "inner-channel-uuid-3": "{'name': 'Posts', 'parent_channel': 'instagram', 'type': 'feed'}"
        }
        """
        return "CreatorVerse:master:inner_channels:all"

    @staticmethod
    def master_inner_channel(inner_channel_id: str | UUID) -> str:
        """
        Redis key for caching individual inner channel data.
        
        Usage: Store specific inner channel information for quick lookups
        Data Type: Hash
        TTL: No specific TTL (master data)
        
        Sample Data:
        {
            "id": "inner-channel-uuid",
            "name": "Stories",
            "parent_channel_id": "instagram-uuid",
            "type": "story",
            "description": "Instagram Stories content type",
            "is_active": "true",
            "max_duration": "24",
            "content_specs": "{'max_size': '100MB', 'formats': ['jpg', 'mp4']}"
        }
        """
        return f"CreatorVerse:master:inner_channel:{inner_channel_id}"

    @staticmethod
    def master_channel_inner_channels(channel_id: str | UUID) -> str:
        """
        Redis key for caching inner channels belonging to a specific channel.
        
        Usage: Store list of inner channels for a parent channel for hierarchical lookups
        Data Type: Set or List
        TTL: No specific TTL (master data)
        
        Sample Data (List):
        ["stories-uuid", "reels-uuid", "posts-uuid", "igtv-uuid"]
        
        Or as Hash with metadata:
        {
            "stories-uuid": "{'name': 'Stories', 'order': 1}",
            "reels-uuid": "{'name': 'Reels', 'order': 2}",
            "posts-uuid": "{'name': 'Posts', 'order': 3}"
        }
        """
        return f"CreatorVerse:master:channel:{channel_id}:inner_channels"


class RedisPatterns:
    """Redis key patterns for bulk operations."""
    RBAC_ALL = "CreatorVerse:rbac:*"
    USER_ALL = "CreatorVerse:user:*"
    OAUTH_ALL = "CreatorVerse:oauth:*"
    OTP_ALL = "CreatorVerse:otp:*"
    TENANT_ALL = "CreatorVerse:tenant:*"
    ORGANIZATION_ALL = "CreatorVerse:organization:*"
    SESSION_ALL = "CreatorVerse:session:*"
    REFRESH_TOKEN_ALL = "CreatorVerse:refresh_token:*"
    BLOOM_ALL = "CreatorVerse:bloom:*"
    MASTER_ALL = "CreatorVerse:master:*"


class RedisConfig:
    """Redis configuration constants."""
    OTP_TTL = 900  # 15 minutes
    OAUTH_STATE_TTL = 300  # 5 minutes
    REFRESH_TOKEN_TTL = 7 * 24 * 3600  # 7 days
    ACCESS_TOKEN_TTL = 1200  # 20 minutes
    RBAC_TTL = 3600  # 1 hour
    USER_ROLES_TTL = 3600  # 1 hour
    OTP_MAX_ATTEMPTS = 5
    TENANT_DOMAIN_TTL = 7 * 24 * 3600  # 7 days
    MAX_SESSIONS_PER_USER = 4
    MASTER_AUTH_METHODS_TTL = 24 * 3600  # 24 hours
    USER_TTL = 7 * 24 * 3600  # 7 days
    DOMAIN_ORG_TTL = 3600  # 1 hour
    ORGANIZATION_MEMBERS_TTL = 1800  # 30 minutes
    USER_PROFILE_TTL = 3600  # 1 hour cache for user profile data
    WELCOME_EMAIL_TTL = 30 * 24 * 3600  # 30 days cache for welcome email tracking
