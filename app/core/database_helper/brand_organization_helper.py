from typing import Tu<PERSON>, Dict, Any, Optional
from uuid import UUID
import secrets
import string
from datetime import datetime, UTC
import re
import hashlib

from database_helper.database.models import <PERSON>, BrandMembership, DomainClaim, Organization, OrganizationMembership
from database_helper.database.sync_db2 import SyncDatabaseDB
from sqlalchemy import select
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.core.config import APP_CONFIG


def generate_organization_code(domain: str) -> str:
    """
    Generate a unique organization code based on domain.
    
    Args:
        domain: Domain name to generate code for
        
    Returns:
        A unique organization code string
    """
    try:
        # Clean domain name and create base code
        domain_clean = domain.lower().replace('.', '').replace('-', '')
        
        # Take first 6 characters of domain, pad if needed
        base_code = domain_clean[:6].ljust(6, 'x').upper()
        
        # Add random suffix for uniqueness
        random_suffix = secrets.token_hex(2).upper()
        
        organization_code = f"{base_code}{random_suffix}"
        
        APP_CONFIG.logger.debug(f"Generated organization code: {organization_code} for domain: {domain}")
        return organization_code
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Error generating organization code for domain {domain}: {str(e)}")
        # Fallback to random code
        fallback_code = f"ORG{secrets.token_hex(3).upper()}"
        return fallback_code


async def get_organization_by_domain_cache_aside(
    domain: str,
    redis_client,
    db_conn: SyncDatabaseDB
) -> Optional[Dict[str, Any]]:
    """
    Get organization by domain using cache-aside pattern.
    
    Args:
        domain: Domain to lookup
        redis_client: Redis client
        db_conn: Database connection
    
    Returns:
        Organization data or None if not found
    """
    cache_key = f"CreatorVerse:organization:domain:{domain}"
    
    # Try cache first
    try:
        cached_data = redis_client.get(cache_key)
        if cached_data:
            import json
            return json.loads(cached_data)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Cache error in get_organization_by_domain: {str(e)}")
    
    # Cache miss - query database
    with db_conn.transaction() as session:
        organization = session.query(Organization).filter(
            Organization.domain == domain,
            Organization.is_active == True,
            Organization.deleted_at.is_(None)
        ).first()
        
        if not organization:
            # Cache negative result for shorter time
            try:
                redis_client.setex(cache_key, 300, "null")  # 5 minutes
            except Exception as e:
                APP_CONFIG.logger.warning(f"Cache set error: {str(e)}")
            return None
        
        org_data = {
            "id": str(organization.id),
            "name": organization.name,
            "domain": organization.domain,
            "organization_code": organization.organization_code,
            "is_active": organization.is_active,
            "created_at": organization.created_at.isoformat() if organization.created_at else None
        }
        
        # Cache the result
        try:
            import json
            redis_client.setex(cache_key, 3600, json.dumps(org_data))  # 1 hour
        except Exception as e:
            APP_CONFIG.logger.warning(f"Cache set error: {str(e)}")
        
        return org_data


async def check_if_first_user_from_domain(
    domain: str,
    redis_client,
    db_conn: SyncDatabaseDB
) -> bool:
    """
    Check if this is the first user registering from a domain.
    
    Args:
        domain: Domain to check
        redis_client: Redis client
        db_conn: Database connection
    
    Returns:
        True if first user, False otherwise
    """
    organization = await get_organization_by_domain_cache_aside(domain, redis_client, db_conn)
    return organization is None


def check_if_first_user_from_domain_sync(session: Session, domain: str) -> bool:
    """
    Check if this is the first user registering from a specific domain.
    Returns True if no brands exist for this domain.
    """
    existing_brand = session.scalar(
        select(Brand).where(Brand.contact_email.contains(f"@{domain}"))
    )
    return existing_brand is None


def generate_brand_code(domain: str) -> str:
    """
    Generate a unique brand code based on domain.
    Format: first part of domain + random string
    """
    # Create a hash of domain + timestamp for uniqueness
    timestamp = str(int(datetime.now(UTC).timestamp()))
    hash_input = f"{domain}_{timestamp}"
    hash_digest = hashlib.md5(hash_input.encode()).hexdigest()[:8]
    
    # Use first part of domain + hash
    domain_part = domain.split('.')[0][:4].upper()
    return f"{domain_part}_{hash_digest.upper()}"


def create_brand_for_first_user(
    session: Session,
    user_id: UUID,
    email: str,
    domain: str
) -> Tuple[Brand, BrandMembership, DomainClaim]:
    """
    Create brand, membership, and automatically verified domain claim for the first user from a domain.
    
    Args:
        session: Database session
        user_id: User ID of the first user
        email: User's email
        domain: Domain extracted from email
        
    Returns:
        Tuple of (Brand, BrandMembership, DomainClaim)
    """
    try:
        # 1. Create brand with auto-generated name
        brand_name = domain.split('.')[0].title()  # e.g., "company.com" -> "Company"
        
        brand = Brand(
            name=brand_name,
            contact_email=email,
            is_active=True,
            created_by=user_id
        )
        session.add(brand)
        session.flush()  # Get the ID
        
        # 2. Create brand membership with brand-admin role
        membership = BrandMembership(
            brand_id=brand.id,
            user_id=user_id,
            role="brand_admin",  # First user from domain gets brand-admin role
            status="active"
        )
        session.add(membership)
        
        # 3. Create automatically verified domain claim for first user
        domain_claim = DomainClaim(
            domain=domain,
            verification_token=secrets.token_urlsafe(32),  # Generate token but mark as verified
            claimed_by_user_id=user_id,
            verified_at=datetime.now(UTC)  # Automatically verify for first user
        )
        session.add(domain_claim)
        
        APP_CONFIG.logger.info(
            f"Created brand '{brand_name}' with auto-verified domain claim for first user {user_id} from domain {domain}"
        )
        
        return brand, membership, domain_claim
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to create brand for first user: {str(e)}")
        raise
