"""
Brand helper utility for brand-related database operations.
Includes functions for creating brands, managing brand memberships, and brand requests.
"""
import json
from datetime import datetime, UTC
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, select
from sqlalchemy.exc import IntegrityError

from database_helper.database.models import (
    Brand, 
    BrandMembership, 
    User
)
from database_helper.database.sync_db2 import SyncDatabaseDB
from app.core.config import APP_CONFIG, get_locobuzz_redis, get_database

# Cache TTL constants
BRAND_CACHE_TTL = 3600  # 1 hour
BRAND_LIST_CACHE_TTL = 1800  # 30 minutes


async def get_brand_by_id_cache_aside(
    brand_id: str,
    redis_client=get_locobuzz_redis(),
    db_conn: SyncDatabaseDB = get_database()
) -> Optional[Dict[str, Any]]:
    """
    Get brand by ID using cache-aside pattern.
    First checks Redis, then falls back to database if not found.
    
    Args:
        brand_id: UUID of the brand
        redis_client: Redis client instance
        db_conn: Database connection
        
    Returns:
        Dictionary with brand data or None if not found
    """
    # Check Redis cache first
    brand_key = f"CreatorVerse:brand:{brand_id}"
    brand_data = redis_client.get(brand_key)
    
    if brand_data:
        return json.loads(str(brand_data))
    
    # If not in cache, query from database
    with db_conn.transaction() as session:
        brand = session.query(Brand).filter(
            Brand.id == UUID(brand_id),
            Brand.deleted_at.is_(None)
        ).first()
        
        if not brand:
            return None
        
        # Cache the brand data
        brand_dict = {
            "id": str(brand.id),
            "name": brand.name,
            "description": brand.description,
            "logo_url": brand.logo_url,
            "website_url": brand.website_url,
            "contact_email": brand.contact_email,
            "is_active": brand.is_active,
            "created_at": brand.created_at.isoformat() if brand.created_at else None,
            "created_by": str(brand.created_by) if brand.created_by else None
        }
        
        redis_client.setex(brand_key, BRAND_CACHE_TTL, json.dumps(brand_dict))
        
        return brand_dict


async def get_user_brands_cache_aside(
    user_id: str,
    include_inactive: bool = False,
    redis_client=get_locobuzz_redis(),
    db_conn: SyncDatabaseDB = get_database()
) -> List[Dict[str, Any]]:
    """
    Get all brands that a user is a member of using cache-aside pattern.
    
    Args:
        user_id: UUID of the user
        include_inactive: Whether to include inactive brands
        redis_client: Redis client instance
        db_conn: Database connection
        
    Returns:
        List of brand dictionaries with membership role
    """
    # Construct cache key including inactive flag
    cache_key = f"CreatorVerse:user:brands:{user_id}"
    if include_inactive:
        cache_key += ":all"
    
    # Check Redis cache first
    brands_data = redis_client.get(cache_key)
    
    if brands_data:
        return json.loads(str(brands_data))
    
    # If not in cache, query from database
    with db_conn.transaction() as session:
        # Join Brand and BrandMembership to get user's brands with roles
        query = (
            session.query(
                Brand,
                BrandMembership.role
            )
            .join(
                BrandMembership,
                and_(
                    BrandMembership.brand_id == Brand.id,
                    BrandMembership.user_id == UUID(user_id),
                    BrandMembership.status == 'active'
                )
            )
            .filter(
                Brand.deleted_at.is_(None)
            )
        )
        
        if not include_inactive:
            query = query.filter(Brand.is_active.is_(True))
        
        results = query.all()
        
        brand_list = []
        for brand, role in results:
            brand_dict = {
                "id": str(brand.id),
                "name": brand.name,
                "description": brand.description,
                "logo_url": brand.logo_url,
                "website_url": brand.website_url,
                "contact_email": brand.contact_email,
                "is_active": brand.is_active,
                "created_at": brand.created_at.isoformat() if brand.created_at else None,
                "membership_role": role
            }
            brand_list.append(brand_dict)
        
        # Cache the brands data
        redis_client.setex(cache_key, BRAND_LIST_CACHE_TTL, json.dumps(brand_list))
        
        return brand_list


async def create_brand_for_user(
    user_id: str,
    brand_data: Dict[str, Any],
    redis_client=get_locobuzz_redis(),
    db_conn: SyncDatabaseDB = get_database()
) -> Dict[str, Any]:
    """
    Create a new brand with proper validation and cache management.
    
    Args:
        user_id: UUID of the user creating the brand
        brand_data: Dictionary containing brand information (name is required)
        redis_client: Redis client for cache operations
        db_conn: Database connection
    
    Returns:
        Dictionary containing created brand details
    
    Raises:
        ValueError: If validation fails
    """
    try:
        with db_conn.transaction() as session:
            # Check for duplicate brand name
            existing_brand = session.query(Brand).filter(
                Brand.name == brand_data["name"],
                Brand.deleted_at.is_(None)
            ).first()
            
            if existing_brand:
                raise ValueError(f"Brand with name '{brand_data['name']}' already exists")
            
            # Create the brand
            brand = Brand(
                name=brand_data["name"],
                description=brand_data.get("description"),
                logo_url=brand_data.get("logo_url"),
                website_url=brand_data.get("website_url"),
                contact_email=brand_data.get("contact_email"),
                created_by=UUID(user_id),
                is_active=True,
                created_at=datetime.now(UTC)
            )
            
            session.add(brand)
            session.flush()  # Get the brand ID
            
            # Create brand membership for the creator as brand_admin
            brand_membership = BrandMembership(
                brand_id=brand.id,
                user_id=UUID(user_id),
                role="brand_admin",
                status="active",
                joined_at=datetime.now(UTC)
            )
            
            session.add(brand_membership)
            session.commit()
            
            # Prepare response data
            brand_response = {
                "id": str(brand.id),
                "name": brand.name,
                "description": brand.description,
                "logo_url": brand.logo_url,
                "website_url": brand.website_url,
                "contact_email": brand.contact_email,
                "is_active": brand.is_active,
                "created_at": brand.created_at.isoformat(),
                "creator_role": "brand_admin"
            }
            
            # Invalidate relevant caches
            try:
                cache_keys_to_invalidate = [
                    f"CreatorVerse:user:brands:{user_id}",
                ]
                
                for cache_key in cache_keys_to_invalidate:
                    redis_client.delete(cache_key)
                
                APP_CONFIG.logger.info(f"Invalidated caches for new brand: {brand.name}")
            except Exception as cache_error:
                APP_CONFIG.logger.warning(f"Cache invalidation warning: {str(cache_error)}")
            
            return brand_response
            
    except IntegrityError as e:
        APP_CONFIG.logger.error(f"Database integrity error creating brand: {str(e)}")
        raise ValueError("Brand name must be unique")
    except Exception as e:
        APP_CONFIG.logger.error(f"Error creating brand: {str(e)}")
        raise


def invalidate_brand_caches(
    redis_client,
    brand_id: Optional[str] = None,
    user_id: Optional[str] = None
) -> None:
    """
    Invalidate brand-related caches.
    Can target brand-level or user-level caches.
    
    Args:
        redis_client: Redis client instance
        brand_id: Optional brand ID to invalidate a specific brand
        user_id: Optional user ID to invalidate user's brand memberships
    """
    keys_to_delete = []
    
    if brand_id:
        # Delete specific brand cache
        keys_to_delete.append(f"CreatorVerse:brand:{brand_id}")
    
    if user_id:
        # Delete user's brands cache
        keys_to_delete.append(f"CreatorVerse:user:brands:{user_id}")
        keys_to_delete.append(f"CreatorVerse:user:brands:{user_id}:all")
    
    # Delete all collected keys
    for key in keys_to_delete:
        try:
            redis_client.delete(key)
        except Exception as e:
            APP_CONFIG.logger.warning(f"Failed to invalidate brand cache key {key}: {str(e)}")
