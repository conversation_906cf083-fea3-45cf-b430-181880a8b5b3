import asyncio
import time
from datetime import datetime, UTC
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>
from uuid import UUID

import redis
from database_helper.database.models import User, Organization, UserOTP, UserSession
from database_helper.database.models import User<PERSON><PERSON><PERSON>ethod
from database_helper.database.models import UserRoleModel
from database_helper.database.sync_db2 import SyncDatabaseDB
from fastapi import Request, status
from sqlalchemy import select, update, func, insert, exc, and_
from sqlalchemy.orm.session import Session

from app.core.config import APP_CONFIG
from app.core.config import TOTAL_MEMBERS, get_locobuzz_redis, get_database
from app.core.enums_data import UserRoleEnum
from app.core.exceptions import (
    EmailNotFoundError,
    BusinessLogicError,
    ServiceUnavailableError
)
from app.core.redis_keys import RedisKeys, RedisConfig
from app.core.security import hash_otp
from app.schemas import ClientInfo
from app.schemas.auth import OAuthTokenResponse
from app.schemas.auth_brand import BrandRegistrationResponse
from app.utilities.email_service import get_email_service
from app.utilities.jwt_oauth_utilities import issue_tokens_for_user, issue_tokens_for_user_with_uuid_role
from app.utilities.validation_functions import generate_otp

# Declaring the global tables
user_sessions_t = UserSession.__table__


async def create_user_role(session: Session, user_id: str, target_role_id: str) -> None:
    """
    Create user role entry with duplicate check to prevent constraint violations.
    
    Args:
        session: Database session
        user_id: User ID as string
        target_role_id: Role ID as string
    """
    # Check if role assignment already exists
    existing_role = session.query(UserRoleModel).filter_by(
        user_id=user_id,
        role_id=target_role_id
    ).first()

    if existing_role:
        APP_CONFIG.logger.info(
            f"Role {target_role_id} already assigned to user {user_id}, skipping duplicate assignment")
        return

    # Create new role assignment
    user_role = UserRoleModel(
        user_id=user_id,
        role_id=target_role_id,
        assigned_at=datetime.now(UTC)
    )
    session.add(user_role)
    APP_CONFIG.logger.info(f"Assigned role {target_role_id} to user {user_id}")


async def get_or_create_organization(session: Session, email: str) -> tuple[Organization, bool]:
    """
    Create a new organization in the database.
    """
    # Extract domain and name from the email
    domain = email.split('@')[-1]
    name = email.split('@')[0].replace('.', ' ').title()
    if domain:
        name = domain.split('.')[0].capitalize()

    existing_org = session.scalar(
        select(Organization).where(Organization.domain == domain)
    )
    if existing_org:
        # Check if Organization model has filled_members attribute
        if hasattr(Organization, 'filled_members'):
            update_stmt = update(Organization).where(
                ((Organization.id == existing_org.id) & (Organization.is_active.is_(True)))
            ).values(
                filled_members=Organization.filled_members + 1,  # Increment
                updated_at=func.now()  # Update the timestamp
            ).returning(Organization)
            # Execute the update and get the freshly updated organization object
            updated_org = session.scalar(update_stmt)
            return updated_org, True
        else:
            # If filled_members doesn't exist, just update the timestamp
            update_stmt = update(Organization).where(
                ((Organization.id == existing_org.id) & (Organization.is_active.is_(True)))
            ).values(
                updated_at=func.now()  # Update the timestamp
            ).returning(Organization)
            updated_org = session.scalar(update_stmt)
            return updated_org, True
    else:
        # Generate organization code using the helper function
        from app.core.database_helper.brand_organization_helper import generate_organization_code
        org_code = generate_organization_code(domain)

        # Prepare base organization data
        org_data = {
            "domain": domain,
            "name": name,
            "organization_code": org_code,
            "is_active": 1,
        }

        # Add optional fields if they exist in the model
        if hasattr(Organization, 'total_members'):
            org_data["total_members"] = TOTAL_MEMBERS
        if hasattr(Organization, 'filled_members'):
            org_data["filled_members"] = 1

        insert_stmt = insert(Organization).values(**org_data).returning(Organization)

        try:
            # session.scalar() executes the INSERT and fetches the returned Organization object.
            new_org = session.scalar(insert_stmt)
            session.add(new_org)
            return new_org, False
        except exc.IntegrityError:
            session.rollback()
            APP_CONFIG.logger.warning(
                f"Concurrency conflict: Organization with domain '{domain}' was created concurrently. Re-fetching.")
            return session.scalar(
                select(Organization).where(Organization.domain == domain)
            ), True


async def create_new_user(
        db_conn: SyncDatabaseDB,
        email: str,
        role: int,
        **kwargs
):
    user_data = {
        "is_email_verified": 1,
        "is_active": 1,
        "last_login_at": datetime.now(UTC),
        "status": "active",
        "updated_at": datetime.now(UTC),
    }
    redis_client = get_locobuzz_redis()
    with db_conn.transaction() as session:
        # Check if not the influencer role we need to create an organization and need the ref in the user table
        if role != UserRoleEnum.INFLUENCER.value:
            organization_data, is_exists_org = await get_or_create_organization(session, email)
            user_data["organization_id"] = organization_data.id

        # Create a new user instance
        new_user = await create_user_only(session, email, user_data)

        # create user_auth_methods entry
        await create_user_oauth(session, new_user.id, **kwargs)

        # Assign the role to the user
        token_data, target_role_id = issue_tokens_for_user(new_user.id, redis_client, session, role, **kwargs)

        # Create user role entry
        await create_user_role(session, new_user.id, target_role_id)

    return token_data


async def create_user_only(session: Session, email, user_data: dict, redis_client=get_locobuzz_redis(),
                           **kwargs) -> User:
    """
    Create a user without organization.
    """
    # first need to check in the cache if the user already exists using the redis key need redis code for that
    redis_key = RedisKeys.user_by_email(email)
    cached_user = redis_client.hgetall(redis_key)
    if cached_user:
        # Return existing user from cache, but ensure it's up to date
        user = session.query(User).filter_by(email=email).first()
        if user:
            for key, value in user_data.items():
                if hasattr(user, key):
                    setattr(user, key, value)
            session.add(user)
            return user

    # 1️⃣ Try to load existing
    user = session.query(User).filter_by(email=email).one_or_none()
    if user:
        # If user exists, update the user data
        for key, value in user_data.items():
            if hasattr(user, key):
                setattr(user, key, value)
        session.add(user)
        return user

    # 2️⃣ Not found → create new user
    # For registration requests, set status as "requested" initially
    request_type = kwargs.get("request_type", "")
    if request_type == "register":
        user_data["is_email_verified"] = 0
        user_data["is_active"] = 0
        user_data["status"] = "requested"

    new_user = User(email=email, **user_data)
    session.add(new_user)
    session.flush()  # populates new_user.id
    return new_user


async def create_user_oauth(
        session,
        user_id: str,
        redis_client,
        register_channel: str = "oauth_google",
        auth_method_id: str | None = None
) -> None:
    """
    Create user OAuth auth method entry with proper auth_method_id.
    
    Args:
        session: Database session
        user_id: User ID
        redis_client: Redis client for caching
        register_channel: Auth method key (for fallback lookup)
        auth_method_id: Auth method ID (required)
    """

    if not auth_method_id:
        # Fallback: try to get auth_method_id using cache-aside pattern
        auth_method_id = await get_auth_method_id_cache_aside(register_channel, redis_client, session)

        if not auth_method_id:
            raise ValueError(f"Auth method ID not found for: {register_channel}")

    # Create the user auth method record
    user_auth_method = UserAuthMethod(
        user_id=user_id,
        auth_method_id=auth_method_id,
        is_enabled=True,
        enabled_at=datetime.now(UTC),
        disabled_at=None
    )

    session.add(user_auth_method)
    APP_CONFIG.logger.info(f"Created user auth method for user {user_id} with method {auth_method_id}")


async def get_auth_method_id_cache_aside(method_key: str, redis_client, session) -> str | None:
    """
    Get auth method ID using cache-aside pattern.
    
    Args:
        method_key: The auth method key to look up
        redis_client: Redis client
        session: Database session
        
    Returns:
        The auth method ID as string or None if not found
    """
    from database_helper.database.models import MasterAuthMethod
    from app.core.redis_keys import RedisConfig
    from app.core.config import APP_CONFIG

    # 1. Try to get from Redis cache first
    auth_methods_key = "CreatorVerse:auth:methods"

    try:
        cached_auth_method_id = redis_client.hget(auth_methods_key, method_key)

        if cached_auth_method_id:
            APP_CONFIG.logger.debug(f"Auth method {method_key} found in cache")
            return str(cached_auth_method_id)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Redis error while fetching auth method: {e}")

    # 2. Cache miss - get from database and populate cache
    try:
        # Get all auth methods to populate cache
        auth_methods = session.query(MasterAuthMethod).all()

        if not auth_methods:
            APP_CONFIG.logger.warning("No auth methods found in database")
            return None

        # Build cache dictionary
        auth_methods_dict = {str(method.method_key): str(method.id) for method in auth_methods}

        # Store in Redis
        if auth_methods_dict:
            try:
                redis_client.hset(auth_methods_key, mapping=auth_methods_dict)
                redis_client.expire(auth_methods_key, RedisConfig.RBAC_TTL)
                APP_CONFIG.logger.debug(f"Cached {len(auth_methods_dict)} auth methods")
            except Exception as cache_error:
                APP_CONFIG.logger.warning(f"Failed to cache auth methods: {cache_error}")

        # Return the requested auth method ID
        found_id = auth_methods_dict.get(str(method_key))
        return str(found_id) if found_id else None

    except Exception as e:
        APP_CONFIG.logger.error(f"Error fetching auth method from database: {str(e)}")
        return None


# DEPRECATED: This function has been replaced by CentralizedOTPService
# Use app.services.centralized_otp_service.send_email_with_retry_and_cleanup_centralized instead


# DEPRECATED: This function has been replaced by CentralizedOTPService  
# Use CentralizedOTPService._check_otp_resend_status() instead
def check_existing_otp_status(
        redis_client,
        email: str
) -> Tuple[bool, Optional[int], str]:
    """
    DEPRECATED: Check if OTP already exists for given tenant_id and email.
    Use CentralizedOTPService._check_otp_resend_status() instead.
    
    Returns:
        Tuple[bool, Optional[int], str]:
        - has_valid_otp: True if valid OTP exists
        - remaining_seconds: Seconds remaining for current OTP (None if no OTP)
        - message: User-friendly message
    """
    otp_key = RedisKeys.otp_key(email)

    try:
        existing_otp_data = redis_client.hgetall(otp_key)

        if not existing_otp_data:
            return False, None, "No existing OTP found"

        generated_at = int(existing_otp_data.get("generated_at", 0))
        current_time = int(time.time())
        elapsed_time = current_time - generated_at

        if elapsed_time >= RedisConfig.OTP_TTL:
            # OTP expired, clean it up
            redis_client.delete(otp_key)
            APP_CONFIG.logger.info(f"Expired OTP cleaned up for email: {email}")
            return False, None, "Previous OTP expired and cleaned up"

        remaining_seconds = RedisConfig.OTP_TTL - elapsed_time
        return True, remaining_seconds, f"OTP already exists. {remaining_seconds} seconds remaining"

    except redis.RedisError as e:
        APP_CONFIG.logger.error(f"Redis error checking OTP status for {email}: {str(e)}")
        # If Redis fails, assume no OTP exists and continue
        return False, None, "Redis unavailable, proceeding with new OTP"
    except Exception as e:
        APP_CONFIG.logger.error(f"Unexpected error checking OTP status for {email}: {str(e)}")
        return False, None, "Error checking OTP status"


def cleanup_expired_otp(redis_client, email: str) -> bool:
    """
    Clean up expired OTP for given  email.
    
    Returns:
        bool: True if cleanup was successful or no OTP existed
    """
    otp_key = RedisKeys.otp_key(email)

    try:
        existing_otp_data = redis_client.hgetall(otp_key)

        if not existing_otp_data:
            return True

        generated_at = int(existing_otp_data.get("generated_at", 0))
        current_time = int(time.time())
        elapsed_time = current_time - generated_at

        if elapsed_time >= RedisConfig.OTP_TTL:
            redis_client.delete(otp_key)
            APP_CONFIG.logger.info(f"Expired OTP cleaned up for email: {email}")

        return True

    except redis.RedisError as e:
        APP_CONFIG.logger.error(f"Redis error during OTP cleanup for {email}: {str(e)}")
        return False
    except Exception as e:
        APP_CONFIG.logger.error(f"Unexpected error during OTP cleanup for {email}: {str(e)}")
        return False


def get_otp_retry_info(redis_client, email: str) -> Dict[str, int]:
    """
    Get OTP retry information including failed attempts and remaining time.
    
    Returns:
        Dict[str, int]: Contains 'failed_attempts', 'remaining_seconds', 'max_attempts'
    """
    otp_key = RedisKeys.otp_key(email)

    try:
        existing_otp_data = redis_client.hgetall(otp_key)

        if not existing_otp_data:
            return {
                'failed_attempts': 0,
                'remaining_seconds': 0,
                'max_attempts': RedisConfig.OTP_MAX_ATTEMPTS
            }

        generated_at = int(existing_otp_data.get("generated_at", 0))
        failed_attempts = int(existing_otp_data.get("failed_attempts", 0))
        current_time = int(time.time())
        elapsed_time = current_time - generated_at
        remaining_seconds = max(0, RedisConfig.OTP_TTL - elapsed_time)

        return {
            'failed_attempts': failed_attempts,
            'remaining_seconds': remaining_seconds,
            'max_attempts': RedisConfig.OTP_MAX_ATTEMPTS
        }

    except redis.RedisError as e:
        APP_CONFIG.logger.error(f"Redis error getting OTP retry info for {email}: {str(e)}")
        return {
            'failed_attempts': 0,
            'remaining_seconds': 0,
            'max_attempts': RedisConfig.OTP_MAX_ATTEMPTS
        }
    except Exception as e:
        APP_CONFIG.logger.error(f"Unexpected error getting OTP retry info for {email}: {str(e)}")
        return {
            'failed_attempts': 0,
            'remaining_seconds': 0,
            'max_attempts': RedisConfig.OTP_MAX_ATTEMPTS
        }


async def get_client_info(request: Request) -> ClientInfo:
    """
    FastAPI dependency to extract client IP address and User-Agent from the request.

    Note: For accurate client IP behind reverse proxies (e.g., Nginx, Cloudflare),
    ensure Uvicorn is started with:
    `uvicorn main:app --proxy-headers --forwarded-allow-ips <YOUR_PROXY_IP_OR_IPS>`
    """

    # Get IP Address
    client_ip: Optional[str] = None
    if request.client:
        client_ip = request.client.host

    # Get User-Agent
    user_agent_header: Optional[str] = request.headers.get("user-agent")

    return ClientInfo(
        ip_address=client_ip,
        user_agent=user_agent_header
    )


# DEPRECATED: This function has been replaced by CentralizedOTPService
# Use CentralizedOTPService._generate_and_store_otp() instead 
async def generate_and_store_otp(
        user_email: str,
        is_db: bool,
        redis_client=get_locobuzz_redis(),
        **kwargs
) -> str:
    """
    DEPRECATED: Generate a new OTP hash it, store it in Redis, and return the OTP and its TTL.
    Use CentralizedOTPService._generate_and_store_otp() instead.

    Returns:
        otp in the str.
    """
    otp = generate_otp()
    otp_hash = hash_otp(otp)

    otp_key = RedisKeys.otp_key(user_email)
    otp_data = {
        "email": user_email,
        "otp_hash": otp_hash,
        "generated_at": int(time.time()),
        "failed_attempts": 0,
        "role": kwargs.get("role"),
        "source_in_user": kwargs.get("register_channel", 0),  # 0 means are not passed properly
        "request_type": kwargs.get("request_type", ""),  # e.g., "login", "register"
        "register_channel": kwargs.get("source_register", ""),  # e.g., "email_otp", "phone_otp"
    }

    redis_client.hset(otp_key, mapping=otp_data)
    redis_client.expire(otp_key, RedisConfig.OTP_TTL)
    if is_db:
        asyncio.ensure_future(create_otp_entry(user_email, otp_hash, **kwargs))
    return otp


# need to create or update entry in the otp table
async def create_otp_or_update_entry(user_email, otp_data: dict, session: Session,
                                     redis_client=get_locobuzz_redis()) -> User:
    """
    Getting user data by email within the current session context.
    Need the user id to create or update the OTP entry in the database.
    """
    # Query user within the current session to avoid detachment issues
    user_data = session.query(User).filter_by(email=user_email).one_or_none()
    if not user_data:
        redis_client.delete(RedisKeys.otp_key(user_email))
        raise EmailNotFoundError(user_email)

    if not user_data.is_active:
        redis_client.delete(RedisKeys.otp_key(user_email))
        raise BusinessLogicError("User account is not active", status.HTTP_403_FORBIDDEN)

    # Updating the otp row
    data = {
        "consumed_at": datetime.now(UTC),
        "failed_attempts": otp_data.get("failed_attempts", 0),
        "is_active": False,
        "channel": otp_data.get("register_channel", ""),
    }
    rows = (
        session.query(UserOTP)
        .filter_by(hashed_code=otp_data.get("otp_hash"), user_id=user_data.id)
        .update(
            data,
            synchronize_session="fetch"
        )
    )
    print("Updated the user otp table rows:", rows)
    return user_data


async def create_otp_entry(user_email: str, otp_hash: str, db_conn: SyncDatabaseDB = get_database(), **kwargs) -> None:
    with db_conn.transaction() as session:
        if kwargs.get("request_type") == "login":
            print("Creating OTP for existing user only")

            user = session.query(User).filter_by(email=user_email).one_or_none()
            otp_data = UserOTP(
                user_id=user.id,
                hashed_code=otp_hash,
                failed_attempts=0,
                lockout_until=None,
                is_active=True,
                channel=kwargs.get("register_channel"),
            )
            session.add(otp_data)
        else:
            # For registration, create user with "requested" status
            user_data = {
                "is_email_verified": 0,
                "is_active": 0,
                "register_source": kwargs.get("register_channel", 0),
                "status": "requested",
            }
            await create_user_only(session, user_email, user_data)


# User Session Queries
async def active_sessions_count(user_id: str, db_conn: SyncDatabaseDB = get_database()) -> Optional[int]:
    """
    Build a Core SELECT COUNT(*) … WHERE user_id = :user_id
                              AND is_revoked = FALSE
                              AND expires_at > NOW()
    """
    stmt = select(func.count()).select_from(user_sessions_t).where(
        and_(
            user_sessions_t.c.user_id == user_id,
            user_sessions_t.c.is_revoked == False,
            user_sessions_t.c.expires_at > func.now(),
        )
    )

    result = db_conn.execute_query2(stmt)

    count = result.scalar_one_or_none()
    return count


async def get_active_sessions(user_id: str, db_conn: SyncDatabaseDB = get_database()) -> list[UserSession]:
    """
    Build a Core SELECT * … WHERE user_id = :user_id
                              AND is_revoked = FALSE
                              AND expires_at > NOW()
    """
    stmt = (
        select(user_sessions_t)
        .where(
            and_(
                user_sessions_t.c.user_id == user_id,
                user_sessions_t.c.is_revoked == False,
                user_sessions_t.c.expires_at > func.now(),
            )
        )
    )
    result = db_conn.execute_query2(stmt)

    return result.fetchall() if result else []


async def get_session_by_session_id(session_id: str, db_conn: SyncDatabaseDB = get_database()) -> Optional[UserSession]:
    """
    Build a Core SELECT * … WHERE id = :session_id
    """
    stmt = select(user_sessions_t).where(user_sessions_t.c.id == session_id)
    result = db_conn.execute_query2(stmt)

    return result.scalar_one_or_none() if result else None


async def get_session_by_user_id(user_id: str, db_conn: SyncDatabaseDB = get_database()) -> list[UserSession]:
    """
    Build a Core SELECT * … WHERE user_id = :user_id
                              AND is_revoked = FALSE
                              AND expires_at > NOW()
    """
    stmt = (
        select(user_sessions_t)
        .where(
            and_(
                user_sessions_t.c.user_id == user_id,
                user_sessions_t.c.is_revoked == False,
                user_sessions_t.c.expires_at > func.now(),
            )
        )
    )
    result = db_conn.execute_query2(stmt)

    return result.fetchall() if result else []


async def revoke_session_by_session_id(session_id: str, db_conn: SyncDatabaseDB = get_database()) -> bool:
    """
    Build a Core UPDATE user_sessions SET is_revoked = TRUE WHERE id = :session_id
    """
    stmt = (
        update(user_sessions_t)
        .where(user_sessions_t.c.id == session_id)
        .values(is_revoked=True)
    )
    try:
        db_conn.execute_query2(stmt)
        return True
    except exc.SQLAlchemyError as e:
        APP_CONFIG.logger.error(f"Failed to revoke session {session_id}: {str(e)}")
        return False


async def revoke_session_by_user_id(user_id: str, db_conn: SyncDatabaseDB = get_database()) -> bool:
    """
    Build a Core UPDATE user_sessions SET is_revoked = TRUE WHERE user_id = :user_id
    """
    stmt = (
        update(user_sessions_t)
        .where(user_sessions_t.c.user_id == user_id)
        .values(is_revoked=True)
    )
    try:
        db_conn.execute_query2(stmt)
        return True
    except exc.SQLAlchemyError as e:
        APP_CONFIG.logger.error(f"Failed to revoke sessions for user {user_id}: {str(e)}")
        return False


async def select_oldest_active_session_by_user_id(user_id: str, session_count: int,
                                                  db_conn: SyncDatabaseDB = get_database()) -> list[dict]:
    """
    Build a Core SELECT … WHERE user_id = :user_id
                          AND is_revoked = FALSE
                          AND expires_at > NOW()
                     ORDER BY issued_at ASC
                     LIMIT 1
    """
    stmt = (
        select(
            user_sessions_t.c.id,
            user_sessions_t.c.refresh_token
        )
        .select_from(user_sessions_t)
        .where(
            and_(
                user_sessions_t.c.user_id == user_id,
                user_sessions_t.c.is_revoked == False,
                user_sessions_t.c.expires_at > func.now(),
            )
        )
        .order_by(user_sessions_t.c.issued_at.asc())
        .limit(session_count)
    )
    result = db_conn.execute_query2(stmt)
    return [{"id": row.id, "refresh_token": row.refresh_token} for row in result.fetchall()]


async def enforce_max_sessions(user_id: str, redis_client, db_conn: SyncDatabaseDB = get_database()) -> None:
    """
    Enforce maximum session limit per user by revoking oldest sessions if needed.
    
    Args:
        user_id: User ID to check sessions for
        redis_client: Redis client instance
        db_conn: Database connection
    """
    try:
        # Count active sessions
        active_count = await active_sessions_count(user_id, db_conn)

        if active_count and active_count >= RedisConfig.MAX_SESSIONS_PER_USER:
            # Calculate how many sessions to revoke
            sessions_to_revoke = active_count - RedisConfig.MAX_SESSIONS_PER_USER + 1

            APP_CONFIG.logger.info(
                f"User {user_id} has {active_count} sessions. Revoking {sessions_to_revoke} oldest sessions")

            # Get oldest sessions to revoke
            oldest_sessions = await select_oldest_active_session_by_user_id(user_id, sessions_to_revoke, db_conn)

            # Revoke sessions in both DB and Redis
            for session in oldest_sessions:
                await revoke_single_session(session['id'], session['refresh_token'], user_id, redis_client, db_conn)

    except Exception as e:
        APP_CONFIG.logger.error(f"Error enforcing max sessions for user {user_id}: {str(e)}")


async def revoke_single_session(session_id: str, refresh_token: str, user_id: str, redis_client,
                                db_conn: SyncDatabaseDB = get_database()) -> bool:
    """
    Revoke a single session in both database and Redis.
    
    Args:
        session_id: Session ID to revoke
        refresh_token: Refresh token to clean from Redis
        user_id: User ID for Redis key construction
        redis_client: Redis client instance
        db_conn: Database connection
    """
    try:
        # Revoke in database
        db_success = await revoke_session_by_session_id(session_id, db_conn)

        if db_success:
            # Clean up Redis keys
            try:
                # Remove session data
                session_key = RedisKeys.session_key(user_id, session_id)
                redis_client.delete(session_key)

                # Remove refresh token data (need tenant_id for this)
                # Note: You might need to get tenant_id from session or pass it as parameter

                APP_CONFIG.logger.info(f"Successfully revoked session {session_id} for user {user_id}")
                return True

            except Exception as redis_error:
                APP_CONFIG.logger.error(f"Failed to clean Redis for session {session_id}: {str(redis_error)}")
                # Continue as DB revocation succeeded
                return True

        return False

    except Exception as e:
        APP_CONFIG.logger.error(f"Error revoking session {session_id}: {str(e)}")
        return False


async def get_user_by_email_cache_aside(user_email: str, db_conn: SyncDatabaseDB = get_database()) -> Optional[dict]:
    """
    Cache-aside lookup for user by email.
    First checks Redis, then falls back to a database if not found.
    Returns user data as dictionary to avoid session detachment issues.
    """
    # Check Redis cache first
    redis_client = get_locobuzz_redis()
    redis_key = RedisKeys.user_by_email(user_email)
    cached_user = redis_client.hgetall(redis_key)

    if cached_user:
        return cached_user

    # If not found in cache, query the database
    with db_conn.transaction() as session:
        user = session.query(User).filter_by(email=user_email).one_or_none()
        if user:
            # Save in the cache
            user_dict = {
                "id": str(user.id),
                "email": user.email,
                "phone_number": user.phone_number or "",
                "status": user.status or "",
                "is_active": str(user.is_active),
                "is_email_verified": str(user.is_email_verified),
            }
            redis_client.hset(redis_key, mapping=user_dict)
            redis_client.expire(redis_key, RedisConfig.USER_TTL)
            return user_dict

    return None


async def create_new_user_with_uuid_role(
        db_conn: SyncDatabaseDB,
        email: str,
        role_uuid: str,
        **kwargs
) -> OAuthTokenResponse:
    """
    Create a new user with UUID role and handle brand organization creation.
    First user from domain gets auto-verified organization.
    Handles both new user creation and existing user activation scenarios.
    """
    from app.utilities.bloom_filter import get_bloom_filter_manager
    redis_client = get_locobuzz_redis()
    try:
        with db_conn.transaction() as session:
            # Extract domain from email
            domain = email.split('@')[-1].lower()

            # Convert register_channel to integer for register_source
            register_channel = kwargs.get("register_channel", 1)
            if isinstance(register_channel, str):
                # Map string values to integers using enum
                from app.core.enums_data import SourceRegister
                channel_mapping = {
                    "email_otp": SourceRegister.EMAIL_OTP.value,
                    "phone_otp": SourceRegister.PHONE_OTP.value,
                    "oauth_google": SourceRegister.GOOGLE_OAUTH.value,
                    "oauth_instagram": SourceRegister.INSTAGRAM_OAUTH.value,
                    "oauth_facebook": SourceRegister.FACEBOOK_OAUTH.value
                }
                register_source = channel_mapping.get(register_channel, SourceRegister.EMAIL_OTP.value)
            else:
                register_source = register_channel

            # Filter out non-JSON serializable data for metadata_json
            metadata_json = {k: v for k, v in kwargs.items()
                             if k not in ['ip_address', 'user_agent', 'device_info', 'redis_client'] and v is not None}

            # Check if user already exists (from OTP request phase)
            existing_user = session.query(User).filter_by(email=email).first()

            if existing_user:
                # Update existing user from "requested" to "active" status
                existing_user.status = "active"
                existing_user.is_email_verified = True
                existing_user.is_active = True
                existing_user.register_source = register_source
                existing_user.metadata_json = metadata_json
                existing_user.updated_at = datetime.now(UTC)

                session.add(existing_user)
                session.flush()
                new_user = existing_user
                APP_CONFIG.logger.info(f"Updated existing user {email} from requested to active status")
            else:
                # Create new user (fallback scenario)
                new_user = User(
                    email=email,
                    is_email_verified=True,
                    status="active",
                    register_source=register_source,
                    metadata_json=metadata_json
                )
                session.add(new_user)
                session.flush()
                APP_CONFIG.logger.info(f"Created new user {email}")

            # Check if user role already exists
            existing_role = session.query(UserRoleModel).filter_by(
                user_id=new_user.id,
                role_id=role_uuid
            ).first()

            if not existing_role:
                # Create user role assignment
                user_role = UserRoleModel(
                    user_id=new_user.id,
                    role_id=role_uuid
                )
                session.add(user_role)
                APP_CONFIG.logger.info(f"Assigned role {role_uuid} to user {email}")

            # Get role name for logging and organization creation logic
            roles_cache = redis_client.hgetall(RedisKeys.rbac_roles()) if redis_client else {}
            role_name = roles_cache.get(role_uuid)

            if not role_name:
                # Fallback to database lookup if not in cache
                from database_helper.database.models import MasterRole
                role = session.query(MasterRole).filter_by(id=role_uuid).first()
                if role:
                    role_name = role.role_name
                    # Update cache for future use
                    if redis_client:
                        redis_client.hset(RedisKeys.rbac_roles(), mapping={role_uuid: role_name})
                        redis_client.expire(RedisKeys.rbac_roles(), RedisConfig.RBAC_TTL)
                else:
                    APP_CONFIG.logger.warning(f"Role with UUID {role_uuid} not found")
                    role_name = "unknown"

            # If it's a brand role, handle organization creation WITHIN THE SAME TRANSACTION
            if role_name and 'brand' in role_name.lower():
                from app.core.database_helper.brand_organization_helper import (
                    check_if_first_user_from_domain
                )

                # Check if first user from domain (using cache-aside pattern)
                if await check_if_first_user_from_domain(domain, redis_client, db_conn):
                    # Create organization for first user WITHIN THE SAME SESSION (simplified)
                    try:
                        from app.core.database_helper.brand_organization_helper import generate_organization_code
                        
                        organization_name = domain.replace('.', ' ').title()
                        org_code = generate_organization_code(domain)
                        
                        # Create organization only - no domain claims or memberships
                        organization = Organization(
                            domain=domain,
                            organization_code=org_code,
                            name=organization_name,
                            description=f"Organization for {domain}",
                            is_active=True,
                            total_members=1,
                            created_at=datetime.now(UTC)
                        )
                        
                        session.add(organization)
                        session.flush()  # Get the organization ID
                        
                        APP_CONFIG.logger.info(f"Created organization for first brand user: {email}")
                    
                    except Exception as org_error:
                        APP_CONFIG.logger.error(f"Organization creation failed for {email}: {str(org_error)}")
                        # Since we're in the same transaction, this will cause the entire 
                        # user creation to rollback automatically
                        raise
                else:
                    # For subsequent users from the same domain, they need to be invited
                    # or verify domain ownership before getting organization access
                    APP_CONFIG.logger.info(
                        f"User {email} registered but organization already exists for domain {domain}")

            # Issue tokens using the specific function for UUID roles
            client_info = {k: v for k, v in kwargs.items()
                           if k in ['ip_address', 'user_agent', 'device_info']}

            # Use the UUID-specific token function
            token_response, _ = issue_tokens_for_user_with_uuid_role(
                str(new_user.id),
                role_uuid,
                redis_client,
                session,
                **client_info
            )

            APP_CONFIG.logger.info(f"User registration completed successfully: {email} with role {role_name}")

            # COMMIT HAPPENS HERE - All operations succeed or all fail together

            # Post-transaction operations (Redis cleanup, bloom filter, etc.)
            try:
                # Add to bloom filter if redis_client is available
                if redis_client:
                    bloom_filter_manager = get_bloom_filter_manager(redis_client)
                    bloom_filter_manager.add_email(email)

                # Update user cache with new status
                redis_key = RedisKeys.user_by_email(email)
                user_dict = {
                    "id": str(new_user.id),
                    "email": new_user.email,
                    "phone_number": new_user.phone_number or "",
                    "status": new_user.status,
                    "is_active": str(new_user.is_active),
                    "is_email_verified": str(new_user.is_email_verified),
                }
                redis_client.hset(redis_key, mapping=user_dict)
                redis_client.expire(redis_key, RedisConfig.USER_TTL)

                # Invalidate organization cache if organization was created
                if role_name and 'brand' in role_name.lower():
                    cache_keys_to_invalidate = [
                        f"CreatorVerse:organization:domain:{domain}",
                        f"CreatorVerse:domain:orgs:{domain}",
                        f"CreatorVerse:user:organizations:{new_user.id}"
                    ]
                    
                    for cache_key in cache_keys_to_invalidate:
                        redis_client.delete(cache_key)
                    
                    APP_CONFIG.logger.info(f"Invalidated caches for new organization: {domain}")

            except Exception as post_error:
                # Post-transaction operations failing should not affect the main transaction
                APP_CONFIG.logger.warning(f"Post-transaction operations failed: {str(post_error)}")

            return token_response

    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to create/update user: {str(e)}")
        raise


async def create_brand_user_with_organization_handling(
        db_conn: SyncDatabaseDB,
        user_email: str,
        role_uuid: str,
        redis_client,
        **additional_data
) -> BrandRegistrationResponse:
    """
    Create brand user and handle organization creation only (no organization membership).
    Returns BrandRegistrationResponse with organization info for brand creation.
    
    Updated workflow:
    1. For first user from domain: Creates organization only, user becomes org admin
    2. For subsequent users: Returns existing organization info  
    3. No organization membership created - users will create/join brands directly via separate API
    4. Brands will be created via separate API endpoints when user requests
    """
    from app.schemas.auth_brand import BrandRegistrationResponse
    import jwt

    domain = user_email.split('@')[-1].lower()

    try:
        # Remove redis_client from additional_data to avoid conflicts
        clean_data = {k: v for k, v in additional_data.items() if k != 'redis_client'}
        clean_data['redis_client'] = redis_client  # Add back for user creation

        # Create user first within the same transaction
        token_response = await create_new_user_with_uuid_role(
            db_conn, user_email, role_uuid, **clean_data
        )

        # Initialize response data
        organization_created = False
        organization_id = None
        user_role = "user"  # Default role, will create brands separately
        domain_verification_required = True

        # Get user ID from token payload for organization handling
        try:
            # Decode token directly using jwt
            payload = jwt.decode(
                token_response.access_token,
                APP_CONFIG.secret_key,
                algorithms=["HS256"],
                options={"verify_signature": False}
            )
            user_id = payload.get("sub")

            if user_id:
                # Check if organization exists for domain
                from app.core.database_helper.organization_helper import get_organization_by_domain_cache_aside
                
                org_data = await get_organization_by_domain_cache_aside(
                    domain=domain,
                    redis_client=redis_client,
                    db_conn=db_conn
                )
                
                if not org_data:
                    # First user from domain - create organization only (no membership)
                    from app.core.database_helper.organization_helper import create_organization_from_domain
                    
                    org_name = domain.split('.')[0].capitalize()
                    org_data = await create_organization_from_domain(
                        domain=domain,
                        user_id=UUID(user_id),
                        org_name=org_name,
                        redis_client=redis_client,
                        db_conn=db_conn
                    )
                    
                    organization_created = True
                    organization_id = org_data["id"]
                    user_role = "admin"  # First user is org admin, will create brands separately
                    domain_verification_required = False  # Auto-verified for first user
                    
                    APP_CONFIG.logger.info(f"Created organization for first brand user: {user_email}")
                else:
                    # Organization exists - user can create/join brands
                    organization_id = org_data["id"]
                    user_role = "user"  # Regular user, can create/join brands
                    domain_verification_required = False
                    
                    APP_CONFIG.logger.info(f"User {user_email} can access existing organization {organization_id}")

        except Exception as e:
            APP_CONFIG.logger.error(f"Error in organization handling: {str(e)}")
            # Don't fail user creation if organization handling fails
            pass

        # All operations successful within transaction - no organization membership created
        return BrandRegistrationResponse(
            access_token=token_response.access_token,
            refresh_token=token_response.refresh_token,
            token_type=token_response.token_type,
            expires_in=token_response.expires_in,
            organization_created=organization_created,
            organization_id=organization_id,
            user_role=user_role,
            domain_verification_required=domain_verification_required,
            organization_members=[]  # No organization membership table usage
        )
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to create brand user with organization handling: {str(e)}")
        raise ServiceUnavailableError("user_registration", "User registration failed due to internal error")


async def get_organization_members_cache_aside(
        organization_id: str,
        redis_client=get_locobuzz_redis(),
        db_conn: SyncDatabaseDB = get_database()
) -> list[dict]:
    """
    DEPRECATED: Organization membership table is no longer used.
    Returns empty list as we now use brand-based memberships instead.
    """
    APP_CONFIG.logger.warning(f"get_organization_members_cache_aside is deprecated - organization membership table not used")
    return []


async def get_organizations_by_domain_with_members(
        domain: str,
        redis_client=get_locobuzz_redis(),
        db_conn: SyncDatabaseDB = get_database()
) -> dict:
    """
    Get organizations by domain with their members list.
    Returns organization info along with member details.
    """
    from app.utilities.domain_service import get_domain_service

    # Get domain verification status
    domain_service = get_domain_service(redis_client, db_conn)
    domain_status = await domain_service.check_domain_status(domain)

    organizations_with_members = []

    if domain_status.get("organizations"):
        for org in domain_status["organizations"]:
            # Get members for each organization
            members = await get_organization_members_cache_aside(
                org["id"], redis_client, db_conn
            )

            # Add members to organization data
            org_with_members = {
                **org,
                "members": members,
                "member_count": len(members)
            }
            organizations_with_members.append(org_with_members)

    return {
        "organizations": organizations_with_members,
        "domain_verified": domain_status.get("is_verified", False),
        "domain": domain
    }


if __name__ == "__main__":
    # Example usage

    email_test = "<EMAIL>"
    # result = asyncio.run(get_user_by_email_cache_aside(email_test))
    # print(result)
    # db_conn = get_database()
    # with db_conn.transaction() as session:
    #     result = asyncio.run(create_otp_or_update_entry(email_test, {'email': '<EMAIL>',
    #                                                                  'otp_hash': '3d3850532d7f6c5dad8f62888283185dbaef324090f0b27fac48c3e72fbbb86c',
    #                                                                  'generated_at': '1749399798',
    #                                                                  'failed_attempts': '6', 'role': '1',
    #                                                                  'source_in_user': 'email_otp',
    #                                                                  'request_type': 'login', 'register_channel': '1'}, session))
    #     print(result)
