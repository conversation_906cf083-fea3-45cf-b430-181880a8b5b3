import json
import uuid
from typing import Optional, List, Dict, Any
from uuid import UUID

from sqlalchemy import and_
from database_helper.database.models import Organization, Brand, BrandMembership, User
from app.core.config import APP_CONFIG
from app.core.redis_keys import RedisKeys


async def get_organization_by_domain_cache_aside(
    domain: str,
    redis_client,
    db_conn
) -> Optional[Dict[str, Any]]:
    """
    Get organization by domain using cache-aside pattern
    """
    # Check redis cache first
    org_key = f"CreatorVerse:organization:domain:{domain}"
    org_data = redis_client.hgetall(org_key)
    
    if org_data:
        return org_data
    
    # If not in cache, query from database
    with db_conn.transaction() as session:
        org = session.query(Organization).filter(Organization.domain == domain).first()
        
        if not org:
            return None
        
        # Cache the organization data
        org_data = {
            "id": str(org.id),
            "name": org.name,
            "domain": org.domain,
            "is_active": str(int(org.is_active)),
            "created_at": str(org.created_at),
        }
        
        redis_client.hset(org_key, mapping=org_data)
        redis_client.expire(org_key, 3600)  # 1 hour TTL
        
        return org_data


async def create_organization_from_domain(
    domain: str, 
    user_id: UUID,
    org_name: Optional[str] = None,
    redis_client = None,
    db_conn = None
) -> Dict[str, Any]:
    """
    Create a new organization from domain for the first user
    """
    # Extract organization name from domain if not provided
    if not org_name:
        org_name = domain.split('.')[0].capitalize()
    
    with db_conn.transaction() as session:
        # Create organization record
        new_org = Organization(
            name=org_name,
            domain=domain,
            is_active=True
        )
        session.add(new_org)
        session.flush()
        
        # Create domain claim for verification (no membership created)
        from database_helper.database.models import DomainClaim
        import secrets
        from datetime import datetime, UTC
        
        domain_claim = DomainClaim(
            domain=domain,
            verification_token=secrets.token_urlsafe(32),
            claimed_by_user_id=user_id,
            organization_id=new_org.id,
            verified_at=datetime.now(UTC)  # Auto-verified for first user
        )
        session.add(domain_claim)
        
        # Cache organization data
        if redis_client:
            org_key = f"CreatorVerse:organization:domain:{domain}"
            org_data = {
                "id": str(new_org.id),
                "name": new_org.name,
                "domain": new_org.domain,
                "is_active": "1",
                "created_at": str(new_org.created_at),
            }
            redis_client.hset(org_key, mapping=org_data)
            redis_client.expire(org_key, 3600)  # 1 hour TTL
        
        APP_CONFIG.logger.info(f"Created new organization {new_org.name} for domain {domain}")
        return {
            "id": str(new_org.id),
            "name": new_org.name,
            "domain": domain,
            "is_active": True,
            "created_at": str(new_org.created_at)
        }


async def get_organization_brands_cache_aside(
    organization_id: UUID,
    redis_client,
    db_conn
) -> List[Dict[str, Any]]:
    """
    Get all brands for an organization using cache-aside pattern
    """
    brands_key = f"CreatorVerse:brands:org:{organization_id}"
    brands_data = redis_client.get(brands_key)
    
    if brands_data:
        return json.loads(brands_data)
    
    # If not in cache, query from database
    with db_conn.transaction() as session:
        brands = session.query(Brand).filter(
            Brand.organization_id == organization_id,
            Brand.deleted_at.is_(None)
        ).all()
        
        result = []
        for brand in brands:
            result.append({
                "id": str(brand.id),
                "name": brand.name,
                "description": brand.description,
                "logo_url": brand.logo_url,
                "website_url": brand.website_url,
                "is_active": brand.is_active,
                "created_at": str(brand.created_at)
            })
        
        # Cache the brands data
        redis_client.set(brands_key, json.dumps(result))
        redis_client.expire(brands_key, 3600)  # 1 hour TTL
        
        return result


async def create_brand_for_organization(
    organization_id: UUID,
    user_id: UUID,
    brand_data: Dict[str, Any],
    redis_client,
    db_conn
) -> Dict[str, Any]:
    """
    Create a new brand within an organization and make the user a brand admin
    """
    with db_conn.transaction() as session:
        # Create the brand
        new_brand = Brand(
            organization_id=organization_id,
            name=brand_data.get("name"),
            description=brand_data.get("description"),
            logo_url=brand_data.get("logo_url"),
            website_url=brand_data.get("website_url"),
            contact_email=brand_data.get("contact_email"),
            industry=brand_data.get("industry"),
            brand_type=brand_data.get("brand_type", "product"),
            created_by=user_id,
            is_active=True
        )
        session.add(new_brand)
        session.flush()
        
        # Make the user a brand admin
        brand_membership = BrandMembership(
            brand_id=new_brand.id,
            user_id=user_id,
            role="brand_admin",
            status="active",
            can_create_campaigns=True,
            can_manage_brand=True,
            invited_by=user_id
        )
        session.add(brand_membership)
        
        # Invalidate related caches using the cache invalidation utility
        try:
            from app.utilities.cache_invalidation import invalidate_brand_caches
            invalidate_brand_caches(redis_client, organization_id=organization_id)
        except ImportError:
            # Fallback to direct invalidation if import fails
            brands_key = f"CreatorVerse:brands:org:{organization_id}"
            redis_client.delete(brands_key)
        
        result = {
            "id": str(new_brand.id),
            "name": new_brand.name,
            "organization_id": str(new_brand.organization_id),
            "description": new_brand.description,
            "logo_url": new_brand.logo_url,
            "website_url": new_brand.website_url,
            "is_active": new_brand.is_active,
            "created_at": str(new_brand.created_at)
        }
        
        APP_CONFIG.logger.info(f"Created new brand {new_brand.name} in organization {organization_id}")
        return result


async def check_and_create_organization_for_user(
    user_id: UUID, 
    user_email: str, 
    redis_client, 
    db_conn
) -> Dict[str, Any]:
    """
    Check if user's email domain already has an organization.
    If not, create one and set user as admin.
    No default brand is created at this step.
    Returns organization details.
    """
    result = {"created": False, "organization": None}
    
    # Extract email domain
    email_domain = user_email.split('@')[-1].lower()
    
    # Check if organization for this domain already exists
    org_data = await get_organization_by_domain_cache_aside(
        domain=email_domain,
        redis_client=redis_client,
        db_conn=db_conn
    )
    
    if not org_data:
        # First user from this domain - create organization
        org_name = email_domain.split('.')[0].capitalize()
        org_data = await create_organization_from_domain(
            domain=email_domain,
            user_id=user_id,
            org_name=org_name,
            redis_client=redis_client,
            db_conn=db_conn
        )
        
        result["created"] = True
        result["organization"] = org_data
        
        APP_CONFIG.logger.info(f"Created organization for first-time domain: {email_domain}")
    else:
        # Organization exists - user can create/join brands within organization
        result["organization"] = org_data
        APP_CONFIG.logger.info(f"User {user_id} can access existing organization {org_data['id']} for domain {email_domain}")
                
    return result
