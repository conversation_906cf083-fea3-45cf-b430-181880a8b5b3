"""
Brand join requests helper for managing brand membership requests.
Handles join requests, approvals, and rejections with proper caching.
"""
import json
from datetime import datetime, UTC
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, select, update
from sqlalchemy.exc import IntegrityError

from database_helper.database.models import (
    Brand, 
    <PERSON>M<PERSON><PERSON>hip, 
    User,
    BrandJoinRequest  # Assuming this model exists
)
from database_helper.database.sync_db2 import SyncDatabaseDB
from app.core.config import APP_CONFIG, get_locobuzz_redis, get_database
from app.core.redis_keys import RedisKeys

# Cache TTL constants
BRAND_JOIN_REQUESTS_TTL = 1800  # 30 minutes
BRAND_MEMBERS_TTL = 3600  # 1 hour


async def get_organization_brands_with_members_cache_aside(
    user_id: str,
    redis_client=get_locobuzz_redis(),
    db_conn: SyncDatabaseDB = get_database()
) -> Dict[str, Any]:
    """
    Get all brands belonging to user's organization with member information.
    Uses cache-aside pattern for performance.
    """
    cache_key = f"CreatorVerse:user:org:brands:{user_id}"
    
    # Try cache first
    try:
        cached_data = redis_client.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Cache error in get_organization_brands_with_members: {str(e)}")
    
    # Cache miss - query database
    with db_conn.transaction() as session:
        # Get user to find their organization
        user = session.get(User, UUID(user_id))
        if not user:
            return {"brands": [], "organization": None, "total_count": 0}
        
        # Get user's organization through their email domain
        user_domain = user.email.split('@')[-1].lower()
        
        # Get all brands for this organization domain
        brands_query = (
            session.query(Brand)
            .join(User, Brand.created_by == User.id)
            .filter(
                User.email.like(f"%@{user_domain}"),
                Brand.deleted_at.is_(None),
                Brand.is_active == True
            )
        )
        
        brands = brands_query.all()
        
        result_brands = []
        for brand in brands:
            # Get brand members with their roles
            members_query = (
                session.query(User, BrandMembership)
                .join(BrandMembership, User.id == BrandMembership.user_id)
                .filter(
                    BrandMembership.brand_id == brand.id,
                    BrandMembership.status == "active"
                )
            )
            
            members = members_query.all()
            
            # Format member data
            members_data = []
            for user_member, membership in members:
                members_data.append({
                    "user_id": str(user_member.id),
                    "email": user_member.email,
                    "role": membership.role,
                    "joined_at": membership.joined_at.isoformat() if membership.joined_at else None,
                    "can_manage_brand": getattr(membership, 'can_manage_brand', False),
                    "can_create_campaigns": getattr(membership, 'can_create_campaigns', False)
                })
            
            # Check if current user is a member
            user_membership = next(
                (m for u, m in members if u.id == UUID(user_id)), 
                None
            )
            
            brand_data = {
                "id": str(brand.id),
                "name": brand.name,
                "description": brand.description,
                "logo_url": brand.logo_url,
                "website_url": brand.website_url,
                "contact_email": brand.contact_email,
                "is_active": brand.is_active,
                "created_at": brand.created_at.isoformat() if brand.created_at else None,
                "created_by": str(brand.created_by) if brand.created_by else None,
                "members": members_data,
                "member_count": len(members_data),
                "user_role": user_membership.role if user_membership else None,
                "is_member": user_membership is not None
            }
            
            result_brands.append(brand_data)
        
        # Prepare response
        response_data = {
            "brands": result_brands,
            "organization": {
                "domain": user_domain,
                "user_email": user.email
            },
            "total_count": len(result_brands),
            "user_id": user_id
        }
        
        # Cache the result
        try:
            redis_client.setex(cache_key, BRAND_MEMBERS_TTL, json.dumps(response_data))
        except Exception as e:
            APP_CONFIG.logger.warning(f"Cache set error: {str(e)}")
        
        return response_data


async def request_to_join_brand(
    brand_id: str,
    user_id: str,
    message: Optional[str] = None,
    redis_client=get_locobuzz_redis(),
    db_conn: SyncDatabaseDB = get_database()
) -> Dict[str, Any]:
    """
    Create a request to join a brand. Validates eligibility and stores request.
    """
    with db_conn.transaction() as session:
        # Validate brand exists and is active
        brand = session.get(Brand, UUID(brand_id))
        if not brand or not brand.is_active or brand.deleted_at:
            raise ValueError("Brand not found or inactive")
        
        # Get requesting user
        user = session.get(User, UUID(user_id))
        if not user:
            raise ValueError("User not found")
        
        # Check if user is already a member
        existing_membership = session.query(BrandMembership).filter_by(
            brand_id=UUID(brand_id),
            user_id=UUID(user_id)
        ).first()
        
        if existing_membership and existing_membership.status == "active":
            raise ValueError("User is already a member of this brand")
        
        # Check if there's already a pending request
        existing_request = session.query(BrandJoinRequest).filter_by(
            brand_id=UUID(brand_id),
            user_id=UUID(user_id),
            status="pending"
        ).first()
        
        if existing_request:
            raise ValueError("Join request already pending for this brand")
        
        # Validate user domain matches brand organization domain
        user_domain = user.email.split('@')[-1].lower()
        brand_creator = session.get(User, brand.created_by)
        brand_domain = brand_creator.email.split('@')[-1].lower()
        
        if user_domain != brand_domain:
            raise ValueError("Can only join brands within your organization domain")
        
        # Create join request
        join_request = BrandJoinRequest(
            brand_id=UUID(brand_id),
            user_id=UUID(user_id),
            message=message,
            status="pending",
            requested_at=datetime.now(UTC)
        )
        
        session.add(join_request)
        session.flush()
        
        # Invalidate relevant caches
        cache_keys_to_invalidate = [
            f"CreatorVerse:brand:join_requests:{brand_id}",
            f"CreatorVerse:user:join_requests:{user_id}",
            f"CreatorVerse:user:org:brands:{user_id}"
        ]
        
        for cache_key in cache_keys_to_invalidate:
            try:
                redis_client.delete(cache_key)
            except Exception as e:
                APP_CONFIG.logger.warning(f"Cache invalidation error: {str(e)}")
        
        APP_CONFIG.logger.info(f"Brand join request created: user {user_id} -> brand {brand_id}")
        
        return {
            "request_id": str(join_request.id),
            "brand_id": brand_id,
            "brand_name": brand.name,
            "user_id": user_id,
            "user_email": user.email,
            "message": message,
            "status": "pending",
            "requested_at": join_request.requested_at.isoformat()
        }


async def get_brand_join_requests_cache_aside(
    brand_id: str,
    redis_client=get_locobuzz_redis(),
    db_conn: SyncDatabaseDB = get_database()
) -> List[Dict[str, Any]]:
    """
    Get all pending join requests for a brand using cache-aside pattern.
    """
    cache_key = f"CreatorVerse:brand:join_requests:{brand_id}"
    
    # Try cache first
    try:
        cached_data = redis_client.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Cache error in get_brand_join_requests: {str(e)}")
    
    # Cache miss - query database
    with db_conn.transaction() as session:
        # Get pending requests with user information
        requests_query = (
            session.query(BrandJoinRequest, User)
            .join(User, BrandJoinRequest.user_id == User.id)
            .filter(
                BrandJoinRequest.brand_id == UUID(brand_id),
                BrandJoinRequest.status == "pending"
            )
            .order_by(BrandJoinRequest.requested_at.desc())
        )
        
        requests = requests_query.all()
        
        result = []
        for join_request, user in requests:
            result.append({
                "request_id": str(join_request.id),
                "user_id": str(user.id),
                "user_email": user.email,
                "message": join_request.message,
                "requested_at": join_request.requested_at.isoformat(),
                "status": join_request.status
            })
        
        # Cache the result
        try:
            redis_client.setex(cache_key, BRAND_JOIN_REQUESTS_TTL, json.dumps(result))
        except Exception as e:
            APP_CONFIG.logger.warning(f"Cache set error: {str(e)}")
        
        return result


async def handle_brand_join_request(
    request_id: str,
    action: str,  # "approve" or "reject"
    admin_user_id: str,
    role: str = "member",
    redis_client=get_locobuzz_redis(),
    db_conn: SyncDatabaseDB = get_database()
) -> Dict[str, Any]:
    """
    Approve or reject a brand join request. Creates membership if approved.
    """
    if action not in ["approve", "reject"]:
        raise ValueError("Action must be 'approve' or 'reject'")
    
    with db_conn.transaction() as session:
        # Get the join request
        join_request = session.get(BrandJoinRequest, UUID(request_id))
        if not join_request:
            raise ValueError("Join request not found")
        
        if join_request.status != "pending":
            raise ValueError(f"Request already {join_request.status}")
        
        # Validate admin has permission to approve
        brand = session.get(Brand, join_request.brand_id)
        admin_membership = session.query(BrandMembership).filter_by(
            brand_id=join_request.brand_id,
            user_id=UUID(admin_user_id),
            status="active"
        ).filter(
            BrandMembership.role.in_(["brand_admin", "admin"])
        ).first()
        
        if not admin_membership:
            raise ValueError("Insufficient permissions to approve/reject requests")
        
        # Update request status
        join_request.status = "approved" if action == "approve" else "rejected"
        join_request.reviewed_by = UUID(admin_user_id)
        join_request.reviewed_at = datetime.now(UTC)
        
        session.add(join_request)
        
        result = {
            "request_id": request_id,
            "action": action,
            "brand_id": str(join_request.brand_id),
            "user_id": str(join_request.user_id),
            "reviewed_by": admin_user_id,
            "reviewed_at": join_request.reviewed_at.isoformat()
        }
        
        # If approved, create brand membership
        if action == "approve":
            # Check if membership already exists (edge case)
            existing_membership = session.query(BrandMembership).filter_by(
                brand_id=join_request.brand_id,
                user_id=join_request.user_id
            ).first()
            
            if existing_membership:
                # Reactivate existing membership
                existing_membership.status = "active"
                existing_membership.role = role
                existing_membership.joined_at = datetime.now(UTC)
                session.add(existing_membership)
            else:
                # Create new membership
                membership = BrandMembership(
                    brand_id=join_request.brand_id,
                    user_id=join_request.user_id,
                    role=role,
                    status="active",
                    joined_at=datetime.now(UTC),
                    invited_by=UUID(admin_user_id)
                )
                session.add(membership)
            
            result["membership_created"] = True
            result["role"] = role
        
        # Invalidate relevant caches
        cache_keys_to_invalidate = [
            f"CreatorVerse:brand:join_requests:{join_request.brand_id}",
            f"CreatorVerse:user:join_requests:{join_request.user_id}",
            f"CreatorVerse:user:org:brands:{join_request.user_id}",
            f"CreatorVerse:brand:details:{join_request.brand_id}"
        ]
        
        for cache_key in cache_keys_to_invalidate:
            try:
                redis_client.delete(cache_key)
            except Exception as e:
                APP_CONFIG.logger.warning(f"Cache invalidation error: {str(e)}")
        
        APP_CONFIG.logger.info(f"Brand join request {action}d: {request_id} by {admin_user_id}")
        
        return result


def invalidate_brand_join_caches(
    redis_client,
    brand_id: Optional[str] = None,
    user_id: Optional[str] = None
) -> None:
    """
    Invalidate brand join request related caches.
    """
    keys_to_delete = []
    
    if brand_id:
        keys_to_delete.extend([
            f"CreatorVerse:brand:join_requests:{brand_id}",
            f"CreatorVerse:brand:details:{brand_id}"
        ])
    
    if user_id:
        keys_to_delete.extend([
            f"CreatorVerse:user:join_requests:{user_id}",
            f"CreatorVerse:user:org:brands:{user_id}"
        ])
    
    for key in keys_to_delete:
        try:
            redis_client.delete(key)
        except Exception as e:
            APP_CONFIG.logger.warning(f"Failed to invalidate cache key {key}: {str(e)}")
