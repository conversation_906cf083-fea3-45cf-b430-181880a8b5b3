from typing import Literal
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field


class InfluencerRegisterRequest(BaseModel):
    """Schema for influencer registration request"""
    email: EmailStr
    role_uuid: str = Field(..., description="UUID of the influencer role")
    register_source: Literal[1, 2, 3, 4, 5] = Field(
        default=1,
        description="Source of user registration",
        examples=[1, 2, 3, 4, 5]
    )

    @property
    def register_source_name(self) -> str:
        """Returns the descriptive name for the register_source."""
        from app.core.enums_data import SourceRegister
        return SourceRegister.get_method_key(self.register_source)


class InfluencerLoginRequest(BaseModel):
    """Schema for influencer login request"""
    email: EmailStr
    role_uuid: str = Field(..., description="UUID of the influencer role")  # Added role_uuid
    login_source: Literal[1, 2, 3, 4, 5] = Field(
        default=1,
        description="Source of user login",
        examples=[1, 2, 3, 4, 5]
    )

    @property
    def login_source_name(self) -> str:
        """Returns the descriptive name for the login_source."""
        if self.login_source == 1:
            return "email_otp"
        elif self.login_source == 2:
            return "phone_otp"
        elif self.login_source == 3:
            return "google_oauth"
        elif self.login_source == 4:
            return "youtube_oauth"
        elif self.login_source == 5:
            return "instagram_oauth"
        else:
            return "unknown"


class InfluencerResendOtpRequest(BaseModel):
    """Schema for influencer resend OTP request"""
    email: EmailStr
    role_uuid: str = Field(..., description="UUID of the influencer role")


class InfluencerOtpRequest(BaseModel):
    """Schema for influencer OTP verification request"""
    email: EmailStr
    otp: int


class InfluencerOtpResponse(BaseModel):
    """Schema for influencer OTP response"""
    message: str
    email: EmailStr
