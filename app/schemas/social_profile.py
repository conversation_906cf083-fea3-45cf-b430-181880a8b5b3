from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class SocialProfileBase(BaseModel):
    service: str = Field(..., description="Social media service name")
    external_id: str = Field(..., description="External service user/channel ID")
    username: Optional[str] = Field(None, description="Username on the platform")
    display_name: Optional[str] = Field(None, description="Display name on the platform")
    avatar_url: Optional[str] = Field(None, description="Profile picture URL")
    follower_count: Optional[int] = Field(None, description="Number of followers/subscribers")
    post_count: Optional[int] = Field(None, description="Number of posts/videos")


class SocialProfileCreate(SocialProfileBase):
    oauth_account_id: UUID = Field(..., description="Associated OAuth account ID")


class SocialProfileResponse(SocialProfileBase):
    id: UUID
    oauth_account_id: UUID
    fetched_at: datetime
    
    class Config:
        from_attributes = True
        orm_mode = True


class SocialProfileUpdate(BaseModel):
    username: Optional[str] = None
    display_name: Optional[str] = None
    avatar_url: Optional[str] = None
    follower_count: Optional[int] = None
    post_count: Optional[int] = None
