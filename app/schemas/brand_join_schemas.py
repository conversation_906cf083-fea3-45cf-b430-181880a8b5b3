"""
Pydantic schemas for brand join request operations.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class BrandMemberInfo(BaseModel):
    """Schema for brand member information."""
    user_id: UUID = Field(..., description="User ID")
    email: str = Field(..., description="User email")
    role: str = Field(..., description="User role in brand")
    joined_at: Optional[datetime] = Field(None, description="Join timestamp")
    can_manage_brand: bool = Field(default=False, description="Can manage brand settings")
    can_create_campaigns: bool = Field(default=False, description="Can create campaigns")


class OrganizationBrandInfo(BaseModel):
    """Schema for brand information with members."""
    id: UUID = Field(..., description="Brand ID")
    name: str = Field(..., description="Brand name")
    description: Optional[str] = Field(None, description="Brand description")
    logo_url: Optional[str] = Field(None, description="Brand logo URL")
    website_url: Optional[str] = Field(None, description="Brand website URL")
    contact_email: Optional[str] = Field(None, description="Brand contact email")
    is_active: bool = Field(..., description="Brand status")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    created_by: Optional[UUID] = Field(None, description="Creator user ID")
    members: List[BrandMemberInfo] = Field(default_factory=list, description="Brand members")
    member_count: int = Field(default=0, description="Total member count")
    user_role: Optional[str] = Field(None, description="Current user's role in brand")
    is_member: bool = Field(default=False, description="Whether current user is a member")


class OrganizationBrandsResponse(BaseModel):
    """Schema for organization brands response."""
    brands: List[OrganizationBrandInfo] = Field(..., description="List of organization brands")
    organization: Dict[str, Any] = Field(..., description="Organization information")
    total_count: int = Field(..., description="Total number of brands")
    user_id: UUID = Field(..., description="Requesting user ID")

    class Config:
        schema_extra = {
            "example": {
                "brands": [
                    {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "name": "Premium Products",
                        "description": "Our premium product line",
                        "logo_url": "https://example.com/logo.png",
                        "is_active": True,
                        "member_count": 5,
                        "user_role": "brand_admin",
                        "is_member": True,
                        "members": [
                            {
                                "user_id": "550e8400-e29b-41d4-a716-************",
                                "email": "<EMAIL>",
                                "role": "brand_admin",
                                "can_manage_brand": True,
                                "can_create_campaigns": True
                            }
                        ]
                    }
                ],
                "organization": {
                    "domain": "company.com",
                    "user_email": "<EMAIL>"
                },
                "total_count": 1,
                "user_id": "550e8400-e29b-41d4-a716-************"
            }
        }


class BrandJoinRequest(BaseModel):
    """Schema for brand join request."""
    brand_id: UUID = Field(..., description="Brand ID to join")
    message: Optional[str] = Field(None, max_length=500, description="Optional message to brand admins")

    @validator('message')
    def validate_message(cls, v):
        if v is not None:
            v = v.strip()
            if not v:  # Empty string after strip
                return None
        return v

    class Config:
        schema_extra = {
            "example": {
                "brand_id": "550e8400-e29b-41d4-a716-************",
                "message": "I would like to join this brand to contribute to marketing campaigns."
            }
        }


class BrandJoinRequestResponse(BaseModel):
    """Schema for brand join request response."""
    request_id: UUID = Field(..., description="Join request ID")
    brand_id: UUID = Field(..., description="Brand ID")
    brand_name: str = Field(..., description="Brand name")
    user_id: UUID = Field(..., description="Requesting user ID")
    user_email: str = Field(..., description="Requesting user email")
    message: Optional[str] = Field(None, description="Request message")
    status: str = Field(..., description="Request status")
    requested_at: datetime = Field(..., description="Request timestamp")


class PendingJoinRequestInfo(BaseModel):
    """Schema for pending join request information."""
    request_id: UUID = Field(..., description="Request ID")
    user_id: UUID = Field(..., description="Requesting user ID")
    user_email: str = Field(..., description="Requesting user email")
    message: Optional[str] = Field(None, description="Request message")
    requested_at: datetime = Field(..., description="Request timestamp")
    status: str = Field(default="pending", description="Request status")


class BrandJoinRequestsResponse(BaseModel):
    """Schema for brand join requests list response."""
    brand_id: UUID = Field(..., description="Brand ID")
    brand_name: str = Field(..., description="Brand name")
    pending_requests: List[PendingJoinRequestInfo] = Field(..., description="Pending join requests")
    total_pending: int = Field(..., description="Total pending requests count")

    class Config:
        schema_extra = {
            "example": {
                "brand_id": "550e8400-e29b-41d4-a716-************",
                "brand_name": "Premium Products",
                "pending_requests": [
                    {
                        "request_id": "550e8400-e29b-41d4-a716-************",
                        "user_id": "550e8400-e29b-41d4-a716-************",
                        "user_email": "<EMAIL>",
                        "message": "I would like to join this brand",
                        "requested_at": "2024-01-15T10:30:00Z",
                        "status": "pending"
                    }
                ],
                "total_pending": 1
            }
        }


class JoinRequestAction(BaseModel):
    """Schema for join request approval/rejection."""
    action: str = Field(..., description="Action to take")
    role: str = Field(default="member", description="Role to assign if approved")

    @validator('action')
    def validate_action(cls, v):
        if v not in ['approve', 'reject']:
            raise ValueError('Action must be either "approve" or "reject"')
        return v

    @validator('role')
    def validate_role(cls, v):
        allowed_roles = ['member', 'admin', 'brand_admin']
        if v not in allowed_roles:
            raise ValueError(f'Role must be one of: {", ".join(allowed_roles)}')
        return v

    class Config:
        schema_extra = {
            "example": {
                "action": "approve",
                "role": "member"
            }
        }


class JoinRequestActionResponse(BaseModel):
    """Schema for join request action response."""
    request_id: UUID = Field(..., description="Request ID")
    action: str = Field(..., description="Action taken")
    brand_id: UUID = Field(..., description="Brand ID")
    user_id: UUID = Field(..., description="User ID")
    reviewed_by: UUID = Field(..., description="Admin who reviewed")
    reviewed_at: datetime = Field(..., description="Review timestamp")
    membership_created: bool = Field(default=False, description="Whether membership was created")
    role: Optional[str] = Field(None, description="Role assigned if approved")
