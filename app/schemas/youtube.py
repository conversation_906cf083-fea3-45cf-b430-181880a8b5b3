from typing import List, Optional
from pydantic import BaseModel, Field


class YouTubeChannelInfo(BaseModel):
    """Schema for YouTube channel information"""
    channel_id: str = Field(..., description="YouTube channel ID")
    title: str = Field(..., description="Channel title")
    description: Optional[str] = Field(None, description="Channel description")
    thumbnail_url: Optional[str] = Field(None, description="Channel thumbnail URL")
    subscriber_count: Optional[int] = Field(None, description="Subscriber count")
    video_count: Optional[int] = Field(None, description="Video count")
    view_count: Optional[int] = Field(None, description="View count")
    is_verified: Optional[bool] = Field(None, description="Whether the channel is verified")


class YouTubeChannelListResponse(BaseModel):
    """Response schema for YouTube channel listing"""
    channels: List[YouTubeChannelInfo] = Field(..., description="List of YouTube channels")


class YouTubeChannelSelectRequest(BaseModel):
    """Request schema for YouTube channel selection"""
    channel_id: str = Field(..., description="ID of the selected YouTube channel")
