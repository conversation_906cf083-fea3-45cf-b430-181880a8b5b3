from typing import Any, Dict, List, Literal, Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field


class BrandRegisterRequest(BaseModel):
    """Schema for brand registration request"""
    email: EmailStr
    role_uuid: UUID = Field(
        ...,
        description="UUID of the brand role",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
    register_source: Literal[1, 2, 3, 4, 5] = Field(
        default=1,
        description="Source of user registration",
        examples=[1, 2, 3, 4, 5]
    )

    @property
    def register_source_name(self) -> str:
        """Returns the descriptive name for the register_source."""
        from app.core.enums_data import SourceRegister
        return SourceRegister.get_method_key(self.register_source)


class BrandLoginRequest(BaseModel):
    """Schema for brand login request"""
    email: EmailStr
    login_source: Literal[1, 2, 3, 4, 5] = Field(
        default=1,
        description="Source of user login",
        examples=[1, 2, 3, 4, 5]
    )

    @property
    def login_source_name(self) -> str:
        """Returns the descriptive name for the login_source."""
        if self.login_source == 1:
            return "email_otp"
        elif self.login_source == 2:
            return "phone_otp"
        elif self.login_source == 3:
            return "google_oauth"
        elif self.login_source == 4:
            return "youtube_oauth"
        elif self.login_source == 5:
            return "instagram_oauth"
        else:
            return "unknown"


class BrandOtpRequest(BaseModel):
    """Schema for brand OTP verification request"""
    email: EmailStr
    otp: int
    role_uuid: UUID = Field(
        ...,
        description="UUID of the brand role",
        examples=["550e8400-e29b-41d4-a716-************"]
    )


class BrandOtpResponse(BaseModel):
    """Schema for brand OTP response"""
    message: str
    email: EmailStr


class BrandRegistrationResponse(BaseModel):
    """Schema for brand registration response with organization info"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    organization_created: bool = False
    organization_id: Optional[str] = None
    user_role: str = "member"
    domain_verification_required: bool = True
    organization_members: List[Dict[str, Any]] = Field(default_factory=list)


class BrandProfileResponse(BaseModel):
    """Schema for brand profile response"""
    user: dict
    organizations: list[dict]  # More specific typing for organization data


class OrganizationCreateRequest(BaseModel):
    """Schema for creating organization"""
    domain: str = Field(..., description="Organization domain")
    organization_code: str = Field(..., description="Organization code")
    name: str = Field(..., description="Organization name")


class OwnershipTransferRequest(BaseModel):
    """Schema for ownership transfer"""
    to_user_id: str = Field(..., description="User ID to transfer ownership to")


class BrandResendOtpRequest(BaseModel):
    """Schema for brand resend OTP request"""
    email: EmailStr
    role_uuid: UUID = Field(
        ...,
        description="UUID of the brand role",
        examples=["550e8400-e29b-41d4-a716-************"]
    )
