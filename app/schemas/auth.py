from typing import Literal, Optional
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, validator


# Shared login request that works with common endpoints
class LoginRequest(BaseModel):
    email: EmailStr
    role_uuid: str = Field(..., description="UUID of the user role")  # Made required, removed Optional
    login_source: Literal[1, 2, 3, 4, 5] = Field(
        ...,
        description="Source of user registration",
        examples=[1, 2, 3, 4, 5]
    )

    @property
    def login_source_name(self) -> str:
        """
        Returns the descriptive name for the register_source.
        """
        from app.core.enums_data import SourceRegister
        return SourceRegister.get_method_key(self.login_source)

    @validator('email')
    def validate_email(cls, v):
        return v.lower().strip()


# Keep for backward compatibility with common endpoints - but deprecated
class RegisterRequest(BaseModel):
    """Schema for user registration request - DEPRECATED: Use role-specific endpoints instead"""

    email: EmailStr
    role_uuid: str = Field(..., description="UUID of the user role")  # Changed from int to UUID
    register_source: Literal[1, 2, 3, 4, 5] = Field(
        ...,
        description="Source of user registration",
        examples=[1, 2, 3, 4, 5]
    )

    @property
    def register_source_name(self) -> str:
        """
        Returns the descriptive name for the register_source.
        """
        from app.core.enums_data import SourceRegister
        return SourceRegister.get_method_key(self.register_source)


# Keep for backward compatibility with common endpoints
class OtpRequest(BaseModel):
    """Schema for OTP verification request"""

    email: EmailStr
    otp: str
    role_uuid: str = Field(..., description="UUID of the user role")  # Made required, removed Optional and int role

    @validator('email')
    def validate_email(cls, v):
        return v.lower().strip()

    def model_post_init(self, __context) -> None:
        # Ensure at least one role field is provided
        if not self.role_uuid:
            raise ValueError("role_uuid must be provided")


class OtpResponse(BaseModel):
    """Schema for OTP response"""

    message: str
    email: EmailStr


class CommonResendOtpRequest(BaseModel):
    """Schema for common resend OTP request (login)"""
    email: EmailStr
    role_uuid: str = Field(..., description="UUID of the user role")

    @validator('email')
    def validate_email(cls, v):
        return v.lower().strip()


class OtpResponseBrand(BaseModel):
    """Schema for OTP response"""

    message: str
    email: EmailStr


# OAuth related schemas
class OAuthInitiateRequest(BaseModel):
    """Schema for OAuth initiation request"""

    provider: str  # 'google' or 'instagram'
    role_uuid: str  # influencer or brand role UUID


class OAuthInitiateResponse(BaseModel):
    """Schema for OAuth initiation response"""

    auth_url: str
    state: str


class OAuthCallbackRequest(BaseModel):
    """Schema for OAuth callback request"""

    code: str
    state: str
    provider: str


class OAuthTokenResponse(BaseModel):
    """Response after successful OAuth authentication."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int = 1800  # 30 minutes
    message: Optional[str] = None  # Added to indicate new registration vs login
    extra_data: Optional[dict] = None  # For additional provider-specific data or metadata


class GoogleTokenResponse(BaseModel):
    """Schema for Google OAuth token response"""

    access_token: str
    id_token: str
    expires_in: int
    refresh_token: str | None = None


class GoogleUserInfo(BaseModel):
    """Schema for Google user information"""

    sub: str  # Google user ID
    email: str
    name: str
    picture: str | None = None
    email_verified: bool


class InstagramTokenResponse(BaseModel):
    """Schema for Instagram OAuth token response"""

    access_token: str


class InstagramUserInfo(BaseModel):
    """Schema for Instagram user information"""

    id: str  # Instagram user ID
    username: str
    account_type: str  # BUSINESS, CREATOR, PERSONAL


class TenantRequestData(BaseModel):
    """Schema for tenant data"""

    domain: str


class TenantResponseData(BaseModel):
    """Schema for tenant data"""

    domain: str
    roles: dict


class LogoutRequest(BaseModel):
    refresh_token: str
