from typing import Optional
from pydantic import BaseModel, Field

class BrandCreate(BaseModel):
    """Schema for creating a new brand"""
    name: str = Field(..., min_length=1, max_length=100, description="Brand name (required)")
    description: Optional[str] = Field(None, max_length=500, description="Brand description or tagline")
    logo_url: Optional[str] = Field(None, max_length=2048, description="URL to brand logo")
    website_url: Optional[str] = Field(None, max_length=2048, description="Brand website URL")
    contact_email: Optional[str] = Field(None, max_length=255, description="Brand-specific contact email")


class BrandResponse(BaseModel):
    """Schema for brand response"""
    id: str
    name: str
    description: Optional[str] = None
    logo_url: Optional[str] = None
    website_url: Optional[str] = None
    contact_email: Optional[str] = None
    is_active: bool
    created_at: str
    organization_id: str
    organization_name: Optional[str] = None


class BrandUpdate(BaseModel):
    """Schema for updating an existing brand"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    logo_url: Optional[str] = Field(None, max_length=2048)
    website_url: Optional[str] = Field(None, max_length=2048)
    contact_email: Optional[str] = Field(None, max_length=255)
    is_active: Optional[bool] = None
