"""
Base response schemas for standardized API responses.
Implements consistent structure: {"message": "...", "data": "..."}
"""

from typing import Any, Optional, Generic, TypeVar, List, Dict
from pydantic import BaseModel, Field


T = TypeVar('T')


class BaseResponse(BaseModel, Generic[T]):
    """Base response schema with message and data fields"""
    message: str = Field(..., description="Response message")
    data: Optional[T] = Field(None, description="Response data")


class SuccessResponse(BaseResponse[T]):
    """Success response schema"""
    def __init__(self, message: str = "Success", data: T = None, **kwargs):
        super().__init__(message=message, data=data, **kwargs)


class ErrorResponse(BaseModel):
    """Error response schema"""
    message: str = Field(..., description="Error message")
    error: Optional[str] = Field(None, description="Error type/code")
    details: Optional[Any] = Field(None, description="Additional error details")


class AuthDataResponse(BaseModel):
    """Authentication data structure"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    user_id: str = Field(..., description="User identifier")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(default=1800, description="Token expiration in seconds")


class OAuthInitiateDataResponse(BaseModel):
    """OAuth initiation data structure"""
    auth_url: str = Field(..., description="OAuth authorization URL")
    state: str = Field(..., description="OAuth state parameter")


class HealthDataResponse(BaseModel):
    """Health check data structure"""
    status: str = Field(..., description="Service status")
    environment: Optional[str] = Field(None, description="Environment name")


class ProfileCompletionDataResponse(BaseModel):
    """Profile completion data structure"""
    overall_completion: float = Field(..., description="Overall completion percentage")
    completion_items: List[Dict[str, Any]] = Field(..., description="Individual completion items")


class SocialProfileDataResponse(BaseModel):
    """Social profile data structure"""
    id: str = Field(..., description="Profile ID")
    platform: str = Field(..., description="Social platform name")
    username: str = Field(..., description="Platform username")
    followers_count: Optional[int] = Field(None, description="Follower count")
    following_count: Optional[int] = Field(None, description="Following count")


class OrganizationDataResponse(BaseModel):
    """Organization data structure"""
    id: str = Field(..., description="Organization ID")
    name: str = Field(..., description="Organization name")
    organization_type: str = Field(..., description="Organization type")
    is_active: bool = Field(..., description="Whether organization is active")


class DomainOrganizationsDataResponse(BaseModel):
    """Domain organizations data structure"""
    organizations: List[OrganizationDataResponse] = Field(..., description="List of organizations")
    domain: str = Field(..., description="Domain name")
    domain_verified: bool = Field(..., description="Whether domain is verified")


# Standard response types for common use cases
StandardSuccessResponse = BaseResponse[Any]
AuthSuccessResponse = BaseResponse[AuthDataResponse]
OAuthInitiateResponse = BaseResponse[OAuthInitiateDataResponse]
HealthResponse = BaseResponse[HealthDataResponse]
ProfileCompletionResponse = BaseResponse[ProfileCompletionDataResponse]
SocialProfilesResponse = BaseResponse[List[SocialProfileDataResponse]]
OrganizationResponse = BaseResponse[OrganizationDataResponse]
DomainOrganizationsResponse = BaseResponse[DomainOrganizationsDataResponse]
