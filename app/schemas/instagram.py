from typing import List, Optional
from pydantic import BaseModel, Field


class InstagramPageInfo(BaseModel):
    """Schema for Instagram page information"""
    page_id: str = Field(..., description="Facebook page ID")
    page_name: str = Field(..., description="Facebook page name")
    instagram_id: str = Field(..., description="Instagram account ID")
    username: str = Field(..., description="Instagram username")
    name: str = Field(..., description="Instagram account name")
    biography: Optional[str] = Field(None, description="Instagram account biography")
    profile_picture_url: Optional[str] = Field(None, description="Instagram profile picture URL")
    followers_count: Optional[int] = Field(None, description="Followers count")
    media_count: Optional[int] = Field(None, description="Media count")
    follows_count: Optional[int] = Field(None, description="Following count")
    account_type: Optional[str] = Field(None, description="Account type (BUSINESS, CREATOR, etc.)")


class InstagramPageListResponse(BaseModel):
    """Response schema for Instagram page listing"""
    pages: List[InstagramPageInfo] = Field(..., description="List of Instagram pages")


class InstagramPageSelectRequest(BaseModel):
    """Request schema for Instagram page selection"""
    instagram_id: str = Field(..., description="ID of the selected Instagram account")
    page_id: str = Field(..., description="ID of the associated Facebook page")
