from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class ProfileCompletionItem(BaseModel):
    """Individual profile completion item with status and weight"""
    label: str = Field(..., description="Display label for this completion item")
    is_completed: bool = Field(..., description="Whether this item is complete")
    weight: int = Field(..., description="Weight of this item in overall completion percentage")
    description: Optional[str] = Field(None, description="Description or hint for completing this item")


class ProfileCompletionResponse(BaseModel):
    """Response schema for profile completion percentage"""
    completion_percentage: int = Field(
        ..., 
        description="Overall profile completion percentage (0-100)", 
        ge=0, 
        le=100
    )
    completed_items: int = Field(..., description="Number of completed items")
    total_items: int = Field(..., description="Total number of possible completion items")
    items: List[ProfileCompletionItem] = Field(
        ..., 
        description="List of individual completion items"
    )
    suggestions: List[str] = Field(
        [], 
        description="List of suggestions to improve profile completion"
    )
