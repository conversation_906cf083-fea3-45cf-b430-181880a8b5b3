from typing import Optional

from pydantic import BaseModel, Field


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    username: str | None = None


class RefreshTokenData(BaseModel):
    refresh_token: str


class ClientInfo(BaseModel):
    ip_address: Optional[str] = Field(
        None,
        description="The client's IP address. This will be the real IP if Uvicorn is configured to trust proxy headers.",
        examples=["*************"]
    )
    user_agent: Optional[str] = Field(
        None,
        description="The User-Agent string from the client's browser.",
        examples=[
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]
    )
