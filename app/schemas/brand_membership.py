from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime

class BrandMembershipRequestSchema(BaseModel):
    brand_id: str = Field(..., description="UUID of the brand to request membership")
    requested_role: str = Field(..., description="Role being requested (e.g., brand-user, brand-admin)")
    message: Optional[str] = Field(None, description="Optional message to brand admins")

class BrandMembershipResponseSchema(BaseModel):
    id: str = Field(..., description="Membership request ID")
    brand_id: str = Field(..., description="Brand ID")
    brand_name: str = Field(..., description="Brand name")
    status: str = Field(..., description="Request status")
    requested_role: str = Field(..., description="Requested role")
    message: str = Field(..., description="Response message")

class MembershipRequestListSchema(BaseModel):
    id: str = Field(..., description="Membership request ID")
    user_id: str = Field(..., description="User ID requesting membership")
    user_email: str = Field(..., description="User email")
    user_name: str = Field(..., description="User display name")
    requested_role: str = Field(..., description="Role being requested")
    request_message: Optional[str] = Field(None, description="User's request message")
    requested_at: datetime = Field(..., description="When request was made")

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "user_id": "550e8400-e29b-41d4-a716-446655440001",
                "user_email": "<EMAIL>",
                "user_name": "John Doe",
                "requested_role": "brand-user",
                "request_message": "I would like to join this brand",
                "requested_at": "2024-01-15T10:30:00Z"
            }
        }
