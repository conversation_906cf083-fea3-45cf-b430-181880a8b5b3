"""
Rate limiting middleware for FastAPI application.
"""

import time
import random
from typing import cast, Optional, Dict, Tu<PERSON>, Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from starlette.types import <PERSON><PERSON><PERSON><PERSON>

from app.core.config import APP_CONFIG, get_locobuzz_redis
from app.core.rate_limiter import get_client_ip


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    FastAPI middleware for rate limiting requests based on IP address.
    Uses Redis for distributed rate limiting with sliding window approach.
    """

    def __init__(
        self,
        app: ASGIApp,
        default_max_requests: int = 100,
        default_window_seconds: int = 60,
        key_prefix: str = "CreatorVerse:ratelimit:global",
        endpoint_limits: Optional[Dict[str, Dict[str, int]]] = None,
    ) -> None:
        """
        Initialize rate limiting middleware.

        Args:
            app: FastAPI application instance
            default_max_requests: Default maximum requests per window
            default_window_seconds: Default time window in seconds
            key_prefix: Redis key prefix for rate limiting
            endpoint_limits: Dict mapping endpoint patterns to custom limits
            Format: {"/api/v1/auth/register": {"max_requests": 10, "window_seconds": 60}}
        """
        super().__init__(app)
        self.default_max_requests = default_max_requests
        self.default_window_seconds = default_window_seconds
        self.key_prefix = key_prefix
        self.endpoint_limits = endpoint_limits or {}
        self.redis_available = True
        self.last_redis_check = 0
        self.redis_check_interval = 60  # Seconds between Redis availability checks
        
    def get_limits_for_endpoint(self, path: str) -> Tuple[int, int]:
        """
        Get rate limit configuration for a specific endpoint.

        Args:
            path: Request path

        Returns:
            Tuple of (max_requests, window_seconds)
        """
        # Check for exact path match first
        if path in self.endpoint_limits:
            limits = self.endpoint_limits[path]
            return limits.get("max_requests", self.default_max_requests), limits.get(
                "window_seconds", self.default_window_seconds
            )

        # Check for pattern matches
        for pattern, limits in self.endpoint_limits.items():
            if path.startswith(pattern):
                return limits.get(
                    "max_requests", self.default_max_requests
                ), limits.get("window_seconds", self.default_window_seconds)

        return self.default_max_requests, self.default_window_seconds

    async def dispatch(self, request: Request, call_next: Any) -> Response:
        """
        Process the request and apply rate limiting.

        Args:
            request: FastAPI request object
            call_next: Next middleware or route handler

        Returns:
            Response object
        """
        # Skip rate limiting for health checks and docs
        if request.url.path in ["/", "/health", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        # Skip rate limiting if Redis is known to be down
        current_time = int(time.time())
        if not self.redis_available:
            # Check if it's time to retry Redis connection
            if current_time - self.last_redis_check > self.redis_check_interval:
                self._check_redis_availability()
            
            # If still unavailable, bypass rate limiting
            if not self.redis_available:
                # Log only occasionally to avoid filling logs
                if random.random() < 0.01:  # 1% chance to log
                    APP_CONFIG.logger.warning("Rate limiting disabled - Redis unavailable")
                return await call_next(request)
                
        client_ip = get_client_ip(request)
        path = request.url.path
        max_requests, window_seconds = self.get_limits_for_endpoint(path)
        
        # Create Redis key following project pattern
        redis_key = f"{self.key_prefix}:{client_ip}:{path}"

        try:
            # Get Redis client with decode_responses=True 
            redis_client = get_locobuzz_redis()
            
            window_start = current_time - window_seconds

            # Use Redis pipeline for atomic operations
            pipe = redis_client.pipeline()

            # Remove expired entries
            pipe.zremrangebyscore(redis_key, 0, window_start)

            # Count current requests in window
            pipe.zcard(redis_key)

            # Add current request
            pipe.zadd(redis_key, {str(current_time): current_time})

            # Set expiration
            pipe.expire(redis_key, window_seconds)

            # Execute pipeline
            results = pipe.execute()

            # Get current count (after removing expired entries)
            current_count = results[1]

            if current_count >= max_requests:
                # Calculate time until next allowed request
                oldest_request = redis_client.zrange(redis_key, 0, 0, withscores=True)
                if oldest_request:
                    oldest_time = int(oldest_request[0][1])
                    retry_after = oldest_time + window_seconds - current_time
                    retry_after = max(1, retry_after)  # At least 1 second
                else:
                    retry_after = window_seconds

                return JSONResponse(
                    status_code=429,
                    content={
                        "detail": {
                            "message": "Rate limit exceeded",
                            "max_requests": max_requests,
                            "window_seconds": window_seconds,
                            "retry_after": retry_after,
                            "endpoint": path,
                        }
                    },
                    headers={"Retry-After": str(retry_after)},
                )

        except Exception as e:
            # Mark Redis as unavailable
            self.redis_available = False
            self.last_redis_check = current_time
            APP_CONFIG.logger.error(f"Redis error in rate limit middleware: {str(e)}")
            # In case of Redis failure, allow the request to proceed

        # Process the request
        response = await call_next(request)
        typed_response = cast(Response, response)

        # Add rate limit headers to response
        typed_response.headers["X-RateLimit-Limit"] = str(max_requests)
        typed_response.headers["X-RateLimit-Window"] = str(window_seconds)

        return typed_response
        
    def _check_redis_availability(self) -> None:
        """Check if Redis is available and update status flag."""
        try:
            redis_client = get_locobuzz_redis()
            redis_client.ping()
            if not self.redis_available:
                APP_CONFIG.logger.info("Redis connection restored - rate limiting re-enabled")
            self.redis_available = True
        except Exception:
            self.redis_available = False
        
        self.last_redis_check = int(time.time())


# Predefined endpoint limits for different API endpoints
AUTH_ENDPOINT_LIMITS = {
    "/api/v1/auth/login": {"max_requests": 10, "window_seconds": 60},
    "/api/v1/auth/influencer/signup/email": {"max_requests": 5, "window_seconds": 60},
    "/api/v1/auth/brand/signup/email": {"max_requests": 5, "window_seconds": 60},
}

USER_ENDPOINT_LIMITS = {
    "/api/v1/users": {"max_requests": 50, "window_seconds": 60},
    "/api/v1/creators": {"max_requests": 30, "window_seconds": 60},
}

# Combined endpoint limits
DEFAULT_ENDPOINT_LIMITS = {**AUTH_ENDPOINT_LIMITS, **USER_ENDPOINT_LIMITS}