"""
Email templates for CreatorVerse User Backend
Contains templates for various email communications including OTP verification emails
"""
from typing import Dict, Any


def get_brand_registration_otp_email_template(otp: str, email: str) -> str:
    """
    Generate HTML email template for brand registration OTP verification.

    Args:
        otp: One-time password
        email: Email address

    Returns:
        str: HTML email content
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CreatorVerse Business - Registration Verification</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8fafc;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                border-top: 4px solid #7c3aed;
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #7c3aed;
                margin-bottom: 10px;
            }}
            .verification-code {{
                background-color: #f3f4f6;
                font-size: 32px;
                font-weight: bold;
                letter-spacing: 5px;
                text-align: center;
                padding: 15px;
                margin: 30px 0;
                border-radius: 8px;
            }}
            .message {{
                margin-bottom: 30px;
                font-size: 16px;
            }}
            .footer {{
                font-size: 14px;
                color: #6b7280;
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
            }}
            .accent {{
                color: #7c3aed;
            }}
            .note {{
                font-size: 13px;
                color: #6b7280;
                font-style: italic;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse <span style="color: #dc2626;">Business</span></div>
                <h2>Welcome to CreatorVerse Business!</h2>
            </div>
            
            <div class="message">
                <p>Hello,</p>
                <p>Thank you for registering your business with CreatorVerse Business. To complete your registration, please use the verification code below:</p>
            </div>
            
            <div class="verification-code">{otp}</div>
            
            <div class="message">
                <p>This verification code will expire in 10 minutes.</p>
                <p>If you didn't request this registration for <span class="accent">{email}</span>, please disregard this email.</p>
            </div>
            
            <div class="note">
                <p>For security reasons, please do not share this code with anyone.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 CreatorVerse Business. All rights reserved.</p>
            <p>This is an automated message, please do not reply.</p>
        </div>
    </body>
    </html>
    """


def get_brand_login_otp_email_template(otp: str, email: str) -> str:
    """
    Generate HTML email template for brand login OTP verification.

    Args:
        otp: One-time password
        email: Email address

    Returns:
        str: HTML email content
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CreatorVerse Business - Login Verification</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8fafc;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                border-top: 4px solid #dc2626;
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #7c3aed;
                margin-bottom: 10px;
            }}
            .verification-code {{
                background-color: #f3f4f6;
                font-size: 32px;
                font-weight: bold;
                letter-spacing: 5px;
                text-align: center;
                padding: 15px;
                margin: 30px 0;
                border-radius: 8px;
            }}
            .message {{
                margin-bottom: 30px;
                font-size: 16px;
            }}
            .footer {{
                font-size: 14px;
                color: #6b7280;
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
            }}
            .accent {{
                color: #dc2626;
            }}
            .note {{
                font-size: 13px;
                color: #6b7280;
                font-style: italic;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse <span style="color: #dc2626;">Business</span></div>
                <h2>Login Verification Code</h2>
            </div>
            
            <div class="message">
                <p>Hello,</p>
                <p>Use the following verification code to access your CreatorVerse Business account:</p>
            </div>
            
            <div class="verification-code">{otp}</div>
            
            <div class="message">
                <p>This verification code will expire in 10 minutes.</p>
                <p>If you didn't request this login for <span class="accent">{email}</span>, please secure your account immediately.</p>
            </div>
            
            <div class="note">
                <p>For security reasons, please do not share this code with anyone.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 CreatorVerse Business. All rights reserved.</p>
            <p>This is an automated message, please do not reply.</p>
        </div>
    </body>
    </html>
    """
