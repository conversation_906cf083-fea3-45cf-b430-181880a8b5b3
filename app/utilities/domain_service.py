import secrets
import string
import uuid
from datetime import datetime, UTC, timed<PERSON><PERSON>
from typing import <PERSON><PERSON>

from database_helper.database.models import DomainClaim, Organization, OrganizationMembership
from database_helper.database.sync_db2 import SyncDatabaseDB
from fastapi import HTTPException, status
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from app.core.config import APP_CONFIG
from app.core.redis_keys import Redis<PERSON>eys, RedisConfig
from app.utilities.email_service import get_email_service


class DomainVerificationError(Exception):
    """Custom exception for domain verification errors"""
    pass


class DomainService:
    """Service for handling domain claims, verification and organization management"""

    def __init__(self, redis_client, db_conn: SyncDatabaseDB):
        self.redis_client = redis_client
        self.db_conn = db_conn

    def generate_domain_token(self) -> str:
        """Generate secure domain verification token"""
        return f"dom_{secrets.token_urlsafe(32)}"

    async def check_domain_status(self, domain: str) -> dict:
        """
        Check if domain is already verified and get organization info.
        Uses cache-aside pattern with proper error handling.
        """
        cache_key = RedisKeys.organization_key(domain)

        # Try cache first
        try:
            cached_data = self.redis_client.hgetall(cache_key)
            if cached_data:
                return {
                    "is_verified": cached_data.get("is_verified") == "true",
                    "organization_id": cached_data.get("organization_id") or None,
                    "organization_name": cached_data.get("organization_name") or None,
                    "verification_pending": cached_data.get("verification_pending") == "true"
                }
        except Exception as e:
            APP_CONFIG.logger.warning(f"Redis cache error in check_domain_status: {str(e)}")

        # Fallback to database
        with self.db_conn.transaction() as session:
            domain_claim = session.scalar(
                select(DomainClaim).where(DomainClaim.domain == domain)
            )

            if not domain_claim:
                org_data = {
                    "is_verified": False,
                    "organization_id": None,
                    "organization_name": None,
                    "verification_pending": False
                }
            else:
                org_data = {
                    "is_verified": domain_claim.verified_at is not None,
                    "organization_id": str(domain_claim.organization_id) if domain_claim.organization_id else None,
                    "organization_name": None,
                    "verification_pending": domain_claim.verified_at is None
                }

                # Get organization name if verified
                if domain_claim.organization_id:
                    org = session.get(Organization, domain_claim.organization_id)
                    if org:
                        org_data["organization_name"] = org.name

            # Cache the result for future requests
            try:
                cache_data = {
                    "is_verified": str(org_data["is_verified"]).lower(),
                    "organization_id": org_data["organization_id"] or "",
                    "organization_name": org_data["organization_name"] or "",
                    "verification_pending": str(org_data["verification_pending"]).lower()
                }
                self.redis_client.hset(cache_key, mapping=cache_data)
                self.redis_client.expire(cache_key, RedisConfig.DOMAIN_ORG_TTL)
            except Exception as e:
                APP_CONFIG.logger.warning(f"Redis cache set error in check_domain_status: {str(e)}")

            return org_data

    async def create_domain_claim(self, email: str, user_id: str) -> Tuple[str, bool]:
        """
        Create a domain claim for verification.
        Returns (message, verification_required)
        """
        domain = email.split('@')[-1].lower()

        with self.db_conn.transaction() as session:
            # Check if domain already verified
            existing_verified_claim = session.scalar(
                select(DomainClaim).where(
                    DomainClaim.domain == domain,
                    DomainClaim.verified_at.is_not(None)
                )
            )

            if existing_verified_claim:
                return "Domain already verified", False

            # Check for pending claims
            pending_claim = session.scalar(
                select(DomainClaim).where(
                    DomainClaim.domain == domain,
                    DomainClaim.verified_at.is_(None)
                )
            )

            if pending_claim:
                return "Domain verification already in progress", True

            # Create new domain claim without created_at
            verification_token = secrets.token_urlsafe(32)
            domain_claim = DomainClaim(
                domain=domain,
                verification_token=verification_token,
                claimed_by_user_id=user_id
            )
            session.add(domain_claim)
            session.flush()

            # Send verification email
            try:
                await self._send_domain_verification_email(email, domain, verification_token)
                APP_CONFIG.logger.info(f"Domain verification email sent for {domain}")
                return "Domain verification email sent", True
            except Exception as e:
                APP_CONFIG.logger.error(f"Failed to send domain verification email: {str(e)}")
                # Remove the claim if email fails
                session.delete(domain_claim)
                return "Failed to send verification email", True

    async def verify_domain(self, token: str) -> dict:
        """
        Verify domain using token and create organization with owner membership.
        """
        with self.db_conn.transaction() as session:
            # Find domain claim by token
            domain_claim = session.scalar(
                select(DomainClaim).where(
                    DomainClaim.verification_token == token,
                    DomainClaim.verified_at.is_(None)
                )
            )

            if not domain_claim:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired verification token"
                )

            # Check token expiry (24 hours)
            if domain_claim.created_at < datetime.now(UTC) - timedelta(hours=24):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Verification token has expired"
                )

            try:
                # Create organization
                org_name = self._generate_org_name(domain_claim.domain)
                organization = Organization(
                    id=str(uuid.uuid4()),
                    domain=domain_claim.domain,
                    name=org_name,
                    organization_code=self._generate_org_code(domain_claim.domain),
                    total_members=50,  # Default from your config
                    filled_members=1,
                    is_active=True,
                    created_at=datetime.now(UTC)
                )
                session.add(organization)
                session.flush()  # Get org ID

                # Update domain claim
                domain_claim.verified_at = datetime.now(UTC)
                domain_claim.organization_id = organization.id
                session.add(domain_claim)

                # Create owner membership
                membership = OrganizationMembership(
                    organization_id=organization.id,
                    user_id=domain_claim.claimed_by_user_id,
                    role="owner",
                    status="active",
                    joined_at=datetime.now(UTC)
                )
                session.add(membership)

                # Invalidate cache
                if self.redis_client:
                    try:
                        self.redis_client.delete(RedisKeys.organization_key(domain_claim.domain))
                    except Exception as e:
                        APP_CONFIG.logger.warning(f"Redis cache delete error in verify_domain: {str(e)}")

                APP_CONFIG.logger.info(
                    f"Domain {domain_claim.domain} verified and organization {organization.id} created"
                )

                return {
                    "message": f"Domain verified successfully. {org_name} organization created.",
                    "organization_id": organization.id,
                    "organization_name": org_name
                }

            except IntegrityError as e:
                session.rollback()
                APP_CONFIG.logger.error(f"Failed to verify domain {domain_claim.domain}: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create organization"
                )

    async def join_organization(self, organization_id: str, user_id: str) -> dict:
        """
        Add user to existing verified organization as member.
        """
        with self.db_conn.transaction() as session:
            # Verify organization exists and is active
            organization = session.get(Organization, organization_id)
            if not organization or not organization.is_active:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Organization not found or inactive"
                )

            # Check if user already has membership
            existing_membership = session.scalar(
                select(OrganizationMembership).where(
                    OrganizationMembership.organization_id == organization_id,
                    OrganizationMembership.user_id == user_id
                )
            )

            if existing_membership:
                if existing_membership.status == "active":
                    return {
                        "message": f"Already a member of {organization.name}",
                        "organization_name": organization.name
                    }
                else:
                    # Reactivate membership
                    existing_membership.status = "active"
                    existing_membership.joined_at = datetime.now(UTC)
                    session.add(existing_membership)
            else:
                # Create new membership
                membership = OrganizationMembership(
                    organization_id=organization_id,
                    user_id=user_id,
                    role="member",
                    status="active",
                    joined_at=datetime.now(UTC)
                )
                session.add(membership)

            session.add(organization)

            APP_CONFIG.logger.info(f"User {user_id} joined organization {organization_id}")

            return {
                "message": f"Successfully joined {organization.name}",
                "organization_name": organization.name
            }

    def _generate_org_name(self, domain: str) -> str:
        """Generate organization name from domain"""
        # Remove TLD and capitalize
        name_part = domain.split('.')[0]
        return name_part.replace('-', ' ').replace('_', ' ').title()

    def _generate_org_code(self, domain: str) -> str:
        """Generate organization code from domain"""
        domain_prefix = domain.split('.')[0][:6].upper()
        random_suffix = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(4))
        return f"{domain_prefix}{random_suffix}"

    async def _send_verification_email(self, email: str, domain: str, token: str) -> None:
        """Send domain verification email"""
        try:
            email_service = get_email_service()
            verification_url = f"{APP_CONFIG.base_url}/v1/brand/auth/domains/verify?token={token}"

            success = await email_service.send_domain_verification_email(
                email, domain, verification_url
            )

            if not success:
                APP_CONFIG.logger.warning(f"Failed to send verification email to {email}")
                # Don't raise exception to avoid breaking user registration

        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to send verification email to {email}: {str(e)}")
            # Don't raise exception to avoid breaking user registration


def get_domain_service(redis_client, db_conn: SyncDatabaseDB) -> DomainService:
    """Factory function to create DomainService instance"""
    return DomainService(redis_client, db_conn)
