"""
Utility functions for cache invalidation.
Handles cache invalidation for brand-related, organization-related, and user-related data.
"""
from typing import Optional, Union, List
from uuid import UUID
import json

from app.core.config import APP_CONFIG


def invalidate_brand_caches(
    redis_client,
    brand_id: Optional[Union[str, UUID]] = None,
    organization_id: Optional[Union[str, UUID]] = None
) -> int:
    """
    Invalidate brand-related caches.
    
    Args:
        redis_client: Redis client
        brand_id: Optional brand ID to invalidate specific brand cache
        organization_id: Optional organization ID to invalidate all brand caches for an organization
        
    Returns:
        Number of invalidated keys
    """
    invalidated_count = 0
    
    try:
        if brand_id:
            # Convert brand_id to string if it's a UUID
            brand_id_str = str(brand_id)
            
            # Invalidate specific brand cache
            brand_key = f"CreatorVerse:brand:details:{brand_id_str}"
            if redis_client.delete(brand_key) > 0:
                invalidated_count += 1
                APP_CONFIG.logger.debug(f"Invalidated cache for brand {brand_id_str}")
        
        if organization_id:
            # Convert organization_id to string if it's a UUID
            org_id_str = str(organization_id)
            
            # Invalidate all brand caches for this organization
            org_keys_set = f"CreatorVerse:brand:keys:org:{org_id_str}"
            
            # Get all brand keys for this organization
            all_brand_keys = redis_client.smembers(org_keys_set)
            
            if all_brand_keys:
                # Delete all brand keys
                pipeline = redis_client.pipeline()
                for key in all_brand_keys:
                    pipeline.delete(key)
                    invalidated_count += 1
                
                # Execute pipeline
                pipeline.execute()
                
                # Delete the keys set itself
                redis_client.delete(org_keys_set)
                
                APP_CONFIG.logger.debug(f"Invalidated {len(all_brand_keys)} brand caches for organization {org_id_str}")
        
        # Also invalidate organization brands list if brand or organization cache is invalidated
        if brand_id or organization_id:
            org_id = organization_id or None
            
            if not org_id and brand_id:
                # We need to get the organization ID from the brand cache if available
                brand_id_str = str(brand_id)
                brand_key = f"CreatorVerse:brand:details:{brand_id_str}"
                brand_data = redis_client.get(brand_key)
                
                if brand_data:
                    try:
                        brand_json = json.loads(brand_data)
                        org_id = brand_json.get("organization", {}).get("id")
                    except:
                        pass
            
            if org_id:
                org_id_str = str(org_id)
                org_brands_key = f"CreatorVerse:brands:org:{org_id_str}"
                
                if redis_client.delete(org_brands_key) > 0:
                    invalidated_count += 1
                    APP_CONFIG.logger.debug(f"Invalidated brands list cache for organization {org_id_str}")
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Error invalidating brand caches: {str(e)}")
    
    return invalidated_count


def invalidate_organization_caches(
    redis_client,
    organization_id: Optional[Union[str, UUID]] = None,
    domain: Optional[str] = None
) -> int:
    """
    Invalidate organization-related caches.
    
    Args:
        redis_client: Redis client
        organization_id: Optional organization ID
        domain: Optional domain to invalidate organization by domain
    
    Returns:
        Number of invalidated keys
    """
    invalidated_count = 0
    
    try:
        # Invalidate by domain
        if domain:
            domain_key = f"CreatorVerse:organization:domain:{domain.lower()}"
            if redis_client.delete(domain_key) > 0:
                invalidated_count += 1
                APP_CONFIG.logger.debug(f"Invalidated organization cache for domain {domain}")
        
        # Invalidate by organization ID
        if organization_id:
            org_id_str = str(organization_id)
            
            # Organization members cache
            members_key = f"CreatorVerse:organization:members:{org_id_str}"
            if redis_client.delete(members_key) > 0:
                invalidated_count += 1
                APP_CONFIG.logger.debug(f"Invalidated members cache for organization {org_id_str}")
            
            # Organization details cache - need to get domain first to build key
            domain = None
            
            # Try to get domain from org_id
            with APP_CONFIG.db_helper() as session:
                from database_helper.database.models import Organization
                org = session.get(Organization, UUID(org_id_str))
                if org:
                    domain = org.domain
            
            if domain:
                domain_key = f"CreatorVerse:organization:domain:{domain.lower()}"
                if redis_client.delete(domain_key) > 0:
                    invalidated_count += 1
                    APP_CONFIG.logger.debug(f"Invalidated organization cache for domain {domain}")
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Error invalidating organization caches: {str(e)}")
    
    return invalidated_count


def invalidate_user_brand_caches(
    redis_client,
    user_id: Union[str, UUID]
) -> int:
    """
    Invalidate user-specific brand-related caches.
    
    Args:
        redis_client: Redis client
        user_id: User ID
    
    Returns:
        Number of invalidated keys
    """
    invalidated_count = 0
    
    try:
        user_id_str = str(user_id)
        
        # Invalidate user's brands list
        user_brands_key = f"CreatorVerse:user:brands:{user_id_str}"
        if redis_client.delete(user_brands_key) > 0:
            invalidated_count += 1
            APP_CONFIG.logger.debug(f"Invalidated brands list cache for user {user_id_str}")
    
    except Exception as e:
        APP_CONFIG.logger.error(f"Error invalidating user brand caches: {str(e)}")
    
    return invalidated_count
