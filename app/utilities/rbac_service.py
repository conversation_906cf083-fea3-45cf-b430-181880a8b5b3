from functools import lru_cache
from uuid import UUID

import redis
from database_helper.database.models import (
    MasterR<PERSON>,
    MasterPermission,
    RolePermission,
    User<PERSON>oleModel,
    MasterAuthMethod,
)
from database_helper.database.sync_db2 import SyncDatabaseDB
from sqlalchemy import select

from app.core.config import APP_CONFIG, get_locobuzz_redis, get_database
from app.core.redis_keys import RedisK<PERSON>s, RedisConfig


class RBACService:
    """Service for Role-Based Access Control and Auth-Method operations with Redis caching."""

    def __init__(self, redis_client: redis.Redis, db_conn: SyncDatabaseDB):
        self.redis_client = redis_client
        self.db_conn = db_conn
        self.logger = APP_CONFIG.logger

    def load_rbac_data(self) -> None:
        """Load roles, permissions, role→permission mappings, and auth-methods into Redis."""
        try:
            self._load_roles()
            self._load_permissions()
            self._load_role_permissions()
            self._load_auth_methods()
            self.logger.info("Global RBAC and AuthMethod data loaded into Redis")
        except Exception:
            self.logger.exception("Failed to load RBAC/AuthMethod data")
            raise

    def _load_roles(self) -> None:
        key = RedisKeys.rbac_roles()
        if self.redis_client.hgetall(key):
            return
        result = self.db_conn.execute_query2(select(MasterRole.id, MasterRole.role_name))
        # Ensure all keys and values are strings
        mapping = {str(r.id): str(r.role_name) for r in result}
        if mapping:
            self.redis_client.hset(key, mapping=mapping)
            self.redis_client.expire(key, RedisConfig.RBAC_TTL)

    def _load_permissions(self) -> None:
        key = RedisKeys.rbac_permissions()
        if self.redis_client.hgetall(key):
            return
        result = self.db_conn.execute_query2(select(MasterPermission.id, MasterPermission.permission_key))
        # Ensure all keys and values are strings
        mapping = {str(r.id): str(r.permission_key) for r in result}
        if mapping:
            self.redis_client.hset(key, mapping=mapping)
            self.redis_client.expire(key, RedisConfig.RBAC_TTL)

    def _load_role_permissions(self) -> None:
        result = self.db_conn.execute_query2(
            select(MasterRole.id.label("role_id"), RolePermission.permission_id)
            .join(RolePermission, MasterRole.id == RolePermission.role_id)
        )
        perms: dict[str, list[str]] = {}
        for row in result:
            rid = str(row.role_id)
            perms.setdefault(rid, []).append(str(row.permission_id))
        for rid, pids in perms.items():
            key = RedisKeys.rbac_role_permissions(UUID(rid))
            if not self.redis_client.smembers(key):
                # Ensure all set members are strings
                self.redis_client.sadd(key, *[str(pid) for pid in pids])
                self.redis_client.expire(key, RedisConfig.RBAC_TTL)

    def _load_auth_methods(self) -> None:
        """Load MasterAuthMethod.method_key→id into a single Redis hash."""
        key = RedisKeys.master_auth_methods()  # e.g. "CreatorVerse:master:auth_methods"

        # If already loaded, do nothing
        if self.redis_client.hgetall(key):
            return

        # Fetch from DB
        result = self.db_conn.execute_query2(
            select(MasterAuthMethod.id, MasterAuthMethod.method_key)
        )
        if not result:
            return

        # field=method_key, value=id - ensure all values are strings
        mapping = {str(r.method_key): str(r.id) for r in result}
        self.redis_client.hset(key, mapping=mapping)
        self.redis_client.expire(key, RedisConfig.RBAC_TTL)

    def get_auth_method_id(self, method_key: str) -> UUID | None:
        """
        Retrieve the UUID for a given method_key via Redis.
        Falls back to DB on cache miss and writes back.
        """
        key = RedisKeys.master_auth_methods()

        # Ensure method_key is string for Redis operation
        method_key_str = str(method_key)

        # try cache (decode_responses=True so returns str or None)
        cached = self.redis_client.hget(key, method_key_str)
        if cached:
            return UUID(str(cached))  # Ensure string conversion before UUID

        # cache miss → query DB
        stmt = select(MasterAuthMethod.id).where(
            MasterAuthMethod.method_key == method_key_str
        )
        master_id = self.db_conn.execute_query2(stmt).scalar_one_or_none()
        if master_id is None:
            return None

        # write back for future - ensure all values are strings
        self.redis_client.hset(key, mapping={method_key_str: str(master_id)})
        self.redis_client.expire(key, RedisConfig.RBAC_TTL)
        return master_id

    def get_auth_method_key(self, method_id: UUID) -> str | None:
        """
        Retrieve the method_key for a given UUID via Redis.
        Falls back to DB on cache miss and writes back.
        """
        key = RedisKeys.master_auth_methods()
        str_id = str(method_id)

        # fetch entire hash and invert in-memory
        mapping = self.redis_client.hgetall(key)  # {method_key: id, ...}
        for mkey, mid in mapping.items():
            # Ensure string comparison
            if str(mid) == str_id:
                return str(mkey)

        # cache miss → query DB
        stmt = select(MasterAuthMethod.method_key).where(
            MasterAuthMethod.id == method_id
        )
        method_key = self.db_conn.execute_query2(stmt).scalar_one_or_none()
        if method_key is None:
            return None

        # write back mapping - ensure all values are strings
        self.redis_client.hset(key, mapping={str(method_key): str_id})
        self.redis_client.expire(key, RedisConfig.RBAC_TTL)
        return str(method_key)

    def get_user_roles(self, user_id: UUID) -> set[str]:
        """Get roles for a user using cache-aside pattern."""
        redis_key = RedisKeys.rbac_user_roles(user_id)
        try:
            cached = self.redis_client.smembers(redis_key)
            if cached:
                # Ensure all returned values are strings
                return {str(cid) for cid in cached}
        except redis.RedisError:
            self.logger.warning("Redis down; falling back to DB for user roles")

        self.logger.info(f"Cache miss for user roles, loading from database for user {user_id}")
        query = select(UserRoleModel.role_id).where(UserRoleModel.user_id == user_id)
        result = self.db_conn.execute_query2(query)
        roles = {str(r.role_id) for r in result}

        if roles:
            try:
                # Ensure all set values are strings
                self.redis_client.sadd(redis_key, *[str(role) for role in roles])
                self.redis_client.expire(redis_key, RedisConfig.RBAC_TTL)
            except redis.RedisError:
                self.logger.warning("Failed to cache user roles")

        return roles

    def get_role_permissions(self, role_id: UUID) -> set[str]:
        """Get permissions for a role using cache-aside pattern."""
        redis_key = RedisKeys.rbac_role_permissions(role_id)
        try:
            cached = self.redis_client.smembers(redis_key)
            if cached:
                # Ensure all returned values are strings
                return {str(pid) for pid in cached}
            self.logger.warning(f"Cache miss for role permissions: {role_id}")
            return set()
        except redis.RedisError:
            self.logger.warning("Redis down; returning empty permissions set")
            return set()

    def check_user_permission(self, user_id: UUID, permission_key: str) -> bool:
        """Check if user has a given permission."""
        try:
            roles = self.get_user_roles(user_id)
            if not roles:
                return False

            user_perms: set[str] = set()
            for rid in roles:
                user_perms |= self.get_role_permissions(UUID(rid))

            all_perms = self.redis_client.hgetall(RedisKeys.rbac_permissions())
            if not all_perms:
                self._load_permissions()
                all_perms = self.redis_client.hgetall(RedisKeys.rbac_permissions())

            perm_id = next(
                (pid.decode() for pid, key in all_perms.items() if key.decode() == permission_key),
                None
            )
            return perm_id in user_perms if perm_id else False

        except Exception:
            self.logger.exception("Error checking user permission")
            return False

    def remove_role_from_user(self, user_id: UUID, role_id: UUID) -> bool:
        """Remove a role from a user (DB + Redis) with cache invalidation."""
        try:
            with self.db_conn.transaction() as session:
                ur = (
                    session.query(UserRoleModel)
                    .filter_by(user_id=user_id, role_id=role_id)
                    .first()
                )
                if ur:
                    session.delete(ur)
            try:
                # Ensure role_id is string for Redis
                self.redis_client.srem(RedisKeys.rbac_user_roles(user_id), str(role_id))
            except redis.RedisError:
                self.logger.warning("Failed to update user roles cache after removal")
            return True
        except Exception:
            self.logger.exception("Failed to remove role from user")
            return False

    def add_role_to_user(self, user_id: UUID, role_id: UUID) -> bool:
        """Add a role to a user (DB + Redis) with cache update."""
        try:
            with self.db_conn.transaction() as session:
                session.add(UserRoleModel(user_id=user_id, role_id=role_id))
            try:
                # Ensure role_id is string for Redis
                self.redis_client.sadd(RedisKeys.rbac_user_roles(user_id), str(role_id))
                self.redis_client.expire(RedisKeys.rbac_user_roles(user_id), RedisConfig.RBAC_TTL)
            except redis.RedisError:
                self.logger.warning("Failed to update user roles cache after addition")
            return True
        except Exception:
            self.logger.exception("Failed to add role to user")
            return False

    def invalidate_user_roles_cache(self, user_id: UUID) -> bool:
        """Invalidate user roles cache."""
        try:
            self.redis_client.delete(RedisKeys.rbac_user_roles(user_id))
            self.logger.info(f"Invalidated user roles cache for user {user_id}")
            return True
        except redis.RedisError:
            self.logger.warning(f"Failed to invalidate user roles cache for user {user_id}")
            return False

    def refresh_rbac_cache(self) -> bool:
        """Force refresh all RBAC cache from database."""
        try:
            self.logger.info("Force refreshing RBAC cache")
            try:
                self.redis_client.delete(RedisKeys.rbac_roles())
                self.redis_client.delete(RedisKeys.rbac_permissions())
                self.redis_client.delete(RedisKeys.master_auth_methods())
            except redis.RedisError:
                pass
            self.load_rbac_data()
            return True
        except Exception as e:
            self.logger.error(f"Failed to refresh RBAC cache: {e}")
            return False

    def get_role_id_by_name(self, role_name: str) -> UUID | None:
        """Get role ID by role name from Redis cache."""
        roles_key = RedisKeys.rbac_roles()
        cached_roles = self.redis_client.hgetall(roles_key)
        
        for role_id, name in cached_roles.items():
            # Ensure string comparison
            if str(name) == str(role_name):
                return UUID(str(role_id))
        
        # Cache miss - load from database
        stmt = select(MasterRole.id).where(MasterRole.role_name == role_name)
        role_id = self.db_conn.execute_query2(stmt).scalar_one_or_none()
        
        if role_id:
            # Update cache - ensure all values are strings
            self.redis_client.hset(roles_key, mapping={str(role_id): str(role_name)})
            self.redis_client.expire(roles_key, RedisConfig.RBAC_TTL)
            
        return role_id


@lru_cache(2)
def get_rbac_service(redis_client=get_locobuzz_redis(), db_conn: SyncDatabaseDB = get_database()) -> RBACService:
    """Factory function to get RBAC service instance."""
    return RBACService(redis_client, db_conn)
