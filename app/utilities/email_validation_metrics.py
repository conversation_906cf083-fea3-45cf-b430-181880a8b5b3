"""
Email validation performance monitoring and metrics collection.
"""
import time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from contextlib import contextmanager

from app.core.config import APP_CONFIG


@dataclass
class EmailValidationMetrics:
    """Track email validation performance metrics."""
    
    total_validations: int = 0
    smtp_validations: int = 0
    format_only_validations: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    validation_times: List[float] = field(default_factory=list)
    errors: Dict[str, int] = field(default_factory=dict)
    
    def add_validation_time(self, validation_time: float, validation_type: str) -> None:
        """Add validation time measurement."""
        self.validation_times.append(validation_time)
        self.total_validations += 1
        
        if validation_type == "smtp":
            self.smtp_validations += 1
        elif validation_type == "format_only":
            self.format_only_validations += 1
    
    def add_cache_hit(self) -> None:
        """Record cache hit."""
        self.cache_hits += 1
    
    def add_cache_miss(self) -> None:
        """Record cache miss."""
        self.cache_misses += 1
    
    def add_error(self, error_type: str) -> None:
        """Record validation error."""
        self.errors[error_type] = self.errors.get(error_type, 0) + 1
    
    def get_average_validation_time(self) -> float:
        """Get average validation time."""
        if not self.validation_times:
            return 0.0
        return sum(self.validation_times) / len(self.validation_times)
    
    def get_cache_hit_rate(self) -> float:
        """Get cache hit rate percentage."""
        total_cache_requests = self.cache_hits + self.cache_misses
        if total_cache_requests == 0:
            return 0.0
        return (self.cache_hits / total_cache_requests) * 100
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        return {
            "total_validations": self.total_validations,
            "smtp_validations": self.smtp_validations,
            "format_only_validations": self.format_only_validations,
            "average_validation_time_ms": round(self.get_average_validation_time() * 1000, 2),
            "cache_hit_rate_percent": round(self.get_cache_hit_rate(), 2),
            "total_errors": sum(self.errors.values()),
            "error_breakdown": self.errors,
            "smtp_vs_format_ratio": f"{self.smtp_validations}:{self.format_only_validations}",
        }


# Global metrics instance
_email_validation_metrics = EmailValidationMetrics()


def get_email_validation_metrics() -> EmailValidationMetrics:
    """Get the global email validation metrics instance."""
    return _email_validation_metrics


@contextmanager
def track_email_validation_time(validation_type: str):
    """Context manager to track email validation performance."""
    start_time = time.time()
    try:
        yield
        validation_time = time.time() - start_time
        _email_validation_metrics.add_validation_time(validation_time, validation_type)
        
        # Log slow validations
        if validation_time > 3.0:  # More than 3 seconds
            APP_CONFIG.logger.warning(
                f"Slow email validation detected: {validation_time:.2f}s for type {validation_type}"
            )
        elif validation_time < 0.1:  # Less than 100ms
            APP_CONFIG.logger.debug(
                f"Fast email validation: {validation_time:.3f}s for type {validation_type}"
            )
            
    except Exception as e:
        validation_time = time.time() - start_time
        error_type = type(e).__name__
        _email_validation_metrics.add_error(error_type)
        
        APP_CONFIG.logger.error(
            f"Email validation failed after {validation_time:.2f}s: {error_type} - {str(e)}"
        )
        raise


def log_email_validation_summary() -> None:
    """Log email validation performance summary."""
    metrics = _email_validation_metrics.get_performance_summary()
    
    APP_CONFIG.logger.info("=== Email Validation Performance Summary ===")
    APP_CONFIG.logger.info(f"Total validations: {metrics['total_validations']}")
    APP_CONFIG.logger.info(f"SMTP validations: {metrics['smtp_validations']}")
    APP_CONFIG.logger.info(f"Format-only validations: {metrics['format_only_validations']}")
    APP_CONFIG.logger.info(f"Average validation time: {metrics['average_validation_time_ms']}ms")
    APP_CONFIG.logger.info(f"Cache hit rate: {metrics['cache_hit_rate_percent']}%")
    APP_CONFIG.logger.info(f"Total errors: {metrics['total_errors']}")
    
    if metrics['error_breakdown']:
        APP_CONFIG.logger.info(f"Error breakdown: {metrics['error_breakdown']}")
    
    APP_CONFIG.logger.info("=" * 45)


def reset_email_validation_metrics() -> None:
    """Reset email validation metrics (useful for testing)."""
    global _email_validation_metrics
    _email_validation_metrics = EmailValidationMetrics()


# Performance thresholds for alerting
PERFORMANCE_THRESHOLDS = {
    "max_validation_time_seconds": 5.0,
    "min_cache_hit_rate_percent": 70.0,
    "max_error_rate_percent": 5.0,
}


def check_performance_alerts() -> List[str]:
    """Check for performance issues and return alerts."""
    alerts = []
    metrics = _email_validation_metrics.get_performance_summary()
    
    # Check average validation time
    avg_time_seconds = metrics['average_validation_time_ms'] / 1000
    if avg_time_seconds > PERFORMANCE_THRESHOLDS['max_validation_time_seconds']:
        alerts.append(
            f"High average validation time: {avg_time_seconds:.2f}s "
            f"(threshold: {PERFORMANCE_THRESHOLDS['max_validation_time_seconds']}s)"
        )
    
    # Check cache hit rate
    cache_hit_rate = metrics['cache_hit_rate_percent']
    if cache_hit_rate < PERFORMANCE_THRESHOLDS['min_cache_hit_rate_percent']:
        alerts.append(
            f"Low cache hit rate: {cache_hit_rate:.1f}% "
            f"(threshold: {PERFORMANCE_THRESHOLDS['min_cache_hit_rate_percent']}%)"
        )
    
    # Check error rate
    if metrics['total_validations'] > 0:
        error_rate = (metrics['total_errors'] / metrics['total_validations']) * 100
        if error_rate > PERFORMANCE_THRESHOLDS['max_error_rate_percent']:
            alerts.append(
                f"High error rate: {error_rate:.1f}% "
                f"(threshold: {PERFORMANCE_THRESHOLDS['max_error_rate_percent']}%)"
            )
    
    return alerts
