"""
Role validation utilities for API endpoints.
Provides role-based access control for specific endpoints.
"""
from typing import Dict, List, Optional, Set, Any
from fastapi import HTTPException, status

from app.core.config import APP_CONFIG
from app.core.redis_keys import RedisKeys


class RoleValidator:
    """
    Role validation utility for API endpoints.
    Validates roles against master roles and provides endpoint-specific validation.
    """
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
    
    def get_master_roles(self) -> Dict[str, str]:
        """
        Get master roles from Redis cache with fallback to default roles.
        
        Returns:
            Dictionary mapping role_uuid to role_name
        """
        try:
            # Try to get roles from Redis cache
            roles_cache = self.redis_client.hgetall(RedisKeys.rbac_roles())
            if roles_cache:
                APP_CONFIG.logger.debug(f"Retrieved {len(roles_cache)} roles from cache")
                return roles_cache
            
            # Fallback to default roles if cache is empty
            default_roles = {
                "11239913-1b15-4724-b176-270dff840ef7": "influencer",
                "9798e242-f889-497f-8a25-65479a57f7fa": "org_owner", 
                "4d0810d1-17be-444c-9376-add7822ce349": "org_admin",
                "2389b63c-b9c1-4e81-b786-8ba2eaa58bf3": "brand_user"
            }
            
            APP_CONFIG.logger.warning("Using default roles - Redis cache empty")
            return default_roles
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to retrieve master roles: {e}")
            # Return default roles as fallback
            return {
                "11239913-1b15-4724-b176-270dff840ef7": "influencer",
                "9798e242-f889-497f-8a25-65479a57f7fa": "org_owner",
                "4d0810d1-17be-444c-9376-add7822ce349": "org_admin", 
                "2389b63c-b9c1-4e81-b786-8ba2eaa58bf3": "brand_user"
            }
    
    def validate_role_exists(self, role_uuid: str) -> str:
        """
        Validate that role UUID exists in master roles.
        
        Args:
            role_uuid: Role UUID to validate
            
        Returns:
            Role name if valid
            
        Raises:
            HTTPException: If role UUID is invalid
        """
        if not role_uuid or not role_uuid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role UUID is required"
            )
        
        master_roles = self.get_master_roles()
        role_name = master_roles.get(role_uuid.strip())
        
        if not role_name:
            available_roles = list(master_roles.keys())
            APP_CONFIG.logger.warning(f"Invalid role UUID provided: {role_uuid}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid role UUID. Available roles: {available_roles}"
            )
        
        return role_name
    
    def validate_role_for_endpoint(self, role_uuid: str, endpoint_type: str) -> str:
        """
        Validate role for specific endpoint type.
        
        Args:
            role_uuid: Role UUID to validate
            endpoint_type: Type of endpoint ('influencer', 'brand', 'admin', etc.)
            
        Returns:
            Role name if valid for endpoint
            
        Raises:
            HTTPException: If role is not valid for endpoint
        """
        role_name = self.validate_role_exists(role_uuid)
        
        # Define allowed roles for each endpoint type
        endpoint_role_map = {
            "influencer": {"influencer"},
            "brand": {"org_owner", "org_admin", "brand_user"},
            "admin": {"org_owner", "org_admin"},
            "user": {"influencer", "org_owner", "org_admin", "brand_user"}  # All roles
        }
        
        allowed_roles = endpoint_role_map.get(endpoint_type.lower(), set())
        
        if role_name not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role '{role_name}' is not allowed for {endpoint_type} endpoints. "
                       f"Allowed roles: {list(allowed_roles)}"
            )
        
        APP_CONFIG.logger.info(f"Role validation successful: {role_name} for {endpoint_type} endpoint")
        return role_name
    
    def validate_influencer_role(self, role_uuid: str) -> str:
        """
        Validate role specifically for influencer endpoints.
        
        Args:
            role_uuid: Role UUID to validate
            
        Returns:
            Role name if valid for influencer
            
        Raises:
            HTTPException: If role is not influencer
        """
        return self.validate_role_for_endpoint(role_uuid, "influencer")
    
    def validate_brand_role(self, role_uuid: str) -> str:
        """
        Validate role specifically for brand endpoints.
        
        Args:
            role_uuid: Role UUID to validate
            
        Returns:
            Role name if valid for brand
            
        Raises:
            HTTPException: If role is not a brand role
        """
        return self.validate_role_for_endpoint(role_uuid, "brand")


def get_role_validator(redis_client) -> RoleValidator:
    """
    Get role validator instance.
    
    Args:
        redis_client: Redis client instance
        
    Returns:
        RoleValidator instance
    """
    return RoleValidator(redis_client)


def validate_role_for_influencer_registration(role_uuid: str, redis_client) -> str:
    """
    Validate role for influencer registration endpoint.
    
    Args:
        role_uuid: Role UUID to validate
        redis_client: Redis client instance
        
    Returns:
        Role name if valid
        
    Raises:
        HTTPException: If role is invalid for influencer registration
    """
    validator = get_role_validator(redis_client)
    return validator.validate_influencer_role(role_uuid)


def validate_role_for_brand_registration(role_uuid: str, redis_client) -> str:
    """
    Validate role for brand registration endpoint.
    
    Args:
        role_uuid: Role UUID to validate
        redis_client: Redis client instance
        
    Returns:
        Role name if valid
        
    Raises:
        HTTPException: If role is invalid for brand registration
    """
    validator = get_role_validator(redis_client)
    return validator.validate_brand_role(role_uuid)


def get_available_roles_response(redis_client) -> Dict[str, Any]:
    """
    Get available roles in API response format.
    
    Args:
        redis_client: Redis client instance
        
    Returns:
        Dictionary with role information
    """
    validator = get_role_validator(redis_client)
    master_roles = validator.get_master_roles()
    
    return {
        "message": "Master roles retrieved successfully",
        "data": master_roles
    }
