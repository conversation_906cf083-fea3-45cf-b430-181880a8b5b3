import asyncio
from datetime import datetime
from typing import Any, Dict
from urllib.parse import urlencode

import httpx
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build

from app.core.config import APP_CONFIG
from app.schemas.auth import GoogleTokenResponse, GoogleUserInfo, InstagramTokenResponse, InstagramUserInfo

# OAuth Configuration is now in AppConfig
GOOGLE_SCOPE_YT = "https://www.googleapis.com/auth/youtube.readonly"

# YouTube availability check
try:
    from googleapiclient.discovery import build
    YOUTUBE_AVAILABLE = True
except ImportError:
    YOUTUBE_AVAILABLE = False


class GoogleOAuthHandler:
    """Handles Google OAuth operations"""
    
    @staticmethod
    def generate_auth_url(redirect_uri: str, state: str) -> str:
        """Generate Google OAuth authorization URL"""
        params = {
            "client_id": APP_CONFIG.oauth_google_client_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": f"openid email profile {GOOGLE_SCOPE_YT}",
            "state": state,
            "access_type": "offline",
            "prompt": "consent",
        }
        return f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"
    
    @staticmethod
    async def exchange_code_for_tokens(code: str, redirect_uri: str) -> GoogleTokenResponse:
        """Exchange authorization code for tokens"""
        token_data = {
            "client_id": APP_CONFIG.oauth_google_client_id,
            "client_secret": APP_CONFIG.oauth_google_client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://oauth2.googleapis.com/token",
                data=token_data,
                headers={"Accept": "application/json"},
            )
            if response.status_code != 200:
                raise Exception(f"Failed to exchange code for tokens: {response.text}")
            
            token_response = response.json()
            return GoogleTokenResponse(**token_response)
    
    @staticmethod
    async def get_user_info(id_token: str) -> GoogleUserInfo:
        """Decode Google ID token and return user info"""
        from jose import jwt
        
        try:
            decoded_token = jwt.get_unverified_claims(id_token)
            
            # Verify issuer
            if decoded_token.get("iss") not in ["accounts.google.com", "https://accounts.google.com"]:
                raise Exception("Invalid issuer in ID token")
            
            # Verify audience
            if decoded_token.get("aud") != APP_CONFIG.oauth_google_client_id:
                raise Exception("Invalid audience in ID token")

            return GoogleUserInfo(
                sub=decoded_token["sub"],
                email=decoded_token["email"],
                name=decoded_token.get("name", ""),
                picture=decoded_token.get("picture"),
                email_verified=decoded_token.get("email_verified", False),
            )
        except Exception as e:
            raise Exception(f"Failed to decode ID token: {str(e)}")
    
    @staticmethod
    async def fetch_youtube_profile(access_token: str, refresh_token: str, expires_at: datetime) -> Dict[str, Any]:
        """Fetch YouTube channel profile data"""
        loop = asyncio.get_running_loop()

        def _worker():
            try:
                creds = Credentials(
                    token=access_token,
                    refresh_token=refresh_token,
                    token_uri="https://oauth2.googleapis.com/token",
                    client_id=APP_CONFIG.oauth_google_client_id,
                    client_secret=APP_CONFIG.oauth_google_client_secret,
                    scopes=[GOOGLE_SCOPE_YT],
                    expiry=expires_at,
                )
                
                yt = build("youtube", "v3", credentials=creds, cache_discovery=False)
                response = yt.channels().list(part="snippet,statistics", mine=True).execute()
                
                if not response.get("items"):
                    raise Exception("No YouTube channel found for this account")
                
                # Return a flag indicating channel selection is needed
                # This will be processed by the OAuth flow to redirect the user
                # to the channel selection interface instead of auto-selecting
                # the first channel
                return {
                    "_needs_channel_selection": True,
                    "_channel_count": len(response.get("items", [])),
                    # Return first channel data for backward compatibility
                    **response["items"][0]
                }
            except Exception as e:
                raise Exception(f"YouTube API error: {str(e)}")

        return await loop.run_in_executor(None, _worker)


class InstagramOAuthHandler:
    """Handles Instagram OAuth operations"""
    
    @staticmethod
    def generate_auth_url(redirect_uri: str, state: str) -> str:
        """Generate Instagram OAuth authorization URL"""
        params = {
            "client_id": APP_CONFIG.oauth_instagram_client_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": "user_profile,user_media",
            "state": state,
        }
        return f"https://api.instagram.com/oauth/authorize?{urlencode(params)}"
    
    @staticmethod
    async def exchange_code_for_tokens(code: str, redirect_uri: str) -> InstagramTokenResponse:
        """Exchange authorization code for tokens"""
        token_url = "https://api.instagram.com/oauth/access_token"
        data = {
            "client_id": APP_CONFIG.oauth_instagram_client_id,
            "client_secret": APP_CONFIG.oauth_instagram_client_secret,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
            "code": code,
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(token_url, data=data)
                response.raise_for_status()
                token_data = response.json()
                return InstagramTokenResponse(
                    access_token=token_data["access_token"],
                    user_id=str(token_data["user_id"]),
                )
        except Exception as e:
            APP_CONFIG.logger.error(f"Instagram token exchange failed: {str(e)}")
            raise Exception("Instagram token exchange failed")
    
    @staticmethod
    async def get_user_info(access_token: str) -> InstagramUserInfo:
        """Fetch Instagram user info"""
        user_info_url = "https://graph.instagram.com/me"
        params = {"fields": "id,username,account_type", "access_token": access_token}

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(user_info_url, params=params)
                response.raise_for_status()
                user_data = response.json()
                return InstagramUserInfo(
                    id=str(user_data["id"]),
                    username=user_data["username"],
                    account_type=user_data.get("account_type", "PERSONAL"),
                )
        except Exception as e:
            APP_CONFIG.logger.error(f"Instagram user info fetch failed: {str(e)}")
            raise Exception("Instagram user info fetch failed")

    @staticmethod
    async def fetch_instagram_pages(access_token: str, user_id: str) -> Dict[str, Any]:
        """Fetch Instagram pages connected to Facebook account"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Get Facebook pages with Instagram accounts
                pages_url = "https://graph.facebook.com/v23.0/me/accounts"
                params = {
                    "access_token": access_token,
                    "fields": "id,name,access_token,instagram_business_account"
                }
                
                response = await client.get(pages_url, params=params)
                response.raise_for_status()
                pages_data = response.json()
                
                # Count pages with Instagram accounts
                instagram_pages = []
                for page in pages_data.get('data', []):
                    if 'instagram_business_account' in page:
                        instagram_pages.append(page)
                
                if not instagram_pages:
                    raise Exception("No Instagram pages found for this Facebook account")
                
                # Return a flag indicating page selection is needed if multiple pages
                result = {
                    "_needs_page_selection": len(instagram_pages) > 1,
                    "_page_count": len(instagram_pages),
                }
                
                # Add first page data for backward compatibility
                if instagram_pages:
                    result.update(instagram_pages[0])
                
                return result
                
        except Exception as e:
            APP_CONFIG.logger.error(f"Instagram pages fetch failed: {str(e)}")
            raise Exception(f"Instagram pages API error: {str(e)}")


class FacebookOAuthHandler:
    """Handles Facebook OAuth operations (including Instagram pages)"""
    
    @staticmethod
    def generate_auth_url(redirect_uri: str, state: str) -> str:
        """Generate Facebook OAuth authorization URL"""
        params = {
            "client_id": APP_CONFIG.oauth_instagram_client_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": "public_profile,email,pages_show_list,pages_read_engagement,instagram_basic,instagram_manage_comments,instagram_manage_insights,instagram_content_publish,pages_manage_metadata",
            "state": state,
            "return_scopes": True,
            "enable_profile_selector": True
        }
        return f"https://www.facebook.com/v23.0/dialog/oauth?{urlencode(params)}"
    
    @staticmethod
    async def exchange_code_for_tokens(code: str, redirect_uri: str) -> InstagramTokenResponse:
        """Exchange Facebook authorization code for tokens"""
        token_url = "https://graph.facebook.com/v23.0/oauth/access_token"
        data = {
            "client_id": APP_CONFIG.oauth_instagram_client_id,
            "client_secret": APP_CONFIG.oauth_instagram_client_secret,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
            "code": code,
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(token_url, data=data)
                response.raise_for_status()
                token_data = response.json()
                
                # For Facebook, we get a long-lived token by default
                return InstagramTokenResponse(
                    access_token=token_data["access_token"],
                    user_id=str(token_data.get("user_id", ""))
                )
        except Exception as e:
            APP_CONFIG.logger.error(f"Facebook token exchange failed: {str(e)}")
            raise Exception("Facebook token exchange failed")
    
    @staticmethod
    async def get_user_info(access_token: str) -> InstagramUserInfo:
        """Fetch Facebook user info"""
        user_info_url = "https://graph.facebook.com/v23.0/me"
        params = {"fields": "id,name,email", "access_token": access_token}

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(user_info_url, params=params)
                response.raise_for_status()
                user_data = response.json()
                return InstagramUserInfo(
                    id=str(user_data["id"]),
                    username=user_data.get("name", ""),
                    account_type="PERSONAL",
                )
        except Exception as e:
            APP_CONFIG.logger.error(f"Facebook user info fetch failed: {str(e)}")
            raise Exception("Facebook user info fetch failed")

    @staticmethod
    async def fetch_instagram_pages(access_token: str, user_id: str) -> Dict[str, Any]:
        """Fetch Instagram pages connected to Facebook account"""
        return await InstagramOAuthHandler.fetch_instagram_pages(access_token, user_id)


def get_oauth_handler(provider: str):
    """Factory function to get OAuth handler"""
    handlers = {
        "google": GoogleOAuthHandler,
        "instagram": InstagramOAuthHandler,
        "facebook": FacebookOAuthHandler,
    }
    
    handler = handlers.get(provider.lower())
    if not handler:
        raise ValueError(f"Unsupported OAuth provider: {provider}")
    
    return handler
