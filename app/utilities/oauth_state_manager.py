"""
OAuth state management with enhanced role_uuid support.
Handles both modern structured data and legacy format for backward compatibility.
"""
import time
import json
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, UTC
from app.core.config import APP_CONFIG


class InMemoryStateStore:
    """Fallback in-memory state store when <PERSON><PERSON> is unavailable"""

    def __init__(self) -> None:
        self._store: dict[str, tuple[Dict[str, Any], float]] = {}
        self._cleanup_interval = 60  # seconds
        self._last_cleanup = time.time()

    def set_state(self, state: str, state_data: Dict[str, Any], expiry_seconds: int = 300) -> None:
        """
        Store OAuth state with expiry in an in-memory dict.
        state → (state_data_dict, expiry_time)
        """
        expiry_time = time.time() + expiry_seconds
        self._store[state] = (state_data, expiry_time)
        self._cleanup_expired()

    def get_and_delete_state(self, state: str) -> Optional[Dict[str, Any]]:
        """
        Return the state data if state is valid (not expired), and delete it from store.
        """
        self._cleanup_expired()
        if state in self._store:
            state_data, expiry_time = self._store.pop(state)
            if time.time() < expiry_time:
                return state_data
        return None

    def _cleanup_expired(self) -> None:
        """Remove any expired states from the in-memory dict."""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            expired = [
                s for s, (_, exp) in self._store.items() if current_time > exp
            ]
            for s in expired:
                self._store.pop(s, None)
            self._last_cleanup = current_time


class OAuthStateManager:
    """Enhanced OAuth state manager with role-aware storage and retrieval"""

    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.fallback_store = InMemoryStateStore()

    def store_state(self, 
                  state: str, 
                  provider: str, 
                  user_type: str, 
                  role_uuid: Optional[str] = None, 
                  extra_data: Optional[Dict[str, Any]] = None,
                  expiry_seconds: int = 300) -> None:
        """
        Store OAuth state with provider, user_type, and optionally role_uuid
        
        Args:
            state: Random state token
            provider: OAuth provider (google, instagram)
            user_type: User type (influencer, brand)
            role_uuid: Optional UUID of role
            extra_data: Optional additional data to store
            expiry_seconds: Expiration time in seconds
        """

        # Get it from the redis keys file

        from app.core.redis_keys import RedisKeys
        state_key = RedisKeys.oauth_state(state)

        # Build state data with provider, user_type and optional role_uuid
        state_data = {
            "provider": provider,
            "user_type": user_type,
            "timestamp": int(datetime.now(UTC).timestamp())
        }
            
        # Add any extra data
        if extra_data:
            state_data.update(extra_data)
            
        # Convert to string for backwards compatibility
        value = f"{provider}:{user_type}"
        
        try:
            self.redis_client.set(state_key, value, ex=expiry_seconds)
            APP_CONFIG.logger.debug(f"OAuth state stored in Redis: {state_key}")
        except Exception as e:
            APP_CONFIG.logger.warning(
                f"Redis unavailable, using in-memory fallback for {state_key}: {str(e)}"
            )
            self.fallback_store.set_state(state, state_data, expiry_seconds)

    async def pop_state(self, state: str) -> Tuple[Optional[str], Optional[str], Optional[str], Dict[str, Any]]:
        """
        Retrieve and delete OAuth state, returning provider, user_type, role_uuid, and extra data
        
        Returns:
            Tuple of (provider, user_type, role_uuid, extra_data)
            provider and user_type may be None if state not found
            role_uuid may be None if not stored
            extra_data is an empty dict if no extra data was stored
        """
        state_key = f"CreatorVerse:oauth:state:{state}"
        extra_data: Dict[str, Any] = {}
        
        try:
            # Try to get state from Redis
            stored = self.redis_client.get(state_key)
            if stored:
                self.redis_client.delete(state_key)
                
                # Process stored value
                stored_str = stored.decode() if isinstance(stored, bytes) else stored
                APP_CONFIG.logger.debug(f"OAuth state retrieved from Redis: {state_key}")
                
                # First check if it's JSON format
                try:
                    data = json.loads(stored_str)
                    provider = data.get("provider")
                    user_type = data.get("user_type")
                    role_uuid = data.get("role_uuid")
                    
                    # Extract extra data
                    extra_data = data.copy()
                    for field in ["provider", "user_type", "role_uuid", "timestamp"]:
                        extra_data.pop(field, None)
                        
                    return provider, user_type, role_uuid, extra_data
                except json.JSONDecodeError:
                    # Not JSON, try legacy format (provider:user_type:role_uuid)
                    parts = stored_str.split(":", 2)
                    if len(parts) >= 2:
                        provider, user_type = parts[0], parts[1]
                        role_uuid = parts[2] if len(parts) > 2 else None
                        return provider, user_type, role_uuid, extra_data
                    else:
                        APP_CONFIG.logger.warning(f"Invalid legacy state format: {stored_str}")
        except Exception as e:
            APP_CONFIG.logger.warning(f"Redis error, trying fallback: {str(e)}")
        
        # Fallback to in-memory store
        stored_data = self.fallback_store.get_and_delete_state(state)
        if stored_data:
            APP_CONFIG.logger.debug(f"OAuth state retrieved from fallback: {state}")
            provider = stored_data.get("provider")
            user_type = stored_data.get("user_type")
            role_uuid = stored_data.get("role_uuid")
            
            # Get any extra data
            extra_data = stored_data.copy()
            for field in ["provider", "user_type", "role_uuid", "timestamp"]:
                if field in extra_data:
                    extra_data.pop(field)
                
            return provider, user_type, role_uuid, extra_data
            
        return None, None, None, {}
    
    # For backward compatibility
    async def pop_oauth_state(self, state: str) -> Optional[str]:
        """
        Compatibility method for OAuthService.pop_oauth_state
        Returns legacy format "provider:user_type"
        """
        provider, user_type, role_uuid, _ = await self.pop_state(state)
        if provider and user_type:
            result = f"{provider}:{user_type}"
            if role_uuid:
                result = f"{result}:{role_uuid}"
            return result
        return None


def get_oauth_state_manager(redis_client) -> OAuthStateManager:
    """Factory function to get OAuthStateManager instance"""
    return OAuthStateManager(redis_client)
