import asyncio
import json
import uuid
from datetime import UTC, datetime, timed<PERSON><PERSON>
from typing import <PERSON><PERSON>
from uuid import UUID

from database_helper.database.models import User<PERSON>ession, <PERSON><PERSON><PERSON>
from sqlalchemy.orm.session import Session

from app.core.config import get_database, APP_CONFIG, get_locobuzz_redis
from app.core.enums_data import User<PERSON><PERSON>Enum
from app.core.redis_keys import RedisConfig, RedisKeys
from app.core.security import create_access_token, create_refresh_token
from app.core.exceptions import ServiceUnavailableError, BusinessLogicError
from app.schemas.auth import OAuthTokenResponse
from app.utilities.rbac_service import get_rbac_service, RBACService


class TokenServiceError(Exception):
    """Custom exception for token service errors"""

    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


async def enforce_session_limit_before_token_issue(user_id: str, redis_client=get_locobuzz_redis()) -> None:
    """Enforce session limits before issuing new tokens."""
    from app.core.database_helper.users_helper import enforce_max_sessions

    db_conn = get_database()
    await enforce_max_sessions(user_id, redis_client, db_conn)


def _validate_redis_client(redis_client):
    """Validate Redis client and raise error if None"""
    if redis_client is None:
        APP_CONFIG.logger.error("Redis client is None")
        raise TokenServiceError("Service temporarily unavailable", 500)


def _get_role_info_from_redis(redis_client, role_id: int) -> Tuple[str, str]:
    """Get role ID and name from Redis cache"""
    roles_redis_key = RedisKeys.rbac_roles()
    try:
        cached_roles = redis_client.hgetall(roles_redis_key)
    except Exception as e:
        APP_CONFIG.logger.error(f"Redis error fetching roles: {str(e)}")
        cached_roles = {}

    target_role_id: str | None = None
    target_role_name = UserRoleEnum.get_role_name(role_id)

    for rid, rname in cached_roles.items():
        if rname == target_role_name:
            target_role_id = rid
            break

    if not target_role_id:
        raise TokenServiceError(f"Role not found for tenant", 500)

    return target_role_id, target_role_name


def _get_role_info_from_uuid(redis_client, session: Session, role_uuid: str) -> str:
    """Get role name from UUID using Redis cache first, fallback to DB"""
    try:
        roles_cache = redis_client.hgetall(RedisKeys.rbac_roles())
        role_name = roles_cache.get(role_uuid)
    except Exception as e:
        APP_CONFIG.logger.error(f"Redis error fetching roles cache: {str(e)}")
        role_name = None

    if not role_name:
        role = session.query(MasterRole).filter_by(id=role_uuid).first()
        if not role:
            raise TokenServiceError("Invalid role UUID provided", 400)
        role_name = role.name

    return role_name


def _set_user_roles_in_redis(redis_client, user_id: str, role_id: str) -> None:
    """Set user roles in Redis with error handling"""
    user_roles_key = RedisKeys.rbac_user_roles(user_id)
    try:
        redis_client.sadd(user_roles_key, role_id)
        redis_client.expire(user_roles_key, RedisConfig.RBAC_TTL)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Failed to set user roles in Redis: {str(e)}")


def _get_permissions_from_redis(redis_client, role_id: str) -> list[str]:
    """Fetch permissions for a role from Redis"""
    role_perms_key = RedisKeys.rbac_role_permissions(role_id)
    permissions_key = RedisKeys.rbac_permissions()
    permissions_list: list[str] = []

    try:
        perm_ids = redis_client.smembers(role_perms_key)
        cached_perms = redis_client.hgetall(permissions_key)

        for pid in perm_ids:
            pkey = cached_perms.get(pid)
            if pkey:
                permissions_list.append(pkey)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Failed to fetch permissions from Redis: {str(e)}")

    return permissions_list


def _create_session_record(user_id: str, session_id: str, access_token: str,
                           refresh_token: str, **kwargs) -> UserSession:
    """Create a UserSession record"""
    return UserSession(
        id=session_id,
        user_id=user_id,
        access_token=access_token,
        refresh_token=refresh_token,
        expires_at=datetime.now(UTC) + timedelta(days=7),
        is_revoked=False,
        issued_at=datetime.now(UTC),
        ip_address=kwargs.get("ip_address", ""),
        user_agent=kwargs.get("user_agent", "")
    )


def _cache_session_in_redis(redis_client, user_id: str, session_id: str,
                            access_token: str, refresh_token: str, **kwargs) -> None:
    """Cache session record in Redis"""
    session_key = RedisKeys.session_key(user_id, session_id)
    user_session_hash = {
        "user_id": str(user_id),
        "access_token": access_token,
        "refresh_token": refresh_token,
        "issued_at": str(int(datetime.now(UTC).timestamp())),
        "expires_at": str(int((datetime.now(UTC) + timedelta(days=7)).timestamp())),
        "is_revoked": "0",
    }

    if kwargs.get("ip_address"):
        user_session_hash["ip_address"] = kwargs.get("ip_address", "")
    if kwargs.get("user_agent"):
        user_session_hash["user_agent"] = kwargs.get("user_agent", "")

    try:
        redis_client.hset(session_key, mapping=user_session_hash)
        redis_client.expire(session_key, RedisConfig.REFRESH_TOKEN_TTL)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Failed to cache session in Redis: {str(e)}")


def _cache_refresh_token_in_redis(redis_client, user_id: str, refresh_token: str,
                                  role_name: str, permissions_list: list[str]) -> None:
    """Cache refresh token in Redis"""
    refresh_key = RedisKeys.refresh_token(user_id, refresh_token)
    refresh_data = {
        "user_id": str(user_id),
        "roles": json.dumps([role_name]),
        "permissions": json.dumps(permissions_list),
    }

    try:
        redis_client.hset(refresh_key, mapping=refresh_data)
        redis_client.expire(refresh_key, RedisConfig.REFRESH_TOKEN_TTL)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Failed to cache refresh token in Redis: {str(e)}")


def _run_async_in_sync_context(coro):
    """Helper to run async function in sync context"""
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(asyncio.run, coro)
                return future.result()
        else:
            return asyncio.run(coro)
    except RuntimeError:
        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(asyncio.run, coro)
            return future.result()


async def issue_tokens_for_user_async(
        user_id: str,
        role_id: int,
        redis_client=get_locobuzz_redis(),
        session: Session = get_database(),
        **kwargs
) -> Tuple[OAuthTokenResponse, str | uuid.UUID]:
    """Async version of token issuance with session limit enforcement."""
    _validate_redis_client(redis_client)
    await enforce_session_limit_before_token_issue(user_id, redis_client)

    # Get role information
    target_role_id, target_role_name = _get_role_info_from_redis(redis_client, role_id)

    # Set user roles and get permissions
    _set_user_roles_in_redis(redis_client, user_id, target_role_id)
    permissions_list = _get_permissions_from_redis(redis_client, target_role_id)

    # Create tokens
    session_id = str(uuid.uuid4())
    payload = {
        "iss": "creator.verse",
        "sub": str(user_id),
        "ssid": session_id,
        "roles": [target_role_name],
        "permissions": permissions_list,
    }
    access_token = create_access_token(payload)
    refresh_token = create_refresh_token()

    # Persist and cache
    session_record = _create_session_record(user_id, session_id, access_token, refresh_token, **kwargs)
    session.add(session_record)

    _cache_session_in_redis(redis_client, user_id, session_id, access_token, refresh_token, **kwargs)
    _cache_refresh_token_in_redis(redis_client, user_id, refresh_token, target_role_name, permissions_list)

    return OAuthTokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=30 * 60,
    ), target_role_id


def issue_tokens_for_user(
        user_id: str,
        role_id: int,
        redis_client=get_locobuzz_redis(),
        session: Session = get_database(),
        **kwargs
) -> tuple[OAuthTokenResponse, str | UUID]:
    """Sync wrapper for token issuance."""
    return _run_async_in_sync_context(
        issue_tokens_for_user_async(user_id, role_id, redis_client, session, **kwargs)
    )


async def issue_tokens_for_user_with_uuid_role_async(
        user_id: str,
        role_uuid: str,
        redis_client=get_locobuzz_redis(),
        session: Session = get_database(),
        **kwargs
) -> Tuple[OAuthTokenResponse, str]:
    """Async version of token issuance with UUID role."""
    _validate_redis_client(redis_client)
    await enforce_session_limit_before_token_issue(user_id, redis_client)

    # Get role information
    role_name = _get_role_info_from_uuid(redis_client, session, role_uuid)

    # Set user roles and get permissions
    _set_user_roles_in_redis(redis_client, user_id, role_uuid)
    permissions_list = _get_permissions_from_redis(redis_client, role_uuid)

    # Create tokens
    session_id = str(uuid.uuid4())
    payload = {
        "iss": "creator.verse",
        "sub": str(user_id),
        "ssid": session_id,
        "roles": [role_name],
        "permissions": permissions_list,
    }
    access_token = create_access_token(payload)
    refresh_token = create_refresh_token()

    # Persist and cache
    session_record = _create_session_record(user_id, session_id, access_token, refresh_token, **kwargs)
    session.add(session_record)

    _cache_session_in_redis(redis_client, user_id, session_id, access_token, refresh_token, **kwargs)
    _cache_refresh_token_in_redis(redis_client, user_id, refresh_token, role_name, permissions_list)

    return OAuthTokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=30 * 60,
    ), role_uuid


def issue_tokens_for_user_with_uuid_role(
        user_id: str,
        role_uuid: str,
        redis_client=get_locobuzz_redis(),
        session: Session = None,
        **kwargs
) -> tuple[OAuthTokenResponse, str]:
    """Sync wrapper for UUID role token issuance."""
    try:
        if session is None:
            # If no session provided, we need to get database connection
            db_conn = get_database()
            with db_conn.transaction() as new_session:
                return _run_async_in_sync_context(
                    issue_tokens_for_user_with_uuid_role_async(user_id, role_uuid, redis_client, new_session, **kwargs)
                )
        else:
            # Use provided session
            return _run_async_in_sync_context(
                issue_tokens_for_user_with_uuid_role_async(user_id, role_uuid, redis_client, session, **kwargs)
            )
    except TokenServiceError as e:
        # Re-raise as appropriate CreatorVerse exception
        if "Service temporarily unavailable" in e.message:
            raise ServiceUnavailableError("token", e.message)
        else:
            raise BusinessLogicError(e.message, e.status_code)


async def issue_token_only(
        user_id: str,
        session: Session = None,
        rbac_service: RBACService = None,
        **kwargs
) -> None:
    """Issue token without explicit role assignment."""
    if session is None:
        session = get_database()
    if rbac_service is None:
        rbac_service = get_rbac_service()

    redis_client = rbac_service.redis_client
    user_roles = rbac_service.get_user_roles(user_id)
    user_permissions = rbac_service.get_role_permissions(user_roles[0]) if user_roles else set()

    # Create tokens
    session_id = str(uuid.uuid4())
    payload = {
        "iss": "creator.verse",
        "sub": str(user_id),
        "ssid": session_id,
        "roles": ["influencer"],
        "permissions": list(user_permissions),
    }
    access_token = create_access_token(payload)
    refresh_token = create_refresh_token()

    # Persist and cache
    session_record = _create_session_record(user_id, session_id, access_token, refresh_token, **kwargs)
    session.add(session_record)

    _cache_session_in_redis(redis_client, user_id, session_id, access_token, refresh_token, **kwargs)

    # Cache refresh token
    refresh_key = RedisKeys.refresh_token(user_id, refresh_token)
    refresh_data = {
        "user_id": str(user_id),
        "roles": json.dumps(["influencer"]),
        "permissions": json.dumps(list(user_permissions)),
    }
    try:
        redis_client.hset(refresh_key, mapping=refresh_data)
        redis_client.expire(refresh_key, RedisConfig.REFRESH_TOKEN_TTL)
    except Exception as e:
        APP_CONFIG.logger.warning(f"Failed to cache refresh token in Redis: {str(e)}")


if __name__ == "__main__":
    db_conn = get_database()
    with db_conn.transaction() as session:
        asyncio.run(issue_token_only("2f78c95c-7ac7-438a-af70-3563c980b0d1", session))
