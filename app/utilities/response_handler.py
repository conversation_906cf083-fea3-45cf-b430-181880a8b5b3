"""
Response handling utilities for consistent API responses.
Implements standardized response structure: {"message": "...", "data": "..."}
"""

from typing import Any, Optional, Dict, List


def create_success_response(
        message: str = "Success",
        data: Any = None
) -> Dict[str, Any]:
    """
    Create standardized success response
    
    Args:
        message: Success message
        data: Response data (can be any type)
        
    Returns:
        Dict with message and data fields
    """
    return {
        "message": message,
        "data": data
    }


def create_error_response(
        message: str,
        error: Optional[str] = None,
        details: Optional[Any] = None
) -> Dict[str, Any]:
    """
    Create standardized error response
    
    Args:
        message: Error message
        error: Error type/code
        details: Additional error details
        
    Returns:
        Dict with message and optional error fields
    """
    response = {"message": message}
    if error:
        response["error"] = error
    if details:
        response["details"] = details
    return response


def create_auth_success_response(
        access_token: str,
        refresh_token: str,
        user_id: str,
        message: str = "Authentication successful"
) -> Dict[str, Any]:
    """
    Create standardized auth success response
    
    Args:
        access_token: JWT access token
        refresh_token: JWT refresh token
        user_id: User identifier
        message: Success message
        
    Returns:
        Standardized response with auth data
    """
    return create_success_response(
        message=message,
        data={
            "access_token": access_token,
            "refresh_token": refresh_token,
            "user_id": user_id,
            "token_type": "bearer",
            "expires_in": 1800  # 30 minutes
        }
    )


def create_otp_success_response(
        email: str,
        message: str = "OTP sent successfully"
) -> Dict[str, Any]:
    """
    Create standardized OTP response
    
    Args:
        email: Email address where OTP was sent
        message: Success message
        
    Returns:
        Standardized response with email data
    """
    return create_success_response(
        message=message,
        data={"email": email}
    )


def create_user_profile_response(
        user_data: Dict[str, Any],
        message: str = "User profile retrieved successfully"
) -> Dict[str, Any]:
    """
    Create standardized user profile response
    
    Args:
        user_data: User profile data
        message: Success message
        
    Returns:
        Standardized response with user data
    """
    return create_success_response(message=message, data=user_data)


def create_social_profiles_response(
        profiles: List[Dict[str, Any]],
        message: str = "Social profiles retrieved successfully"
) -> Dict[str, Any]:
    """
    Create standardized social profiles response
    
    Args:
        profiles: List of social profile data
        message: Success message
        
    Returns:
        Standardized response with profiles data
    """
    return create_success_response(message=message, data=profiles)


def create_organization_response(
        organization_data: Dict[str, Any],
        message: str = "Organization data retrieved successfully"
) -> Dict[str, Any]:
    """
    Create standardized organization response
    
    Args:
        organization_data: Organization data
        message: Success message
        
    Returns:
        Standardized response with organization data
    """
    return create_success_response(message=message, data=organization_data)


def create_oauth_initiate_response(
        auth_url: str,
        state: str,
        message: str = "OAuth initiation successful"
) -> Dict[str, Any]:
    """
    Create standardized OAuth initiation response
    
    Args:
        auth_url: OAuth authorization URL
        state: OAuth state parameter
        message: Success message
        
    Returns:
        Standardized response with OAuth data
    """
    return create_success_response(
        message=message,
        data={
            "auth_url": auth_url,
            "state": state
        }
    )


def create_logout_response(
        message: str = "Successfully logged out"
) -> Dict[str, Any]:
    """
    Create standardized logout response
    
    Args:
        message: Success message
        
    Returns:
        Standardized response
    """
    return create_success_response(message=message)


def create_health_response(
        status: str = "healthy",
        additional_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create standardized health check response
    
    Args:
        status: Health status
        additional_data: Additional health data
        
    Returns:
        Standardized response with health data
    """
    data = {"status": status}
    if additional_data:
        data.update(additional_data)

    return create_success_response(
        message="Service is healthy",
        data=data
    )


def create_master_data_response(
        data: Any,
        message: str = "Master data retrieved successfully"
) -> Dict[str, Any]:
    """
    Create standardized master data response
    
    Args:
        data: Master data (roles, etc.)
        message: Success message
        
    Returns:
        Standardized response with master data
    """
    return create_success_response(message=message, data=data)


def create_profile_completion_response(
        completion_data: Dict[str, Any],
        message: str = "Profile completion data retrieved successfully"
) -> Dict[str, Any]:
    """
    Create standardized profile completion response
    
    Args:
        completion_data: Profile completion data
        message: Success message
        
    Returns:
        Standardized response with completion data
    """
    return create_success_response(message=message, data=completion_data)


def create_domain_organizations_response(
        organizations: List[Dict[str, Any]],
        domain: str,
        domain_verified: bool,
        message: str = "Domain organizations retrieved successfully"
) -> Dict[str, Any]:
    """
    Create standardized domain organizations response
    
    Args:
        organizations: List of organizations
        domain: Domain name
        domain_verified: Whether domain is verified
        message: Success message
        
    Returns:
        Standardized response with organizations data
    """
    return create_success_response(
        message=message,
        data={
            "organizations": organizations,
            "domain": domain,
            "domain_verified": domain_verified
        }
    )


def create_validation_error_response(message: str) -> Dict[str, Any]:
    """Create validation error response with consistent format"""
    return create_error_response(message=message)


def create_email_error_response(email: str, error_type: str = "invalid") -> Dict[str, Any]:
    """Create email-specific error responses"""
    messages = {
        "invalid": "The email address does not exist. Please check for typos and try again.",
        "already_exists": f"An account with email {email} already exists.",
        "not_found": f"No account found with email {email}.",
        "verification_failed": "Email verification failed. Please check the email address and try again."
    }
    message = messages.get(error_type, messages["verification_failed"])
    return create_error_response(message=message)


def create_otp_error_response(error_type: str, **kwargs) -> Dict[str, Any]:
    """Create OTP-specific error responses"""
    messages = {
        "expired": "OTP has expired. Please request a new one.",
        "invalid": "Invalid OTP. Please check and try again.",
        "not_found": "OTP not found or has expired. Please request a new one.",
        "too_many_attempts": f"Too many failed attempts. Please try again after {kwargs.get('lockout_time', '5 minutes')}."
    }
    message = messages.get(error_type, "OTP verification failed. Please try again.")
    return create_error_response(message=message)
