import redis
from redis.cluster import RedisCluster as Redis


def initialize_redis(url: str, environ: str, logger) -> redis.Redis:
    if environ.lower() == "production":
        logger.info(f"Url received for redis prod is {url}")
        split_url, port = url.split(":")
        try:
            port = int(port)
        except ValueError:
            port = 6379
        logger.info(f"After splitting {split_url}")
        redis_client = Redis(host=split_url, port=port)
        logger.info(redis_client.get_nodes())
        logger.info("Connection to redis is successfull")
        return redis_client
    redis_client = redis.Redis.from_url(url=f"redis://{url}", decode_responses=True,   socket_timeout=3.0,           # Socket operations timeout
            socket_connect_timeout=2.0)
    return redis_client
