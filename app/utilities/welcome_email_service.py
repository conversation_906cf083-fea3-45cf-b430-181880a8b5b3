"""
Welcome email service for sending one-time welcome emails to new users.
"""
from datetime import datetime, UTC
from typing import Optional, Dict, Any

from app.core.config import APP_CONFIG
from app.core.redis_keys import RedisKeys, RedisConfig
from app.utilities.email_service import get_email_service


class WelcomeEmailService:
    """Service for managing welcome emails with tracking to prevent duplicates."""
    
    def __init__(self, redis_client):
        """
        Initialize welcome email service.
        
        Args:
            redis_client: Redis client instance
        """
        self.redis_client = redis_client
        self.logger = APP_CONFIG.logger
        self.email_service = get_email_service()
    
    def has_welcome_email_been_sent(self, email: str) -> bool:
        """
        Check if a welcome email has already been sent to this email address.
        
        Args:
            email: Email address to check
            
        Returns:
            bool: True if welcome email was already sent, False otherwise
        """
        try:
            welcome_key = RedisKeys.welcome_email_sent(email.lower())
            sent_timestamp = self.redis_client.get(welcome_key)
            return sent_timestamp is not None
        except Exception as e:
            self.logger.warning(f"Failed to check welcome email status for {email}: {e}")
            # If <PERSON><PERSON> fails, assume email wasn't sent to avoid blocking registration
            return False
    
    def mark_welcome_email_as_sent(self, email: str) -> bool:
        """
        Mark that a welcome email has been sent to this email address.
        
        Args:
            email: Email address that received welcome email
            
        Returns:
            bool: True if marked successfully, False otherwise
        """
        try:
            welcome_key = RedisKeys.welcome_email_sent(email.lower())
            current_time = datetime.now(UTC).isoformat()
            
            # Set the key with TTL (30 days)
            self.redis_client.setex(
                welcome_key, 
                RedisConfig.WELCOME_EMAIL_TTL, 
                current_time
            )
            
            self.logger.info(f"Marked welcome email as sent for {email}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to mark welcome email as sent for {email}: {e}")
            return False
    
    async def send_welcome_email_if_not_sent(
        self, 
        email: str, 
        user_role: str = "influencer"
    ) -> Dict[str, Any]:
        """
        Send welcome email if it hasn't been sent already.
        
        Args:
            email: Email address to send welcome email to
            user_role: User role (influencer/brand) to determine template
            
        Returns:
            dict: Result with success status and message
        """
        email_normalized = email.lower().strip()
        
        try:
            # Check if welcome email was already sent
            if self.has_welcome_email_been_sent(email_normalized):
                self.logger.info(f"Welcome email already sent to {email_normalized}, skipping")
                return {
                    "success": True,
                    "message": "Welcome email already sent",
                    "skipped": True
                }
            
            # Send welcome email
            email_sent = await self.email_service.send_welcome_email(
                email=email_normalized,
                user_role=user_role
            )
            
            if email_sent:
                # Mark as sent to prevent future duplicates
                self.mark_welcome_email_as_sent(email_normalized)
                
                self.logger.info(
                    f"Welcome email sent successfully to {email_normalized} "
                    f"with role {user_role}"
                )
                
                return {
                    "success": True,
                    "message": f"Welcome email sent successfully for {user_role}",
                    "skipped": False
                }
            else:
                self.logger.error(f"Failed to send welcome email to {email_normalized}")
                return {
                    "success": False,
                    "message": "Failed to send welcome email",
                    "skipped": False
                }
                
        except Exception as e:
            self.logger.error(
                f"Error sending welcome email to {email_normalized}: {str(e)}"
            )
            return {
                "success": False,
                "message": f"Error sending welcome email: {str(e)}",
                "skipped": False
            }
    
    def get_welcome_email_timestamp(self, email: str) -> Optional[str]:
        """
        Get the timestamp when welcome email was sent to this email address.
        
        Args:
            email: Email address to check
            
        Returns:
            Optional[str]: ISO timestamp when email was sent, None if not sent
        """
        try:
            welcome_key = RedisKeys.welcome_email_sent(email.lower())
            return self.redis_client.get(welcome_key)
        except Exception as e:
            self.logger.warning(f"Failed to get welcome email timestamp for {email}: {e}")
            return None


def get_welcome_email_service(redis_client) -> WelcomeEmailService:
    """
    Factory function to create WelcomeEmailService instance.
    
    Args:
        redis_client: Redis client instance
        
    Returns:
        WelcomeEmailService: Configured welcome email service
    """
    return WelcomeEmailService(redis_client)
