from datetime import UTC
from typing import Any

from database_helper.database.models import (
    User,
    UserAuth<PERSON>ethod,
    UserSession,
    UserRole,
)
from database_helper.database.sync_db2 import SyncDatabaseDB
from sqlalchemy import delete, select

from app.core.config import APP_CONFIG, get_database, get_locobuzz_redis
from app.core.redis_keys import RedisKeys


class UserDataCleanup:
    """Service for cleaning up user data from database and Redis cache."""

    def __init__(self, redis_client, db_conn: SyncDatabaseDB):
        self.redis_client = redis_client
        self.db_conn = db_conn
        self.logger = APP_CONFIG.logger

    def cleanup_user_data(self, user_email: str, tenant_id: str) -> dict[str, Any]:
        """
        Clean up all user data from database and Redis cache.

        Args:
            user_email: Email of user to cleanup
            tenant_id: Tenant ID for the user

        Returns:
            Dict with cleanup results
        """
        cleanup_results = {
            "database_records_deleted": 0,
            "redis_keys_deleted": 0,
            "errors": [],
        }

        try:
            with self.db_conn.transaction() as session:
                # Find user by email and tenant_id
                user_query = select(User).where(
                    User.email == user_email, User.tenant_id == tenant_id
                )
                user = session.execute(user_query).scalar_one_or_none()

                if not user:
                    cleanup_results["errors"].append(
                        f"User with email {user_email} not found"
                    )
                    return cleanup_results

                user_id = user.id

                # Delete user refresh tokens
                refresh_tokens_query = select(UserSession.refresh_token).where(
                    UserSession.user_id == user_id
                )
                refresh_tokens = session.execute(refresh_tokens_query).scalars().all()

                # Delete refresh tokens from database
                delete_refresh_tokens = delete(UserSession).where(
                    UserSession.user_id == user_id
                )
                result = session.execute(delete_refresh_tokens)
                cleanup_results["database_records_deleted"] += result.rowcount

                # Delete user roles
                delete_user_roles = delete(UserRole).where(UserRole.user_id == user_id)
                result = session.execute(delete_user_roles)
                cleanup_results["database_records_deleted"] += result.rowcount

                # Delete user auth methods
                delete_auth_methods = delete(UserAuthMethod).where(
                    UserAuthMethod.user_id == user_id
                )
                result = session.execute(delete_auth_methods)
                cleanup_results["database_records_deleted"] += result.rowcount

                # Delete user
                delete_user = delete(User).where(User.id == user_id)
                result = session.execute(delete_user)
                cleanup_results["database_records_deleted"] += result.rowcount

                # Clean up Redis keys
                redis_deleted = self._cleanup_redis_data(
                    user_id, user_email, tenant_id, refresh_tokens
                )
                cleanup_results["redis_keys_deleted"] = redis_deleted

                self.logger.info(f"Cleaned up user data for {user_email}")

        except Exception as e:
            cleanup_results["errors"].append(str(e))
            self.logger.error(f"Failed to cleanup user data: {e}")

        return cleanup_results

    def _cleanup_redis_data(
            self, user_id: str, user_email: str, tenant_id: str, refresh_tokens: list[str]
    ) -> int:
        """Clean up Redis keys related to user."""
        deleted_count = 0

        try:
            # OTP key
            otp_key = f"CreatorVerse:otp:{tenant_id}:{user_email}"
            if self.redis_client.delete(otp_key):
                deleted_count += 1

            # User roles key
            if self.redis_client.delete(RedisKeys.rbac_user_roles(user_id)):
                deleted_count += 1

            # Refresh token keys
            for token in refresh_tokens:
                refresh_key = f"CreatorVerse:refresh_token:{token}"
                if self.redis_client.delete(refresh_key):
                    deleted_count += 1

            # Check for any other CreatorVerse keys with user_id pattern
            pattern = f"CreatorVerse:*:{user_id}*"
            keys = self.redis_client.keys(pattern)
            if keys:
                deleted_count += self.redis_client.delete(*keys)

        except Exception as e:
            self.logger.error(f"Failed to cleanup Redis data: {e}")

        return deleted_count

    def cleanup_all_test_users(self, tenant_id: str) -> dict[str, Any]:
        """
        Clean up all test users (emails containing 'test' or 'demo').

        Args:
            tenant_id: Tenant ID to cleanup

        Returns:
            Dict with cleanup results
        """
        cleanup_results = {
            "total_users_deleted": 0,
            "database_records_deleted": 0,
            "redis_keys_deleted": 0,
            "errors": [],
        }

        try:
            with self.db_conn.transaction() as session:
                # Find test users
                test_users_query = select(User).where(
                    User.tenant_id == tenant_id,
                    User.email.ilike("%test%") | User.email.ilike("%demo%"),
                )
                test_users = session.execute(test_users_query).scalars().all()

                for user in test_users:
                    result = self.cleanup_user_data(user.email, tenant_id)
                    cleanup_results["total_users_deleted"] += 1
                    cleanup_results["database_records_deleted"] += result[
                        "database_records_deleted"
                    ]
                    cleanup_results["redis_keys_deleted"] += result[
                        "redis_keys_deleted"
                    ]
                    cleanup_results["errors"].extend(result["errors"])

        except Exception as e:
            cleanup_results["errors"].append(str(e))
            self.logger.error(f"Failed to cleanup test users: {e}")

        return cleanup_results

    def cleanup_expired_refresh_tokens(self) -> dict[str, Any]:
        """Clean up expired refresh tokens from database and Redis."""
        from datetime import datetime

        cleanup_results = {
            "database_records_deleted": 0,
            "redis_keys_deleted": 0,
            "errors": [],
        }

        try:
            with self.db_conn.transaction() as session:
                # Get expired tokens
                expired_tokens_query = select(UserSession.refresh_token).where(
                    UserSession.expires_at <= datetime.now(UTC)
                )
                expired_tokens = session.execute(expired_tokens_query).scalars().all()

                # Delete from database
                delete_expired = delete(UserSession).where(
                    UserSession.expires_at <= datetime.now(UTC)
                )
                result = session.execute(delete_expired)
                cleanup_results["database_records_deleted"] = result.rowcount

                # Clean up Redis
                for token in expired_tokens:
                    refresh_key = f"CreatorVerse:refresh_token:{token}"
                    if self.redis_client.delete(refresh_key):
                        cleanup_results["redis_keys_deleted"] += 1

        except Exception as e:
            cleanup_results["errors"].append(str(e))
            self.logger.error(f"Failed to cleanup expired tokens: {e}")

        return cleanup_results


def cleanup_user_by_email(user_email: str, tenant_id: str) -> None:
    """
    Convenience function to cleanup a single user.

    Args:
        user_email: Email of user to cleanup
        tenant_id: Tenant ID for the user
    """
    redis_client = get_locobuzz_redis()
    db_conn = get_database()

    cleanup_service = UserDataCleanup(redis_client, db_conn)
    result = cleanup_service.cleanup_user_data(user_email, tenant_id)

    print(f"Cleanup results for {user_email}:")
    print(f"Database records deleted: {result['database_records_deleted']}")
    print(f"Redis keys deleted: {result['redis_keys_deleted']}")

    if result["errors"]:
        print(f"Errors: {result['errors']}")


def cleanup_all_test_data(tenant_id: str) -> None:
    """
    Convenience function to cleanup all test data.

    Args:
        tenant_id: Tenant ID to cleanup
    """
    redis_client = get_locobuzz_redis()
    db_conn = get_database()

    cleanup_service = UserDataCleanup(redis_client, db_conn)
    result = cleanup_service.cleanup_all_test_users(tenant_id)

    print(f"Cleanup results for tenant {tenant_id}:")
    print(f"Total users deleted: {result['total_users_deleted']}")
    print(f"Database records deleted: {result['database_records_deleted']}")
    print(f"Redis keys deleted: {result['redis_keys_deleted']}")

    if result["errors"]:
        print(f"Errors: {result['errors']}")


if __name__ == "__main__":
    # Example usage
    # cleanup_user_by_email("<EMAIL>", "your-tenant-id")
    # cleanup_all_test_data("your-tenant-id")
    pass
