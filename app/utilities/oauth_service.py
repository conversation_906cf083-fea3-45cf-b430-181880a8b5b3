import asyncio
import time
from datetime import UTC, datetime, timedelta
from typing import Any
from urllib.parse import urlencode

import httpx
import redis
from database_helper.database.models import User, OAuthAccount, SocialProfile
from database_helper.database.models import User<PERSON>uthMethod
from database_helper.database.sync_db2 import SyncDatabaseDB
from jose import jwt

from app.core.config import get_locobuzz_redis, APP_CONFIG
from app.core.database_helper.users_helper import create_user_only, create_user_oauth, create_user_role
from app.core.enums_data import UserType
from app.core.redis_keys import RedisKeys, RedisConfig
from app.utilities.jwt_oauth_utilities import issue_tokens_for_user_with_uuid_role
from app.utilities.oauth_brand_service import get_oauth_brand_service
from app.utilities.oauth_role_manager import get_oauth_role_manager
from app.utilities.oauth_handlers import get_oauth_handler

# YouTube API imports
try:
    from google.oauth2.credentials import Credentials
    from googleapiclient.discovery import build

    YOUTUBE_AVAILABLE = True
except ImportError:
    YOUTUBE_AVAILABLE = False
    APP_CONFIG.logger.warning(
        "YouTube API client not available. Install google-api-python-client for YouTube integration.")

from app.core.enums_data import SourceRegister
from app.core.security import generate_csrf_state
from app.schemas.auth import (
    GoogleTokenResponse,
    GoogleUserInfo,
    InstagramTokenResponse,
    InstagramUserInfo,
    OAuthTokenResponse,
)
from app.utilities.bloom_filter import get_bloom_filter_manager, CreatorBloomFilterManager

# OAuth Configuration
GOOGLE_SCOPE_YT = "https://www.googleapis.com/auth/youtube.readonly"


class OAuthError(Exception):
    """Custom OAuth exception for better error handling"""

    def __init__(self, message: str, error_code: str = "OAUTH_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class NetworkError(OAuthError):
    """Network-related OAuth errors"""

    def __init__(self, message: str):
        super().__init__(message, "NETWORK_ERROR")


class RedisError(OAuthError):
    """Redis-related OAuth errors"""

    def __init__(self, message: str):
        super().__init__(message, "REDIS_ERROR")


class DatabaseError(OAuthError):
    """Database-related OAuth errors"""

    def __init__(self, message: str):
        super().__init__(message, "DATABASE_ERROR")


class InMemoryStateStore:
    """Fallback in-memory state store when Redis is unavailable"""

    def __init__(self) -> None:
        self._store: dict[str, tuple[str, float]] = {}
        self._cleanup_interval = 60  # seconds
        self._last_cleanup = time.time()

    def set_state(self, state: str, provider: str, expiry_seconds: int = 300) -> None:
        """
        Store OAuth state with expiry in an in-memory dict.
        state → (provider_string, expiry_time)
        """
        expiry_time = time.time() + expiry_seconds
        self._store[state] = (provider, expiry_time)
        self._cleanup_expired()

    def get_and_delete_state(self, state: str) -> str | None:
        """
        Return the provider if state is valid (not expired), and delete it from store.
        """
        self._cleanup_expired()
        if state in self._store:
            provider, expiry_time = self._store.pop(state)
            if time.time() < expiry_time:
                return provider
        return None

    def _cleanup_expired(self) -> None:
        """Remove any expired states from the in-memory dict."""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            expired = [
                s for s, (_, exp) in self._store.items() if current_time > exp
            ]
            for s in expired:
                self._store.pop(s, None)
            self._last_cleanup = current_time


class OAuthService:
    """OAuth service for handling Google and Instagram authentication"""

    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.fallback_store = InMemoryStateStore()
        # Get credentials from AppConfig
        self.google_client_id = APP_CONFIG.oauth_google_client_id
        self.google_client_secret = APP_CONFIG.oauth_google_client_secret
        self.google_redirect_uri = APP_CONFIG.oauth_google_redirect_uri
        self.instagram_client_id = APP_CONFIG.oauth_instagram_client_id
        self.instagram_client_secret = APP_CONFIG.oauth_instagram_client_secret
        self.instagram_redirect_uri = APP_CONFIG.oauth_instagram_redirect_uri

    def initiate_google_oauth(
            self, redirect_uri: str, user_type: str, role_uuid: str | None = None) -> tuple[str, str]:
        """
        Generate a CSRF state, store OAuth state data with provider, user_type and role_uuid,
        then return the Google OAuth URL and the state.

        Args:
            redirect_uri: OAuth callback URL
            user_type: User type (influencer, brand)
            role_uuid: Optional role UUID for explicit role assignment

        Returns:
            Tuple of (auth_url, state)
        """
        # 1) Generate a random CSRF state token
        state = generate_csrf_state()

        # 2) Store OAuth state data with role_uuid support
        from app.utilities.oauth_state_manager import get_oauth_state_manager

        try:
            # Use enhanced state manager with role_uuid support
            state_manager = get_oauth_state_manager(self.redis_client)
            extra_data = {"redirect_uri": redirect_uri}
            state_manager.store_state(
                state=state,
                provider="google",
                user_type=user_type,
                role_uuid=role_uuid,
                extra_data=extra_data
            )
        except Exception as e:
            # If state manager fails, use legacy approach
            APP_CONFIG.logger.warning(f"State manager failed, using legacy approach: {str(e)}")
            state_key = f"CreatorVerse:oauth:state:{state}"
            state_data = f"google:{user_type}"
            if role_uuid:
                state_data = f"{state_data}:{role_uuid}"

            try:
                self.redis_client.set(state_key, state_data, ex=300)
            except Exception:
                APP_CONFIG.logger.warning(
                    f"Redis is unavailable, falling back to in-memory state store for {state_key}")
                self.fallback_store.set_state(state, state_data, 300)

        # 3) Build the Google OAuth URL with YouTube scope
        params = {
            "client_id": self.google_client_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": f"openid email profile {GOOGLE_SCOPE_YT}",  # Added YouTube scope
            "state": state,
            "access_type": "offline",
            "prompt": "consent",
        }
        auth_url = f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"
        return auth_url, state

    def initiate_instagram_oauth(
            self, redirect_uri: str, user_type: str, role_uuid: str | None = None
    ) -> tuple[str, str]:
        """
        Generate a CSRF state, store OAuth state data with provider, user_type and role_uuid,
        then return the Instagram OAuth URL and the state.

        Args:
            redirect_uri: OAuth callback URL
            user_type: User type (influencer, brand)
            role_uuid: Optional role UUID for explicit role assignment

        Returns:
            Tuple of (auth_url, state)
        """
        # 1) Generate a random CSRF state token
        state = generate_csrf_state()

        # 2) Store OAuth state data with role_uuid support
        from app.utilities.oauth_state_manager import get_oauth_state_manager

        try:
            # Use enhanced state manager with role_uuid support
            state_manager = get_oauth_state_manager(self.redis_client)
            extra_data = {"redirect_uri": redirect_uri}
            state_manager.store_state(
                state=state,
                provider="instagram",
                user_type=user_type,
                role_uuid=role_uuid,
                extra_data=extra_data
            )
        except Exception as e:
            # If state manager fails, use legacy approach
            APP_CONFIG.logger.warning(f"State manager failed, using legacy approach: {str(e)}")
            state_key = f"CreatorVerse:oauth:state:{state}"
            state_data = f"instagram:{user_type}"
            if role_uuid:
                state_data = f"{state_data}:{role_uuid}"

            try:
                self.redis_client.set(state_key, state_data, ex=300)
            except Exception:
                APP_CONFIG.logger.warning("Redis is unavailable, falling back to in-memory state store")
                self.fallback_store.set_state(state, state_data, 300)

        # 3) Build the Instagram OAuth URL
        params = {
            "client_id": self.instagram_client_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": "user_profile,user_media",
            "state": state,
        }
        auth_url = f"https://api.instagram.com/oauth/authorize?{urlencode(params)}"
        return auth_url, state

    async def pop_oauth_state(self, state: str) -> str | None:
        """
        Fetch and delete the stored state data from Redis/in-memory.
        Returns provider:user_type[:role_uuid] string format for backwards compatibility.
        """
        try:
            # Use enhanced state manager
            from app.utilities.oauth_state_manager import get_oauth_state_manager
            state_manager = get_oauth_state_manager(self.redis_client)
            return await state_manager.pop_oauth_state(state)
        except Exception as e:
            # Fallback to legacy approach
            APP_CONFIG.logger.warning(f"State manager failed, using legacy approach: {str(e)}")
            state_key = f"CreatorVerse:oauth:state:{state}"
            try:
                stored = self.redis_client.get(state_key)
                if stored:
                    stored_str = stored.decode() if isinstance(stored, bytes) else stored
                    self.redis_client.delete(state_key)
                    return stored_str
            except Exception:
                # Fallback to in-memory
                stored_str = self.fallback_store.get_and_delete_state(state)
                return stored_str

        return None

    async def exchange_google_code_for_tokens(
            self, code: str, redirect_uri: str
    ) -> GoogleTokenResponse:
        """
        Exchange Google authorization code for tokens
        """
        token_data = {
            "client_id": self.google_client_id,
            "client_secret": self.google_client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://oauth2.googleapis.com/token",
                data=token_data,
                headers={"Accept": "application/json"},
            )
            if response.status_code != 200:
                raise OAuthError(f"Failed to exchange code for tokens: {response.text}")
            token_response = response.json()
            return GoogleTokenResponse(**token_response)

    async def get_google_user_info(self, id_token: str) -> GoogleUserInfo:
        """
        Decode Google ID token (without full verification) and return user info.
        (In production, you'd verify against Google's public keys.)
        """
        try:
            decoded_token = jwt.get_unverified_claims(id_token)
            # Verify issuer
            if decoded_token.get("iss") not in [
                "accounts.google.com", "https://accounts.google.com"
            ]:
                raise OAuthError("Invalid issuer in ID token")
            # Verify audience
            if decoded_token.get("aud") != self.google_client_id:
                raise OAuthError("Invalid audience in ID token")

            return GoogleUserInfo(
                sub=decoded_token["sub"],
                email=decoded_token["email"],
                name=decoded_token.get("name", ""),
                picture=decoded_token.get("picture"),
                email_verified=decoded_token.get("email_verified", False),
            )
        except Exception as e:
            raise OAuthError(f"Failed to decode ID token: {str(e)}")

    async def create_oauth_account_entry(
            self,
            session,
            user_id: str,
            provider: str,
            provider_user_id: str,
            access_token: str,
            refresh_token: str | None = None,
            expires_at: datetime | None = None,
            scope: str | None = None,
    ) -> OAuthAccount:
        """
        Create or update OAuth account entry for the user.
        """
        # Check if OAuth account already exists
        existing_account = session.query(OAuthAccount).filter_by(
            user_id=user_id,
            provider=provider,
            provider_user_id=provider_user_id
        ).first()

        if existing_account:
            # Update existing account
            existing_account.access_token = access_token
            existing_account.refresh_token = refresh_token
            existing_account.expires_at = expires_at
            existing_account.scope = scope
            existing_account.updated_at = datetime.now(UTC)
            session.add(existing_account)
            return existing_account
        else:
            # Create new OAuth account
            oauth_account = OAuthAccount(
                user_id=user_id,
                provider=provider,
                provider_user_id=provider_user_id,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=expires_at,
                scope=scope,
                created_at=datetime.now(UTC),
                updated_at=datetime.now(UTC),
            )
            session.add(oauth_account)
            return oauth_account

    def _build_youtube_client(self, access_token: str, refresh_token: str, expires_at: datetime):
        """Build authenticated YouTube API client (blocking operation)"""
        if not YOUTUBE_AVAILABLE:
            raise OAuthError("YouTube API client not available")
        
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=UTC)

        creds = Credentials(
            token=access_token,
            refresh_token=refresh_token,
            token_uri="https://oauth2.googleapis.com/token",
            client_id=self.google_client_id,
            client_secret=self.google_client_secret,
            scopes=[GOOGLE_SCOPE_YT],
            expiry=expires_at,
        )
        return build("youtube", "v3", credentials=creds, cache_discovery=False)

    async def fetch_youtube_profile(self, access_token: str, refresh_token: str, expires_at: datetime) -> dict:
        """Fetch YouTube channel profile data asynchronously"""
        loop = asyncio.get_running_loop()

        def _worker():
            try:
                yt = self._build_youtube_client(access_token, refresh_token, expires_at)
                response = yt.channels().list(part="snippet,statistics", mine=True).execute()
                if not response.get("items"):
                    raise OAuthError("No YouTube channel found for this account")
                return response["items"][0]
            except Exception as e:
                raise OAuthError(f"YouTube API error: {str(e)}")

        return await loop.run_in_executor(None, _worker)

    def upsert_social_profile(
            self,
            *,
            session,
            oauth_account_id: str,
            service: str,
            external_id: str,
            username: str | None = None,
            display_name: str | None = None,
            avatar_url: str | None = None,
            follower_count: int | None = None,
            post_count: int | None = None,
            raw_json: dict | None = None
    ):
        """Create or update social profile entry"""

        profile = session.query(SocialProfile).filter_by(
            oauth_account_id=oauth_account_id,
            service=service
        ).first()

        if profile is None:
            profile = SocialProfile(
                oauth_account_id=oauth_account_id,
                service=service,
                external_id=external_id
            )
            session.add(profile)

        # Update profile data
        profile.username = username
        profile.display_name = display_name
        profile.avatar_url = avatar_url
        profile.follower_count = follower_count
        profile.post_count = post_count
        profile.raw_json = raw_json or {}
        profile.fetched_at = datetime.now(UTC)

        return profile

    async def create_or_update_user_from_oauth(
            self,
            user_info: GoogleUserInfo | Any,
            provider: int,
            access_token: str,
            refresh_token: str | None = None,
            db_conn: SyncDatabaseDB | None = None,
            provider_user_id: str | None = None,
            scope: str | None = None,
            expires_at: datetime | None = None,
            role_uuid: str | None = None  # Added explicit role_uuid parameter
    ) -> dict[str, Any]:
        """
        Given a GoogleUserInfo (or similar), upsert the User record and issue app JWTs.
        Now supports UUID roles for brand users with proper domain handling.
        """
        if db_conn is None:
            raise ValueError("Database connection is required")

        user_email = user_info.email.lower().strip()

        # We will get the role type from the role manager for type safety checking
        role_manager = get_oauth_role_manager(self.redis_client)
        role_type_enum: UserType = await role_manager.get_user_type_for_role_uuid(role_uuid,
                                                                                  db_conn) if role_uuid else None

        # For brand users, validate domain first using brand service
        if role_type_enum == UserType.BRAND:
            brand_service = get_oauth_brand_service(self.redis_client)
            domain_check = await brand_service.check_brand_domain_eligibility(user_email)

            if not domain_check["eligible"]:
                raise OAuthError(domain_check["message"])

        # Get roles cache for additional operations
        roles_cache = self.redis_client.hgetall(RedisKeys.rbac_roles())

        # Determine OAuth provider string and auth method
        if provider == SourceRegister.GOOGLE_OAUTH.value:
            provider_str = "google"
            auth_method_key = "oauth_google"
        elif provider == SourceRegister.INSTAGRAM_OAUTH.value:
            provider_str = "instagram"
            auth_method_key = "oauth_instagram"
        else:
            raise OAuthError(f"Unsupported OAuth provider: {provider}")

        try:
            # Get auth method ID using cache-aside pattern
            auth_method_id = await self.get_auth_method_id_cache_aside(auth_method_key, db_conn)

            if not auth_method_id:
                raise OAuthError(f"Auth method not found: {auth_method_key}")

            with db_conn.transaction() as session:
                # Disable autoflush to prevent premature flushes
                with session.no_autoflush:
                    # Check if user exists using cache-aside pattern
                    redis_key = RedisKeys.user_by_email(user_email)
                    cached_user = self.redis_client.hgetall(redis_key)

                    user = None
                    is_new_user = False
                    user_id_str = None

                    user = cached_user if cached_user else session.query(User).filter_by(email=user_email).first()

                    if not user:
                        # User doesn't exist, create new user
                        is_new_user = True
                        APP_CONFIG.logger.info(f"Creating new user via OAuth: {user_email}")

                        user_data = {
                            "name": user_info.name or user_email.split('@')[0],
                            "profile_image": user_info.picture or "",
                            "is_email_verified": True,
                            "is_active": True,
                            "status": "active",
                            "register_source": provider,
                        }

                        user = await create_user_only(
                            session,
                            user_email,
                            user_data,
                            self.redis_client
                        )

                        user_id_str = str(user.id)

                        # Create OAuth auth method entry
                        await create_user_oauth(
                            session,
                            user_id_str,
                            self.redis_client,
                            register_channel=auth_method_key,
                            auth_method_id=auth_method_id
                        )

                        # Create user role entry (main role only, no duplicates)
                        await create_user_role(session, user_id_str, role_uuid)

                        APP_CONFIG.logger.info(f"New user created via OAuth: {user_email}")
                    else:
                        APP_CONFIG.logger.info(f"Updating existing user via OAuth: {user_email}")
                        detached = User(**{**cached_user, **{
                            "name": user_info.name or cached_user["name"],
                            "profile_image": user_info.picture or cached_user["profile_image"],
                            "is_email_verified": True,
                            "is_active": True,
                            "status": "active",
                        }})
                        user = session.merge(detached)
                        user_id_str = str(user.id)
                        # Check if OAuth auth method exists for existing user
                        existing_auth = session.query(UserAuthMethod).filter_by(
                            user_id=user.id,
                            auth_method_id=auth_method_id
                        ).first()

                        if not existing_auth:
                            await create_user_oauth(
                                session,
                                user_id_str,
                                self.redis_client,
                                register_channel=auth_method_key,
                                auth_method_id=auth_method_id
                            )

                        APP_CONFIG.logger.info(f"Existing user updated via OAuth: {user_email}")

                # Force flush to persist user and auth method records
                session.flush()
                session.refresh(user)

                # Handle brand-specific logic AFTER user creation (separate transaction scope)
                if role_type_enum == UserType.BRAND:
                    brand_service = get_oauth_brand_service(self.redis_client)
                    await brand_service.handle_brand_user_creation(
                        session, user, user_email, role_type_enum, roles_cache
                    )

                # Create or update OAuth account entry
                oauth_account = await self.create_oauth_account_entry(
                    session=session,
                    user_id=user_id_str,
                    provider=provider_str,
                    provider_user_id=provider_user_id or getattr(user_info, 'sub', str(user_info.id)),
                    access_token=access_token,
                    refresh_token=refresh_token,
                    expires_at=expires_at,
                    scope=scope,
                )

                # Create social profiles only for influencers (Google YouTube for now)
                # AND only for new users (not existing ones being linked)
                if role_type_enum == UserType.INFLUENCER and provider_str == "google" and is_new_user:
                    try:
                        yt_json = await self.fetch_youtube_profile(
                            access_token=access_token,
                            refresh_token=refresh_token,
                            expires_at=expires_at,
                        )
                        
                        # Check if the user needs to select a channel
                        needs_channel_selection = yt_json.get("_needs_channel_selection", False)
                        channel_count = yt_json.get("_channel_count", 0)
                        
                        # If the user has multiple channels, store the OAuth account without creating
                        # the social profile yet - they'll need to select a channel first
                        if needs_channel_selection and channel_count > 1:
                            APP_CONFIG.logger.info(
                                f"User {user_email} has {channel_count} YouTube channels. "
                                "Redirecting to channel selection."
                            )
                            
                            # Store a flag in the user's metadata for the frontend
                            user.metadata_json = {
                                **(user.metadata_json or {}),
                                "needs_youtube_channel_selection": True,
                                "youtube_channel_count": channel_count
                            }
                            session.flush()
                            session.refresh(user)
                        else:
                            # Single channel or automatic selection flow (backward compatibility)
                            snippet = yt_json.get("snippet", {})
                            stats = yt_json.get("statistics", {})
                            thumbnails = snippet.get("thumbnails", {})
                            avatar_url = thumbnails.get("default", {}).get("url") if thumbnails else None

                            self.upsert_social_profile(
                                session=session,
                                oauth_account_id=str(oauth_account.id),
                                service="youtube",
                                external_id=str(yt_json["id"]),
                                username=None,
                                display_name=snippet.get("title"),
                                avatar_url=avatar_url,
                                follower_count=int(stats.get("subscriberCount", 0)),
                                post_count=int(stats.get("videoCount", 0)),
                                raw_json=yt_json,
                            )
                        APP_CONFIG.logger.info(f"YouTube profile created for new influencer: {user_email}")
                    except Exception as e:
                        APP_CONFIG.logger.warning(f"YouTube profile fetch failed for {user_email}: {e}")
                elif role_type_enum == UserType.INFLUENCER and provider_str == "google" and not is_new_user:
                    APP_CONFIG.logger.info(f"Skipping YouTube profile enrichment for existing user: {user_email}")

                elif role_type_enum == UserType.INFLUENCER and provider_str == "instagram" and is_new_user:
                    try:
                        # For Facebook OAuth with Instagram access, fetch Instagram pages
                        if provider == SourceRegister.INSTAGRAM_OAUTH.value:
                            # This is the legacy Instagram OAuth flow
                            self.upsert_social_profile(
                                session=session,
                                oauth_account_id=str(oauth_account.id),
                                service="instagram",
                                external_id=str(provider_user_id),
                                username=getattr(user_info, 'username', None),
                                display_name=getattr(user_info, 'username', None),
                                avatar_url=None,
                                follower_count=None,
                                post_count=None,
                                raw_json=getattr(user_info, 'model_dump', lambda: {})(),
                            )
                            APP_CONFIG.logger.info(f"Instagram profile created for new influencer: {user_email}")
                        else:
                            # This could be Facebook OAuth with Instagram pages
                            try:
                                # Try to fetch Instagram pages connected to Facebook
                                handler = get_oauth_handler("facebook")
                                ig_pages_data = await handler.fetch_instagram_pages(
                                    access_token=access_token,
                                    user_id=provider_user_id or str(user_info.id)
                                )
                                
                                # Check if the user needs to select an Instagram page
                                needs_page_selection = ig_pages_data.get("_needs_page_selection", False)
                                page_count = ig_pages_data.get("_page_count", 0)
                                
                                # If the user has multiple Instagram pages, store the flag in metadata
                                if needs_page_selection and page_count > 1:
                                    APP_CONFIG.logger.info(
                                        f"User {user_email} has {page_count} Instagram pages. "
                                        "Redirecting to page selection."
                                    )
                                    
                                    # Store a flag in the user's metadata for the frontend
                                    user.metadata_json = {
                                        **(user.metadata_json or {}),
                                        "needs_instagram_page_selection": True,
                                        "instagram_page_count": page_count
                                    }
                                    session.flush()
                                    session.refresh(user)
                                else:
                                    # Single Instagram page or automatic selection
                                    if page_count > 0:
                                        # Create social profile with the first (or only) Instagram page
                                        # Note: We'll store minimal data here, full data gets stored when user selects
                                        self.upsert_social_profile(
                                            session=session,
                                            oauth_account_id=str(oauth_account.id),
                                            service="instagram",
                                            external_id=ig_pages_data.get("instagram_business_account", {}).get("id", ""),
                                            username=ig_pages_data.get("instagram_business_account", {}).get("username", ""),
                                            display_name=ig_pages_data.get("name", ""),
                                            avatar_url=None,
                                            follower_count=None,
                                            post_count=None,
                                            raw_json=ig_pages_data,
                                        )
                                        APP_CONFIG.logger.info(f"Instagram page profile created for new influencer: {user_email}")
                                    
                            except Exception as ig_e:
                                APP_CONFIG.logger.warning(f"Instagram pages fetch failed for {user_email}: {ig_e}")
                                # Fallback to basic Instagram profile if pages fetch fails
                                self.upsert_social_profile(
                                    session=session,
                                    oauth_account_id=str(oauth_account.id),
                                    service="instagram",
                                    external_id=str(provider_user_id),
                                    username=getattr(user_info, 'username', None),
                                    display_name=getattr(user_info, 'username', None),
                                    avatar_url=None,
                                    follower_count=None,
                                    post_count=None,
                                    raw_json=getattr(user_info, 'model_dump', lambda: {})(),
                                )
                                APP_CONFIG.logger.info(f"Basic Instagram profile created for new influencer: {user_email}")
                    except Exception as e:
                        APP_CONFIG.logger.warning(f"Instagram profile creation failed for {user_email}: {e}")
                elif role_type_enum == UserType.INFLUENCER and provider_str == "instagram" and not is_new_user:
                    APP_CONFIG.logger.info(f"Skipping Instagram profile enrichment for existing user: {user_email}")

                # Update user cache
                user_dict = {
                    "id": user_id_str,
                    "email": user.email,
                    "name": user.name or "",
                    "phone_number": user.phone_number or "",
                    "status": user.status or "",
                    "is_active": str(user.is_active),
                    "is_email_verified": str(user.is_email_verified),
                }

                self.redis_client.hset(redis_key, mapping=user_dict)
                self.redis_client.expire(redis_key, RedisConfig.USER_TTL)

                # Issue tokens for the user with UUID role
                token_response, _ = issue_tokens_for_user_with_uuid_role(
                    user_id_str,
                    role_uuid,
                    redis_client=self.redis_client,
                    session=session,
                    ip_address=None,
                    user_agent=None
                )

                return {
                    "access_token": token_response.access_token,
                    "refresh_token": token_response.refresh_token,
                    "token_type": token_response.token_type,
                    "expires_in": token_response.expires_in,
                    "user_id": user_id_str,
                    "is_new_user": is_new_user,
                    "metadata_json": user.metadata_json
                }

        except Exception as e:
            APP_CONFIG.logger.error(f"Error creating/updating user from OAuth: {str(e)}")
            raise OAuthError(f"Failed to create or update user: {str(e)}")

    async def handle_google_oauth_callback(
            self, code: str, state: str, redirect_uri: str, db_conn: SyncDatabaseDB, role_uuid: str
    ) -> OAuthTokenResponse:
        """
        Validate CSRF state, exchange 'code' for Google tokens,
        decode user info, upsert user in DB, and return our own app JWTs.
        
        The state string may contain role_uuid in the format "google:user_type:role_uuid"
        """

        google_tokens = await self.exchange_google_code_for_tokens(code, redirect_uri)
        user_info = await self.get_google_user_info(google_tokens.id_token)

        # Calculate token expiry (Google typically provides 1 hour expiry)
        expires_at = datetime.now(UTC).replace(microsecond=0)
        if google_tokens.expires_in:
            expires_at += timedelta(seconds=google_tokens.expires_in)

        user_data = await self.create_or_update_user_from_oauth(
            user_info=user_info,
            provider=SourceRegister.GOOGLE_OAUTH.value,
            access_token=google_tokens.access_token,
            refresh_token=google_tokens.refresh_token,
            db_conn=db_conn,
            provider_user_id=user_info.sub,
            scope="openid email profile",
            expires_at=expires_at,
            role_uuid=role_uuid
        )

        # Add message indicating if this was a new registration or login
        message = "Registration successful!" if user_data.get("is_new_user", False) else "Login successful!"
        
        # Check if the user needs YouTube channel selection
        needs_channel_selection = False
        if "metadata_json" in user_data and isinstance(user_data["metadata_json"], dict):
            needs_channel_selection = user_data["metadata_json"].get("needs_youtube_channel_selection", False)
        
        response = OAuthTokenResponse(
            access_token=user_data["access_token"],
            refresh_token=user_data["refresh_token"],
            token_type=user_data["token_type"],
            expires_in=user_data["expires_in"],
            message=message
        )
        
        # Add channel selection flag to the response extra_data
        if needs_channel_selection:
            response.extra_data = {
                "needs_youtube_channel_selection": True,
                "user_id": user_data.get("user_id")
            }
            
        return response

    async def exchange_instagram_code_for_tokens(
            self, code: str, redirect_uri: str
    ) -> InstagramTokenResponse:
        """
        Exchange Instagram authorization code for a short-lived access_token.
        """
        token_url = "https://graph.facebook.com/v23.0/oauth/access_token"
        data = {
            "client_id": self.instagram_client_id,
            "client_secret": self.instagram_client_secret,
            "grant_type": "authorization_code",  # Authorization Code Grant
            "redirect_uri": redirect_uri,
            "code": code,
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(token_url, data=data)
                response.raise_for_status()
                token_data = response.json()
                APP_CONFIG.logger.info(f"Instagram token exchange successful for user ID: {token_data}")
                # need a long live token, so we will use the short-lived token to get a long-lived token
                short_data = {
                    "client_id": self.instagram_client_id,
                    "client_secret": self.instagram_client_secret,
                    "grant_type": "fb_exchange_token",  # Token Exchange Grant
                    "fb_exchange_token": token_data["access_token"]  # short live token to get the longed live token
                }
                try:
                    async with httpx.AsyncClient(timeout=30.0) as client2:
                        response = await client2.post(token_url, data=short_data)
                        response.raise_for_status()
                        token_data = response.json()
                        return InstagramTokenResponse(
                            access_token=token_data["access_token"]
                        )
                except httpx.TimeoutException:
                    APP_CONFIG.logger.error("Timeout while exchanging Instagram code for tokens")
                    raise NetworkError("Request timeout during Instagram token exchange")
                except httpx.HTTPStatusError as e:
                    APP_CONFIG.logger.error(
                        f"HTTP error during Instagram token exchange: {e.response.status_code}")
                    raise OAuthError(f"Instagram token exchange failed: {e.response.text}")
                except Exception as e:
                    APP_CONFIG.logger.error(f"Unexpected error during Instagram token exchange: {str(e)}")
                    raise OAuthError("Instagram token exchange failed")

        except httpx.TimeoutException:
            APP_CONFIG.logger.error("Timeout while exchanging Instagram code for tokens")
            raise NetworkError("Request timeout during Instagram token exchange")
        except httpx.HTTPStatusError as e:
            APP_CONFIG.logger.error(
                f"HTTP error during Instagram token exchange: {e.response.status_code}"
            )
            raise OAuthError(f"Instagram token exchange failed: {e.response.text}")
        except Exception as e:
            APP_CONFIG.logger.error(f"Unexpected error during Instagram token exchange: {str(e)}")
            raise OAuthError("Instagram token exchange failed")

    async def get_instagram_user_info(self, access_token: str) -> InstagramUserInfo:
        """
        Using the short-lived Instagram access_token, fetch basic user info.
        """
        user_info_url = "https://graph.instagram.com/me"
        params = {"fields": "id,username,account_type", "access_token": access_token}

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(user_info_url, params=params)
                response.raise_for_status()
                user_data = response.json()
                return InstagramUserInfo(
                    id=str(user_data["id"]),
                    username=user_data["username"],
                    account_type=user_data.get("account_type", "PERSONAL"),
                )
        except httpx.TimeoutException:
            APP_CONFIG.logger.error("Timeout while fetching Instagram user info")
            raise NetworkError("Request timeout during Instagram user info fetch")
        except httpx.HTTPStatusError as e:
            APP_CONFIG.logger.error(
                f"HTTP error during Instagram user info fetch: {e.response.status_code}"
            )
            raise OAuthError(f"Instagram user info fetch failed: {e.response.text}")
        except Exception as e:
            APP_CONFIG.logger.error(f"Unexpected error during Instagram user info fetch: {str(e)}")
            raise OAuthError("Instagram user info fetch failed")

    async def check_user_exists(self, email):
        """
        Check if the user exists in the database.
        This is a placeholder method and should be implemented based on your database logic.
        """
        # Implement your logic to check if the user exists
        bloom_filter_manager: CreatorBloomFilterManager = get_bloom_filter_manager(get_locobuzz_redis())
        if not bloom_filter_manager.check_email_exists(email):
            return False
        return True

    async def handle_instagram_oauth_callback(
            self, code: str, state: str, redirect_uri: str, db_conn: SyncDatabaseDB, role_uuid: str
    ) -> OAuthTokenResponse:
        """
        Validate CSRF state, exchange `code` for Instagram tokens,
        fetch minimal user info, upsert user in DB (creating a fake email),
        and return our own app JWTs.
        
        The state string may contain role_uuid in the format "instagram:user_type:role_uuid"
        """
        # Extract role_uuid from state if available
        inst_tokens = await self.exchange_instagram_code_for_tokens(code, redirect_uri)
        user_info = await self.get_instagram_user_info(inst_tokens.access_token)

        # Instagram access tokens typically expire in 60 days (short-lived) or never (long-lived)
        # For short-lived tokens, expire in 60 days
        expires_at = datetime.now(UTC).replace(microsecond=0) + timedelta(days=60)

        # Instagram does not return email; we fabricate one
        compatible_user_info = type(
            "UserInfo",
            (),
            {
                "sub": user_info.id,
                "id": user_info.id,
                "email": f"{user_info.username}@instagram.com",
                "name": user_info.username,
                "picture": None,
            },
        )()

        user_data = await self.create_or_update_user_from_oauth(
            user_info=compatible_user_info,
            provider=SourceRegister.INSTAGRAM_OAUTH.value,
            access_token=inst_tokens.access_token,
            refresh_token=None,
            db_conn=db_conn,
            provider_user_id=user_info.id,
            scope="user_profile,user_media",
            expires_at=expires_at,
            role_uuid=role_uuid  # Pass the extracted role_uuid
        )

        # Add message indicating if this was a new registration or login
        message = "Registration successful!" if user_data.get("is_new_user", False) else "Login successful!"

        return OAuthTokenResponse(
            access_token=user_data["access_token"],
            refresh_token=user_data["refresh_token"],
            token_type=user_data["token_type"],
            expires_in=user_data["expires_in"],
            message=message
        )

    async def handle_facebook_oauth_callback(
            self, code: str, state: str, redirect_uri: str, db_conn: SyncDatabaseDB, role_uuid: str
    ) -> OAuthTokenResponse:
        """
        Handle Facebook OAuth callback with Instagram page access.
        
        This method exchanges Facebook authorization code for tokens and creates user profile.
        For influencers, it also fetches Instagram pages and handles page selection flow.
        """
        try:
            # Exchange code for Facebook tokens using Instagram handlers (they share the same API)
            facebook_tokens = await self.exchange_instagram_code_for_tokens(code, redirect_uri)
            
            # Get Facebook user info
            handler = get_oauth_handler("facebook")
            user_info = await handler.get_user_info(facebook_tokens.access_token)

            # Facebook access tokens typically have longer expiry
            expires_at = datetime.now(UTC).replace(microsecond=0) + timedelta(days=60)

            # Facebook provides email, so we can use it directly
            # But fallback to fabricated email if not available
            email = getattr(user_info, 'email', f"{user_info.username}@facebook.com")
            
            compatible_user_info = type(
                "UserInfo",
                (),
                {
                    "sub": user_info.id,
                    "id": user_info.id,
                    "email": email,
                    "name": user_info.username,
                    "picture": None,
                },
            )()

            user_data = await self.create_or_update_user_from_oauth(
                user_info=compatible_user_info,
                provider=SourceRegister.INSTAGRAM_OAUTH.value,  # We use Instagram OAuth value for Facebook too
                access_token=facebook_tokens.access_token,
                refresh_token=None,
                db_conn=db_conn,
                provider_user_id=user_info.id,
                scope="public_profile,email,pages_show_list,pages_read_engagement,instagram_basic,instagram_manage_comments,instagram_manage_insights,instagram_content_publish,pages_manage_metadata",
                expires_at=expires_at,
                role_uuid=role_uuid
            )

            # Add message indicating if this was a new registration or login
            message = "Registration successful!" if user_data.get("is_new_user", False) else "Login successful!"
            
            # Check if the user needs Instagram page selection
            needs_page_selection = False
            if "metadata_json" in user_data and isinstance(user_data["metadata_json"], dict):
                needs_page_selection = user_data["metadata_json"].get("needs_instagram_page_selection", False)
            
            response = OAuthTokenResponse(
                access_token=user_data["access_token"],
                refresh_token=user_data["refresh_token"],
                token_type=user_data["token_type"],
                expires_in=user_data["expires_in"],
                message=message
            )
            
            # Add page selection flag to the response extra_data
            if needs_page_selection:
                response.extra_data = {
                    "needs_instagram_page_selection": True,
                    "user_id": user_data.get("user_id")
                }
                
            return response
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Facebook OAuth callback failed: {str(e)}")
            raise OAuthError(f"Facebook authentication failed: {str(e)}")

    async def get_auth_method_id_cache_aside(self, method_key: str, db_conn: SyncDatabaseDB) -> str | None:
        """
        Get auth method ID using cache-aside pattern.
        First checks Redis, then falls back to database if not found.

        Args:
            method_key: The auth method key to look up
            db_conn: Database connection

        Returns:
            The auth method ID as string or None if not found
        """
        from app.core.redis_keys import RedisConfig
        from database_helper.database.models import MasterAuthMethod

        # 1. Try to get from Redis cache first
        auth_methods_key = "CreatorVerse:auth:methods"
        cached_auth_method_id = self.redis_client.hget(auth_methods_key, method_key)

        if cached_auth_method_id:
            APP_CONFIG.logger.debug(f"Auth method {method_key} found in cache")
            return str(cached_auth_method_id)  # Ensure string return

        # 2. Not in cache, try to get all auth methods from database
        try:
            with db_conn.transaction() as session:
                # Get all auth methods
                auth_methods = session.query(MasterAuthMethod).all()

                if not auth_methods:
                    APP_CONFIG.logger.warning("No auth methods found in database")
                    return None

                # Build cache dictionary - ensure all values are strings
                auth_methods_dict = {str(method.method_key): str(method.id) for method in auth_methods}

                # Store in Redis with appropriate TTL
                if auth_methods_dict:
                    pipe = self.redis_client.pipeline()
                    pipe.hmset(auth_methods_key, auth_methods_dict)
                    pipe.expire(auth_methods_key, RedisConfig.RBAC_TTL)
                    pipe.execute()

                # Return the requested auth method ID if found
                found_id = auth_methods_dict.get(str(method_key))
                return str(found_id) if found_id else None

        except Exception as e:
            APP_CONFIG.logger.error(f"Error fetching auth method from database: {str(e)}")
            return None


def get_oauth_service(redis_client: redis.Redis) -> OAuthService:
    """
    Factory function to create an OAuthService instance
    """
    return OAuthService(redis_client)
