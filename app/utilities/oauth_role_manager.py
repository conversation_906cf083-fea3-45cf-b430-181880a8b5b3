"""
Enhanced role management for OAuth flows.
Handles role_uuid validation and retrieval during registration and login.
"""
from typing import Dict, Optional

from app.core.config import APP_CONFIG
from app.core.enums_data import UserType
from app.core.redis_keys import RedisKeys


class OAuthRoleManager:
    """
    Manages role validation and resolution for OAuth flows.
    Ensures consistent role_uuid handling for both influencer and brand users.
    """

    def __init__(self, redis_client):
        self.redis_client = redis_client

    async def get_role_uuid_for_user_type(self, user_type: str, db_conn) -> Optional[str]:
        """
        Resolves role_uuid from user_type using cache-aside pattern.
        
        Args:
            user_type: String user type (influencer, brand)
            db_conn: Database connection
            
        Returns:
            role_uuid if found, None otherwise
        """
        # Normalize user_type
        user_type = user_type.lower()

        # Get role UUID from Redis cache first
        roles_cache = self.redis_client.hgetall(RedisKeys.rbac_roles())

        # Look for exact match by role
        for uuid, name in roles_cache.items():
            if uuid.lower() == user_type:
                APP_CONFIG.logger.debug(f"Found role_uuid {uuid} for {user_type} in Redis cache")
                return str(uuid)

        # If not in cache, check database directly
        if db_conn:
            with db_conn.transaction() as session:
                from database_helper.database.models import MasterRole
                role = session.query(MasterRole).filter(
                    MasterRole.role_name.ilike(f"%{user_type}%")
                ).first()
                if role:
                    role_uuid = str(role.id)
                    APP_CONFIG.logger.debug(f"Found role_uuid {role_uuid} for {user_type} in database")
                    return role_uuid

        APP_CONFIG.logger.warning(f"Role not found for user type: {user_type}")
        return None

    async def get_user_type_for_role_uuid(self, role_uuid: str, db_conn) -> Optional[UserType]:
        """
        Resolves user_type (role name) from role_uuid using cache-aside pattern.
        This is the opposite of get_role_uuid_for_user_type.

        Args:
            role_uuid: String role UUID
            db_conn: Database connection

        Returns:
            user_type/role_name if found, None otherwise
        """
        if not role_uuid:
            return None

        # Get role name from Redis cache first
        roles_cache = self.redis_client.hgetall(RedisKeys.rbac_roles())

        # Look for exact match by UUID
        role_name = roles_cache.get(role_uuid)
        if role_name:
            APP_CONFIG.logger.debug(f"Found user_type {role_name} for role_uuid {role_uuid} in Redis cache")
            return UserType(role_name.lower())

        # If not in cache, check database directly
        if db_conn:
            with db_conn.transaction() as session:
                from database_helper.database.models import MasterRole
                role = session.query(MasterRole).filter_by(id=role_uuid).first()
                if role:
                    role_name = role.role_name
                    APP_CONFIG.logger.debug(f"Found user_type {role_name} for role_uuid {role_uuid} in database")
                    return role_name.lower()

        APP_CONFIG.logger.warning(f"User type not found for role UUID: {role_uuid}")
        return None

    async def validate_role_uuid(self, role_uuid: str, expected_user_type: Optional[str] = None) -> bool:
        """
        Validates that a role_uuid exists and optionally matches expected user type.
        
        Args:
            role_uuid: The UUID to validate
            expected_user_type: Optional user type to validate against
            
        Returns:
            True if valid, False otherwise
        """
        if not role_uuid:
            return False

        # Get roles from cache
        roles_cache = self.redis_client.hgetall(RedisKeys.rbac_roles())

        # Check if role exists
        role_name = roles_cache.get(role_uuid)
        if not role_name:
            APP_CONFIG.logger.warning(f"Role UUID {role_uuid} not found in cache")
            return False

        # If expected type is provided, validate match
        if expected_user_type and role_name.lower() != expected_user_type.lower():
            APP_CONFIG.logger.warning(
                f"Role UUID {role_uuid} ({role_name}) doesn't match expected type {expected_user_type}")
            return False

        return True

    def get_default_roles(self) -> Dict[str, str]:
        """Get default role mappings when Redis cache is unavailable"""
        return {
            "11239913-1b15-4724-b176-270dff840ef7": "influencer",
            "9798e242-f889-497f-8a25-65479a57f7fa": "org_owner",
            "4d0810d1-17be-444c-9376-add7822ce349": "org_admin",
            "2389b63c-b9c1-4e81-b786-8ba2eaa58bf3": "brand_user"
        }


def get_oauth_role_manager(redis_client) -> OAuthRoleManager:
    """Factory function to get OAuthRoleManager instance"""
    return OAuthRoleManager(redis_client)
