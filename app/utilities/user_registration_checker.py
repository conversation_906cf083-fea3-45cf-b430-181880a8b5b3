from typing import Optional, Dict, Any
from database_helper.database.sync_db2 import SyncDatabaseD<PERSON>
from database_helper.database.models import <PERSON>r, OAuthAccount
from app.core.config import APP_CONFIG
from app.core.redis_keys import Red<PERSON>K<PERSON>s, RedisConfig
from app.utilities.bloom_filter import get_bloom_filter_manager


async def check_user_registration_status(
    email: str,
    provider: str,
    redis_client,
    db_conn: SyncDatabaseDB
) -> Dict[str, Any]:
    """
    Check if user is already registered and their OAuth status.
    Uses cache-aside pattern for performance.
    
    Returns:
        {
            "is_registered": bool,
            "has_oauth": bool,
            "user_id": str | None,
            "message": str
        }
    """
    try:
        email = email.lower().strip()
        
        # 1. Quick bloom filter check first
        bloom_manager = get_bloom_filter_manager(redis_client)
        if not bloom_manager.check_email_exists(email):
            return {
                "is_registered": False,
                "has_oauth": False,
                "user_id": None,
                "message": "User not registered"
            }
        
        # 2. Check Redis cache for user data
        user_cache_key = RedisKeys.user_by_email(email)
        cached_user = redis_client.hgetall(user_cache_key)
        
        user_id = None
        if cached_user:
            user_id = cached_user.get("id")
        else:
            # 3. Fallback to database if not in cache
            with db_conn.transaction() as session:
                user = session.query(User).filter_by(email=email).first()
                if user:
                    user_id = str(user.id)
                    # Cache the user data
                    user_dict = {
                        "id": user_id,
                        "email": user.email,
                        "name": user.name or "",
                        "status": user.status or "",
                        "is_active": str(user.is_active),
                        "is_email_verified": str(user.is_email_verified),
                    }
                    redis_client.hset(user_cache_key, mapping=user_dict)
                    redis_client.expire(user_cache_key, RedisConfig.USER_TTL)
        
        if not user_id:
            return {
                "is_registered": False,
                "has_oauth": False,
                "user_id": None,
                "message": "User not registered"
            }
        
        # 4. Check OAuth account status
        oauth_cache_key = f"CreatorVerse:oauth:user:{user_id}:{provider}"
        cached_oauth = redis_client.get(oauth_cache_key)
        
        has_oauth = False
        if cached_oauth:
            has_oauth = cached_oauth == "1"
        else:
            # Check database for OAuth account
            with db_conn.transaction() as session:
                oauth_account = session.query(OAuthAccount).filter_by(
                    user_id=user_id,
                    provider=provider
                ).first()
                
                has_oauth = oauth_account is not None
                
                # Cache the result
                redis_client.set(oauth_cache_key, "1" if has_oauth else "0", ex=3600)
        
        return {
            "is_registered": True,
            "has_oauth": has_oauth,
            "user_id": user_id,
            "message": "Already linked to OAuth" if has_oauth else "User registered, ready for OAuth"
        }
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Error checking user registration status: {str(e)}")
        return {
            "is_registered": False,
            "has_oauth": False,
            "user_id": None,
            "message": "Error checking registration status"
        }


async def get_user_by_email_cache_aside(
    email: str,
    redis_client,
    db_conn: SyncDatabaseDB
) -> Optional[Dict[str, Any]]:
    """
    Get user by email using cache-aside pattern.
    Returns user data dict or None if not found.
    """
    try:
        email = email.lower().strip()
        
        # 1. Try Redis cache first
        user_cache_key = RedisKeys.user_by_email(email)
        cached_user = redis_client.hgetall(user_cache_key)
        
        if cached_user:
            APP_CONFIG.logger.debug(f"User {email} found in cache")
            return dict(cached_user)
        
        # 2. Fallback to database
        with db_conn.transaction() as session:
            user = session.query(User).filter_by(email=email).first()
            
            if not user:
                return None
            
            # 3. Cache the result
            user_dict = {
                "id": str(user.id),
                "email": user.email,
                "name": user.name or "",
                "phone_number": user.phone_number or "",
                "status": user.status or "",
                "is_active": str(user.is_active),
                "is_email_verified": str(user.is_email_verified),
            }
            
            redis_client.hset(user_cache_key, mapping=user_dict)
            redis_client.expire(user_cache_key, RedisConfig.USER_TTL)
            
            APP_CONFIG.logger.debug(f"User {email} cached from database")
            return user_dict
            
    except Exception as e:
        APP_CONFIG.logger.error(f"Error getting user by email: {str(e)}")
        return None
