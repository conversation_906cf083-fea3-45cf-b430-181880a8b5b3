"""
Email service for sending emails using Mailgun API with async support.
"""
from functools import lru_cache
from typing import Optional

import httpx
from pydantic import BaseModel
from app.core.config import APP_CONFIG
from app.templates.email_templates import (
    get_brand_login_otp_email_template,
    get_brand_registration_otp_email_template,
)


class EmailConfig(BaseModel):
    """Email configuration settings"""
    mailgun_domain: str
    mailgun_secret: str
    sender_name: str = "CreatorVerse"


class EmailService:
    """Async email service using Mailgun API with connection reuse"""

    # Class-level httpx client for connection pooling
    _http_client: Optional[httpx.AsyncClient] = None

    def __init__(self, config: EmailConfig):
        """
        Initialize email service with configuration.

        Args:
            config: Email configuration settings
        """

        self.config = config
        self.logger = APP_CONFIG.logger
        self.base_url = f"https://api.mailgun.net/v3/{self.config.mailgun_domain}"
        self.auth = ("api", self.config.mailgun_secret)

    async def get_http_client(self) -> httpx.AsyncClient:
        """
        Get or create the shared HTTP client with connection pooling.
        
        Returns:
            httpx.AsyncClient: Shared HTTP client
        """
        if EmailService._http_client is None or EmailService._http_client.is_closed:
            # Create new client with connection pooling
            EmailService._http_client = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
            )
            self.logger.info("Created new Mailgun HTTP client")
        return EmailService._http_client

    async def send_email(
            self,
            text: str,
            receiver: str,
            subject: str,
            sender: Optional[str] = None,
            report_name: Optional[str] = None,
    ) -> bool:
        """
        Send email using Mailgun API asynchronously with connection reuse.

        Args:
            text: HTML content of the email
            receiver: Recipient email address
            subject: Email subject
            sender: Sender name (optional, defaults to config sender_name)
            report_name: Report name for logging (optional)

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        sender_name = sender or self.config.sender_name

        email_data = {
            "from": f"{sender_name} <no-reply@{self.config.mailgun_domain}>",
            "to": receiver,
            "subject": subject,
            "html": text,
        }

        try:
            client = await self.get_http_client()
            response = await client.post(
                f"{self.base_url}/messages",
                auth=self.auth,
                data=email_data,
            )

            if response.status_code == 200:
                self.logger.info(f"Email sent successfully to {receiver}")
                return True

            self.logger.error(
                f"Failed to send email to {receiver}. "
                f"Status: {response.status_code}, Response: {response.text}"
            )
            return False

        except httpx.TimeoutException:
            self.logger.error(f"Timeout while sending email to {receiver}")
            return False

        except httpx.RequestError as e:
            self.logger.error(f"Network error while sending email to {receiver}: {str(e)}")
            return False

        except Exception as e:
            self.logger.error(f"Unexpected error while sending email to {receiver}: {str(e)}")
            return False

    async def send_registration_otp_email(self, email: str, otp: str, user_role: str = "influencer") -> bool:
        """
        Send OTP verification email for registration.

        Args:
            email: Recipient email address
            otp: One-time password
            user_role: User role (influencer/brand) to determine template

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if user_role.lower() == "brand":
            subject = "CreatorVerse Business - Welcome Partner! Verify Your Business Email"
            html_content = get_brand_registration_otp_email_template(otp, email)
            self.logger.info(f"Sending brand registration OTP email to {email}")
        else:
            subject = "CreatorVerse - Welcome! Verify Your Email"
            html_content = get_registration_otp_email_template(otp, email)
            self.logger.info(f"Sending influencer registration OTP email to {email}")

        return await self.send_email(
            text=html_content,
            receiver=email,
            subject=subject,
            report_name=f"{user_role}_registration_otp_verification",
        )

    async def send_brand_registration_otp_email(self, email: str, otp: str) -> bool:
        """
        Send OTP verification email for brand registration.

        Args:
            email: Recipient email address
            otp: One-time password

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        subject = "CreatorVerse Business - Welcome Partner! Verify Your Business Email"
        html_content = get_brand_registration_otp_email_template(otp, email)
        self.logger.info(f"Sending brand registration OTP email to {email}")

        return await self.send_email(
            text=html_content,
            receiver=email,
            subject=subject,
            report_name="brand_registration_otp_verification",
        )

    async def send_login_otp_email(self, email: str, otp: str, user_role: str = "influencer") -> bool:
        """
        Send OTP verification email for login.

        Args:
            email: Recipient email address
            otp: One-time password
            user_role: User role (influencer/brand) to determine template

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if user_role.lower() == "brand":
            subject = "CreatorVerse Business - Business Account Login Verification"
            html_content = get_brand_login_otp_email_template(otp, email)
            self.logger.info(f"Sending brand login OTP email to {email}")
        else:
            subject = "CreatorVerse - Login Verification Code"
            html_content = get_login_otp_email_template(otp, email)
            self.logger.info(f"Sending influencer login OTP email to {email}")

        return await self.send_email(
            text=html_content,
            receiver=email,
            subject=subject,
            report_name=f"{user_role}_login_otp_verification",
        )

    async def send_welcome_email(self, email: str, user_role: str = "influencer") -> bool:
        """
        Send welcome email after successful registration.

        Args:
            email: Recipient email address
            user_role: User role (influencer/brand) to determine template

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if user_role.lower() == "brand":
            subject = "🎉 Welcome to CreatorVerse Business - Let's Elevate Your Brand!"
            html_content = get_brand_welcome_email_template(email)
            self.logger.info(f"Sending brand welcome email to {email}")
        else:
            subject = "🎉 Welcome to CreatorVerse - Your Creator Journey Starts Now!"
            html_content = get_influencer_welcome_email_template(email)
            self.logger.info(f"Sending influencer welcome email to {email}")

        return await self.send_email(
            text=html_content,
            receiver=email,
            subject=subject,
            report_name=f"{user_role}_welcome_email",
        )

    @classmethod
    async def close(cls) -> None:
        """Close the shared HTTP client when the application shuts down."""
        if cls._http_client and not cls._http_client.is_closed:
            await cls._http_client.aclose()
            cls._http_client = None


def get_registration_otp_email_template(otp: str, email: str) -> str:
    """
    Generate HTML email template for registration OTP verification.

    Args:
        otp: One-time password
        email: Email address

    Returns:
        str: HTML email content
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to CreatorVerse - Email Verification</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #6366f1;
                margin-bottom: 10px;
            }}
            .welcome-banner {{
                background: linear-gradient(135deg, #6366f1, #8b5cf6);
                color: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                margin-bottom: 20px;
            }}
            .otp-box {{
                background: #f8fafc;
                border: 2px solid #10b981;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                margin: 20px 0;
            }}
            .otp-code {{
                font-size: 32px;
                font-weight: bold;
                color: #10b981;
                letter-spacing: 4px;
                margin: 10px 0;
            }}
            .benefits {{
                background: #f0f9ff;
                border-left: 4px solid #0ea5e9;
                padding: 15px;
                margin: 20px 0;
            }}
            .warning {{
                background: #fef3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                text-align: center;
                color: #666;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse</div>
                <div class="welcome-banner">
                    <h1 style="margin: 0; font-size: 24px;">🎉 Welcome to CreatorVerse!</h1>
                    <p style="margin: 10px 0 0 0; opacity: 0.9;">You're one step away from joining our creator community</p>
                </div>
            </div>

            <p>Hi {email},</p>

            <p>Thank you for choosing CreatorVerse! We're excited to have you join our community of creators. To complete your registration and activate your account, please verify your email address using the code below:</p>

            <div class="otp-box">
                <div style="font-size: 16px; margin-bottom: 10px;">🔐 Your Verification Code</div>
                <div class="otp-code">{otp}</div>
                <div style="font-size: 14px; color: #666; margin-top: 10px;">⏰ This code expires in 5 minutes</div>
            </div>

            <div class="benefits">
                <h3 style="margin-top: 0; color: #0ea5e9;">What's next after verification?</h3>
                <ul style="padding-left: 20px;">
                    <li>✨ Complete your creator profile</li>
                    <li>🔗 Connect your social media accounts</li>
                    <li>📊 Access detailed analytics and insights</li>
                    <li>🤝 Start collaborating with brands</li>
                </ul>
            </div>

            <div class="warning">
                <strong>🔒 Security Notice:</strong> Never share this code with anyone. CreatorVerse will never ask for your verification code via phone or unsolicited emails.
            </div>

            <p>If you didn't create this account, please ignore this email and the account will not be activated.</p>

            <div class="footer">
                <p>© 2025 CreatorVerse. All rights reserved.</p>
                <p>This is an automated message, please do not reply to this email.</p>
                <p>Need help? Contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """


def get_login_otp_email_template(otp: str, email: str) -> str:
    """
    Generate HTML email template for login OTP verification.

    Args:
        otp: One-time password
        email: Email address

    Returns:
        str: HTML email content
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CreatorVerse - Login Verification</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #6366f1;
                margin-bottom: 10px;
            }}
            .login-banner {{
                background: linear-gradient(135deg, #f59e0b, #ef4444);
                color: white;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                margin-bottom: 20px;
            }}
            .otp-box {{
                background: #fef7ed;
                border: 2px solid #f59e0b;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                margin: 20px 0;
            }}
            .otp-code {{
                font-size: 32px;
                font-weight: bold;
                color: #f59e0b;
                letter-spacing: 4px;
                margin: 10px 0;
            }}
            .security-info {{
                background: #fef2f2;
                border-left: 4px solid #ef4444;
                padding: 15px;
                margin: 20px 0;
                color: #991b1b;
            }}
            .warning {{
                background: #fef3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                text-align: center;
                color: #666;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse</div>
                <div class="login-banner">
                    <h1 style="margin: 0; font-size: 20px;">🔐 Login Verification Required</h1>
                </div>
            </div>

            <p>Hi {email},</p>

            <p>Someone is trying to log in to your CreatorVerse account. To ensure the security of your account, please use the verification code below to complete your login:</p>

            <div class="otp-box">
                <div style="font-size: 16px; margin-bottom: 10px;">🔑 Your Login Code</div>
                <div class="otp-code">{otp}</div>
                <div style="font-size: 14px; color: #666; margin-top: 10px;">⏰ This code expires in 5 minutes</div>
            </div>

            <div class="security-info">
                <h3 style="margin-top: 0;">🚨 Didn't try to log in?</h3>
                <p style="margin-bottom: 0;">If you didn't attempt to log in, please:</p>
                <ul style="margin: 10px 0;">
                    <li>Ignore this email - your account remains secure</li>
                    <li>Consider changing your password if you're concerned</li>
                    <li>Contact our support team if this happens frequently</li>
                </ul>
            </div>

            <div class="warning">
                <strong>🔒 Security Reminder:</strong> Never share this code with anyone. CreatorVerse support will never ask for your verification codes.
            </div>

            <p><strong>Account Security Tip:</strong> Always log out from shared devices and use strong, unique passwords.</p>

            <div class="footer">
                <p>© 2025 CreatorVerse. All rights reserved.</p>
                <p>This is an automated security message, please do not reply to this email.</p>
                <p>Questions? Contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """

    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to CreatorVerse Business - Email Verification</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8fafc;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                border-top: 4px solid #7c3aed;
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #7c3aed;
                margin-bottom: 10px;
            }}
            .business-badge {{
                display: inline-block;
                background: linear-gradient(135deg, #7c3aed, #a855f7);
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 15px;
            }}
            .welcome-banner {{
                background: linear-gradient(135deg, #7c3aed, #a855f7);
                color: white;
                padding: 25px;
                border-radius: 10px;
                text-align: center;
                margin-bottom: 25px;
            }}
            .otp-box {{
                background: #faf5ff;
                border: 3px solid #7c3aed;
                border-radius: 10px;
                padding: 25px;
                text-align: center;
                margin: 25px 0;
            }}
            .otp-code {{
                font-size: 36px;
                font-weight: bold;
                color: #7c3aed;
                letter-spacing: 6px;
                margin: 15px 0;
                font-family: 'Courier New', monospace;
            }}
            .business-benefits {{
                background: #f0f9ff;
                border-left: 4px solid #0ea5e9;
                padding: 20px;
                margin: 25px 0;
                border-radius: 0 8px 8px 0;
            }}
            .feature-grid {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin: 20px 0;
            }}
            .feature-item {{
                background: #f8fafc;
                padding: 15px;
                border-radius: 8px;
                border-left: 3px solid #7c3aed;
            }}
            .security-notice {{
                background: #fef2f2;
                border: 1px solid #fecaca;
                border-radius: 8px;
                padding: 20px;
                margin: 25px 0;
                color: #991b1b;
            }}
            .footer {{
                margin-top: 35px;
                padding-top: 25px;
                border-top: 2px solid #e5e7eb;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
            }}
            .business-support {{
                background: #f3f4f6;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                text-align: center;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse</div>
                <div class="business-badge">Business Account</div>
                <div class="welcome-banner">
                    <h1 style="margin: 0; font-size: 26px;">🤝 Welcome to CreatorVerse Business!</h1>
                    <p style="margin: 15px 0 0 0; opacity: 0.95; font-size: 16px;">Your gateway to connecting with top creators</p>
                </div>
            </div>

            <p>Dear Business Partner,</p>

            <p>Welcome to CreatorVerse Business! We're thrilled to have your brand join our exclusive network of innovative companies. To activate your business account and start collaborating with our curated creator community, please verify your email address using the code below:</p>

            <div class="otp-box">
                <div style="font-size: 18px; margin-bottom: 15px; color: #7c3aed;">🔐 Business Verification Code</div>
                <div class="otp-code">{otp}</div>
                <div style="font-size: 14px; color: #6b7280; margin-top: 15px;">⏰ This code expires in 5 minutes</div>
            </div>

            <div class="business-benefits">
                <h3 style="margin-top: 0; color: #0ea5e9; font-size: 20px;">🚀 What's waiting for your brand?</h3>
                <div class="feature-grid">
                    <div class="feature-item">
                        <strong>🎯 Targeted Campaigns</strong><br>
                        <small>Connect with creators in your niche</small>
                    </div>
                    <div class="feature-item">
                        <strong>📊 Advanced Analytics</strong><br>
                        <small>Track campaign performance & ROI</small>
                    </div>
                    <div class="feature-item">
                        <strong>💼 Brand Management</strong><br>
                        <small>Centralized brand asset library</small>
                    </div>
                    <div class="feature-item">
                        <strong>🤖 Smart Matching</strong><br>
                        <small>AI-powered creator recommendations</small>
                    </div>
                </div>
            </div>

            <div class="business-support">
                <h4 style="margin-top: 0; color: #7c3aed;">🎯 Next Steps After Verification</h4>
                <ul style="text-align: left; display: inline-block; margin: 15px 0;">
                    <li>📝 Complete your brand profile</li>
                    <li>💳 Set up your campaign budget</li>
                    <li>🎨 Upload brand assets and guidelines</li>
                    <li>🔍 Discover and connect with creators</li>
                    <li>📈 Launch your first campaign</li>
                </ul>
            </div>

            <div class="security-notice">
                <strong>🛡️ Security Notice:</strong> This verification code is exclusively for your business account. Never share this code with anyone. CreatorVerse will never request your verification codes via phone calls or unsolicited communications.
            </div>

            <p>If you didn't request this business account registration, please ignore this email and the account will not be activated.</p>

            <div class="footer">
                <p><strong>CreatorVerse Business Team</strong></p>
                <p>© 2025 CreatorVerse. All rights reserved.</p>
                <p>This is an automated message for business account verification.</p>
                <p>Business Support: <strong><EMAIL></strong> | Priority Support: <strong>******-CREATOR</strong></p>
            </div>
        </div>
    </body>
    </html>
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CreatorVerse Business - Login Verification</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8fafc;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                border-top: 4px solid #dc2626;
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #7c3aed;
                margin-bottom: 10px;
            }}
            .business-badge {{
                display: inline-block;
                background: linear-gradient(135deg, #7c3aed, #a855f7);
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 15px;
            }}
            .login-banner {{
                background: linear-gradient(135deg, #dc2626, #ef4444);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                margin-bottom: 25px;
            }}
            .otp-box {{
                background: #fef2f2;
                border: 3px solid #dc2626;
                border-radius: 10px;
                padding: 25px;
                text-align: center;
                margin: 25px 0;
            }}
            .otp-code {{
                font-size: 36px;
                font-weight: bold;
                color: #dc2626;
                letter-spacing: 6px;
                margin: 15px 0;
                font-family: 'Courier New', monospace;
            }}
            .security-alert {{
                background: #fef3c7;
                border: 2px solid #f59e0b;
                border-radius: 10px;
                padding: 20px;
                margin: 25px 0;
                color: #92400e;
            }}
            .business-security {{
                background: #f0f9ff;
                border-left: 4px solid #0ea5e9;
                padding: 20px;
                margin: 25px 0;
                border-radius: 0 8px 8px 0;
            }}
            .quick-actions {{
                background: #f3f4f6;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
            }}
            .footer {{
                margin-top: 35px;
                padding-top: 25px;
                border-top: 2px solid #e5e7eb;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse</div>
                <div class="business-badge">Business Account</div>
                <div class="login-banner">
                    <h1 style="margin: 0; font-size: 22px;">🔐 Business Account Login Verification</h1>
                    <p style="margin: 10px 0 0 0; opacity: 0.95;">Secure access to your brand dashboard</p>
                </div>
            </div>

            <p>Dear Business Partner,</p>

            <p>Someone is attempting to access your CreatorVerse Business account. To ensure the security of your brand's data and campaigns, please use the verification code below to complete your login:</p>

            <div class="otp-box">
                <div style="font-size: 18px; margin-bottom: 15px; color: #dc2626;">🔑 Business Login Code</div>
                <div class="otp-code">{otp}</div>
                <div style="font-size: 14px; color: #6b7280; margin-top: 15px;">⏰ This code expires in 5 minutes</div>
            </div>

            <div class="business-security">
                <h3 style="margin-top: 0; color: #0ea5e9;">🛡️ Enhanced Business Security</h3>
                <p style="margin-bottom: 15px;">Your business account includes advanced security features:</p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>🔐 Two-factor authentication for all logins</li>
                    <li>📊 Login activity monitoring and alerts</li>
                    <li>🏢 Team member access controls</li>
                    <li>💼 Brand asset protection protocols</li>
                </ul>
            </div>

            <div class="security-alert">
                <h3 style="margin-top: 0;">⚠️ Didn't attempt to log in?</h3>
                <p style="margin-bottom: 15px;"><strong>If you didn't try to access your business account:</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>Ignore this email - your account remains secure</li>
                    <li>Consider updating your password immediately</li>
                    <li>Review your team member access permissions</li>
                    <li>Contact our business security team if this occurs repeatedly</li>
                </ul>
            </div>

            <div class="quick-actions">
                <h4 style="margin-top: 0; color: #7c3aed;">🚀 Quick Access After Login</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                    <div>📈 Campaign Dashboard</div>
                    <div>👥 Creator Discovery</div>
                    <div>💰 Budget Management</div>
                    <div>📊 Performance Analytics</div>
                </div>
            </div>

            <p><strong>Business Security Reminder:</strong> Always log out from shared devices and ensure your team members use strong, unique passwords for their accounts.</p>

            <div class="footer">
                <p><strong>CreatorVerse Business Security Team</strong></p>
                <p>© 2025 CreatorVerse. All rights reserved.</p>
                <p>This is an automated security message for business account verification.</p>
                <p>Business Security: <strong><EMAIL></strong> | Emergency: <strong>******-SECURE-BIZ</strong></p>
            </div>
        </div>
    </body>
    </html>
    """


def get_influencer_welcome_email_template(email: str) -> str:
    """
    Generate HTML email template for influencer welcome email.

    Args:
        email: Email address

    Returns:
        str: HTML email content
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to CreatorVerse - Your Creator Journey Begins!</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                border-top: 4px solid #10b981;
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 32px;
                font-weight: bold;
                color: #6366f1;
                margin-bottom: 10px;
            }}
            .celebration-banner {{
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 25px;
                border-radius: 12px;
                text-align: center;
                margin-bottom: 30px;
            }}
            .welcome-content {{
                background: #f0fdf4;
                border-left: 4px solid #10b981;
                padding: 20px;
                margin: 25px 0;
                border-radius: 0 8px 8px 0;
            }}
            .features-grid {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin: 30px 0;
            }}
            .feature-card {{
                background: #f8fafc;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                border: 2px solid #e2e8f0;
                transition: transform 0.2s ease;
            }}
            .feature-card:hover {{
                transform: translateY(-2px);
                border-color: #10b981;
            }}
            .feature-icon {{
                font-size: 24px;
                margin-bottom: 10px;
            }}
            .next-steps {{
                background: #fef3c7;
                border: 2px solid #f59e0b;
                border-radius: 10px;
                padding: 25px;
                margin: 30px 0;
            }}
            .cta-button {{
                display: inline-block;
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 15px 30px;
                border-radius: 8px;
                text-decoration: none;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
            }}
            .social-proof {{
                background: #f0f9ff;
                border-left: 4px solid #0ea5e9;
                padding: 20px;
                margin: 25px 0;
                border-radius: 0 8px 8px 0;
            }}
            .footer {{
                margin-top: 40px;
                padding-top: 25px;
                border-top: 2px solid #e5e7eb;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse</div>
                <div class="celebration-banner">
                    <h1 style="margin: 0; font-size: 28px;">🎉 Welcome to CreatorVerse!</h1>
                    <p style="margin: 15px 0 0 0; opacity: 0.95; font-size: 18px;">Your creator journey starts here</p>
                </div>
            </div>

            <p style="font-size: 18px;">Hi <strong>{email}</strong>,</p>

            <div class="welcome-content">
                <h2 style="margin-top: 0; color: #059669;">🚀 You're officially part of the CreatorVerse family!</h2>
                <p>We're thrilled to have you join thousands of creators who are already building amazing content and growing their audience with our platform.</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3 style="margin: 10px 0; color: #374151;">Analytics Dashboard</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">Track your growth, engagement, and performance metrics in real-time.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🤝</div>
                    <h3 style="margin: 10px 0; color: #374151;">Brand Collaborations</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">Connect with brands that align with your content and values.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3 style="margin: 10px 0; color: #374151;">Content Tools</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">Access powerful tools to create, edit, and optimize your content.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3 style="margin: 10px 0; color: #374151;">Monetization</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">Multiple revenue streams to help you earn from your creativity.</p>
                </div>
            </div>

            <div class="next-steps">
                <h3 style="margin-top: 0; color: #92400e;">⚡ Your Next Steps to Success</h3>
                <ol style="margin: 20px 0; padding-left: 20px;">
                    <li><strong>Complete Your Profile:</strong> Add your bio, profile picture, and social media links</li>
                    <li><strong>Connect Your Accounts:</strong> Link your Instagram, TikTok, YouTube, and other platforms</li>
                    <li><strong>Explore the Dashboard:</strong> Familiarize yourself with our analytics and content tools</li>
                    <li><strong>Join Our Community:</strong> Connect with other creators and share tips</li>
                    <li><strong>Browse Brand Opportunities:</strong> Start exploring collaboration requests</li>
                </ol>
                <div style="text-align: center;">
                    <a href="#" class="cta-button">Complete Your Profile Now →</a>
                </div>
            </div>

            <div class="social-proof">
                <h3 style="margin-top: 0; color: #0ea5e9;">💫 Join 50,000+ Successful Creators</h3>
                <p style="margin-bottom: 15px;">"CreatorVerse helped me turn my passion into a profitable business. The analytics alone are worth it!" - Sarah K., Fashion Creator</p>
                <p style="margin-bottom: 0; font-style: italic;">Average creator earnings increase by 300% in their first 6 months with CreatorVerse.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <h3 style="color: #374151;">Need Help Getting Started?</h3>
                <p>Our creator success team is here to help you every step of the way.</p>
                <p style="margin-top: 15px;">
                    📧 <strong>Email:</strong> <EMAIL><br>
                    💬 <strong>Live Chat:</strong> Available 24/7 in your dashboard<br>
                    📚 <strong>Knowledge Base:</strong> help.creatorverse.com
                </p>
            </div>

            <div class="footer">
                <p><strong>Welcome to your creator journey!</strong></p>
                <p>© 2025 CreatorVerse. All rights reserved.</p>
                <p>Follow us: Twitter @CreatorVerse | Instagram @CreatorVerse | TikTok @CreatorVerse</p>
                <p style="margin-top: 15px; font-size: 12px;">
                    You're receiving this because you just joined CreatorVerse.<br>
                    This is a one-time welcome email. Manage your email preferences in your dashboard.
                </p>
            </div>
        </div>
    </body>
    </html>
    """


def get_brand_welcome_email_template(email: str) -> str:
    """
    Generate HTML email template for brand welcome email.

    Args:
        email: Email address

    Returns:
        str: HTML email content
    """
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to CreatorVerse Business - Elevate Your Brand!</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8fafc;
            }}
            .container {{
                background: white;
                padding: 35px;
                border-radius: 16px;
                box-shadow: 0 8px 30px rgba(0,0,0,0.12);
                border-top: 5px solid #7c3aed;
            }}
            .header {{
                text-align: center;
                margin-bottom: 35px;
            }}
            .logo {{
                font-size: 32px;
                font-weight: bold;
                color: #7c3aed;
                margin-bottom: 10px;
            }}
            .business-badge {{
                display: inline-block;
                background: linear-gradient(135deg, #7c3aed, #a855f7);
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                font-size: 14px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 20px;
            }}
            .celebration-banner {{
                background: linear-gradient(135deg, #7c3aed, #a855f7);
                color: white;
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                margin-bottom: 35px;
            }}
            .welcome-content {{
                background: #faf5ff;
                border-left: 5px solid #7c3aed;
                padding: 25px;
                margin: 30px 0;
                border-radius: 0 10px 10px 0;
            }}
            .business-features {{
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 25px;
                margin: 35px 0;
            }}
            .feature-card {{
                background: #f8fafc;
                padding: 25px;
                border-radius: 12px;
                text-align: center;
                border: 2px solid #e2e8f0;
                transition: all 0.3s ease;
            }}
            .feature-card:hover {{
                transform: translateY(-3px);
                border-color: #7c3aed;
                box-shadow: 0 8px 25px rgba(124, 58, 237, 0.15);
            }}
            .feature-icon {{
                font-size: 30px;
                margin-bottom: 15px;
                display: block;
            }}
            .roi-highlight {{
                background: linear-gradient(135deg, #f59e0b, #d97706);
                color: white;
                padding: 25px;
                border-radius: 12px;
                text-align: center;
                margin: 30px 0;
            }}
            .action-steps {{
                background: #f0f9ff;
                border: 2px solid #0ea5e9;
                border-radius: 12px;
                padding: 30px;
                margin: 35px 0;
            }}
            .cta-button {{
                display: inline-block;
                background: linear-gradient(135deg, #7c3aed, #a855f7);
                color: white;
                padding: 18px 35px;
                border-radius: 10px;
                text-decoration: none;
                font-weight: bold;
                font-size: 16px;
                margin: 25px 0;
                text-align: center;
                box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
            }}
            .success-stories {{
                background: #f0fdf4;
                border-left: 5px solid #10b981;
                padding: 25px;
                margin: 30px 0;
                border-radius: 0 10px 10px 0;
            }}
            .business-support {{
                background: #f3f4f6;
                padding: 25px;
                border-radius: 12px;
                margin: 30px 0;
                text-align: center;
            }}
            .footer {{
                margin-top: 40px;
                padding-top: 30px;
                border-top: 3px solid #e5e7eb;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CreatorVerse</div>
                <div class="business-badge">Business Partner</div>
                <div class="celebration-banner">
                    <h1 style="margin: 0; font-size: 30px;">🚀 Welcome to CreatorVerse Business!</h1>
                    <p style="margin: 20px 0 0 0; opacity: 0.95; font-size: 18px;">Where brands meet their perfect creator matches</p>
                </div>
            </div>

            <p style="font-size: 18px;">Dear <strong>{email}</strong>,</p>

            <div class="welcome-content">
                <h2 style="margin-top: 0; color: #7c3aed;">🎯 Your Brand's Growth Journey Begins Now!</h2>
                <p style="font-size: 16px;">Welcome to the exclusive CreatorVerse Business network! You've just unlocked access to thousands of vetted creators who are ready to amplify your brand's message and drive real results.</p>
            </div>

            <div class="roi-highlight">
                <h3 style="margin-top: 0; font-size: 22px;">📈 Average Brand Results with CreatorVerse</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 20px;">
                    <div>
                        <div style="font-size: 24px; font-weight: bold;">3.5x</div>
                        <div style="font-size: 14px;">Engagement Rate</div>
                    </div>
                    <div>
                        <div style="font-size: 24px; font-weight: bold;">250%</div>
                        <div style="font-size: 14px;">ROI Increase</div>
                    </div>
                    <div>
                        <div style="font-size: 24px; font-weight: bold;">40%</div>
                        <div style="font-size: 14px;">Cost Reduction</div>
                    </div>
                </div>
            </div>

            <div class="business-features">
                <div class="feature-card">
                    <span class="feature-icon">🎯</span>
                    <h3 style="margin: 10px 0; color: #374151;">Smart Creator Matching</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">AI-powered algorithm matches your brand with creators who align with your values and target audience.</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">📊</span>
                    <h3 style="margin: 10px 0; color: #374151;">Advanced Analytics</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">Real-time campaign tracking with detailed ROI metrics and performance insights.</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🛡️</span>
                    <h3 style="margin: 10px 0; color: #374151;">Brand Safety</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">Comprehensive vetting process ensures all creators meet our quality and safety standards.</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">⚡</span>
                    <h3 style="margin: 10px 0; color: #374151;">Campaign Automation</h3>
                    <p style="margin: 0; color: #6b7280; font-size: 14px;">Streamlined workflow tools to manage multiple campaigns and collaborations efficiently.</p>
                </div>
            </div>

            <div class="action-steps">
                <h3 style="margin-top: 0; color: #0ea5e9; font-size: 22px;">🚀 Your First 48 Hours Action Plan</h3>
                <ol style="margin: 25px 0; padding-left: 25px; font-size: 16px;">
                    <li style="margin-bottom: 10px;"><strong>Complete Your Brand Profile:</strong> Add your brand story, assets, and campaign objectives</li>
                    <li style="margin-bottom: 10px;"><strong>Set Your Campaign Budget:</strong> Define your spending limits and preferred collaboration types</li>
                    <li style="margin-bottom: 10px;"><strong>Browse Creator Profiles:</strong> Explore our curated creator database and bookmark favorites</li>
                    <li style="margin-bottom: 10px;"><strong>Launch Your First Campaign:</strong> Start with a pilot project to test the waters</li>
                    <li style="margin-bottom: 10px;"><strong>Schedule Your Success Call:</strong> Book a strategy session with our brand experts</li>
                </ol>
                <div style="text-align: center;">
                    <a href="#" class="cta-button">Access Your Brand Dashboard →</a>
                </div>
            </div>

            <div class="success-stories">
                <h3 style="margin-top: 0; color: #10b981;">🏆 Success Story Spotlight</h3>
                <p style="margin-bottom: 15px; font-style: italic;">"CreatorVerse transformed our influencer marketing. We saw a 400% increase in engagement and 60% better conversion rates compared to traditional advertising. The creator quality is unmatched!"</p>
                <p style="margin-bottom: 0; font-weight: bold;">- Maria Rodriguez, Marketing Director at TechFlow Solutions</p>
            </div>

            <div class="business-support">
                <h3 style="color: #7c3aed; margin-top: 0;">🎯 Dedicated Business Support</h3>
                <p style="margin-bottom: 20px;">Your success is our priority. Our expert team is ready to help you maximize your campaigns.</p>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: left;">
                    <div>
                        <strong>🤝 Account Manager</strong><br>
                        <small>Dedicated support for enterprise clients</small>
                    </div>
                    <div>
                        <strong>📞 Priority Support</strong><br>
                        <small><EMAIL></small>
                    </div>
                    <div>
                        <strong>📚 Business Resources</strong><br>
                        <small>Case studies, best practices, guides</small>
                    </div>
                    <div>
                        <strong>🔧 Custom Solutions</strong><br>
                        <small>Tailored campaigns for unique needs</small>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin: 35px 0;">
                <h3 style="color: #374151;">Ready to Amplify Your Brand?</h3>
                <p style="font-size: 16px;">Join 500+ brands already seeing incredible results with CreatorVerse.</p>
                <div style="margin-top: 25px;">
                    <a href="#" class="cta-button">Start Your First Campaign →</a>
                </div>
            </div>

            <div class="footer">
                <p><strong>Welcome to the CreatorVerse Business Family!</strong></p>
                <p>© 2025 CreatorVerse Business. All rights reserved.</p>
                <p><strong>Business Support:</strong> <EMAIL> | <strong>Phone:</strong> ******-CREATOR-BIZ</p>
                <p style="margin-top: 20px; font-size: 12px;">
                    You're receiving this because you just joined CreatorVerse Business.<br>
                    This is a one-time welcome email. Manage your email preferences in your business dashboard.
                </p>
            </div>
        </div>
    </body>
    </html>
    """


@lru_cache(maxsize=1)
def get_email_service() -> EmailService:
    """
    Factory function to create EmailService instance with config-based settings.
    Uses caching to return the same instance for multiple calls.

    Returns:
        EmailService: Configured email service
    """

    config = EmailConfig(
        mailgun_domain=APP_CONFIG.MAILGUN_DOMAIN,
        mailgun_secret=APP_CONFIG.MAILGUN_SECRET,
        sender_name=APP_CONFIG.EMAIL_SENDER_NAME,
    )

    return EmailService(config)


# Add this to your application startup/shutdown handlers
async def close_email_service() -> None:
    """Close email service connections on application shutdown."""
    await EmailService.close()