"""
Redis pipeline optimization utilities for OTP operations.
Implements batch operations to reduce Redis round trips.
"""
import time
from typing import Dict, List, Optional, Tuple

from app.core.config import APP_CONFIG
from app.core.redis_keys import RedisKeys, RedisConfig
from app.core.security import hash_otp
from app.utilities.validation_functions import generate_otp
from app.core.config import DEFAULT_OTP_LENGTH

class OptimizedOTPManager:
    """
    Optimized OTP manager using Redis pipelines for batch operations.
    Implements cache-aside pattern with performance optimizations.
    """
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
    
    def check_existing_otp_status_pipeline(self, user_email: str) -> Tuple[bool, int, str]:
        """
        Optimized OTP status check using Redis pipeline.
        
        Returns:
            (has_valid_otp, remaining_seconds, message)
        """
        otp_key = RedisKeys.otp_key(user_email)
        
        try:
            # Use pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            pipe.hgetall(otp_key)
            pipe.ttl(otp_key)
            results = pipe.execute()
            
            stored_data, ttl_seconds = results
            
            if not stored_data or ttl_seconds <= 0:
                return False, 0, "No valid OTP found"
            
            return True, ttl_seconds, "Valid OTP exists"
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to check OTP status for {user_email}: {e}")
            # Fallback to individual operations
            return self._check_otp_status_fallback(user_email)
    
    def _check_otp_status_fallback(self, user_email: str) -> Tuple[bool, int, str]:
        """Fallback method for OTP status check."""
        otp_key = RedisKeys.otp_key(user_email)
        
        try:
            stored_data = self.redis_client.hgetall(otp_key)
            if not stored_data:
                return False, 0, "No valid OTP found"
            
            ttl_seconds = self.redis_client.ttl(otp_key)
            if ttl_seconds <= 0:
                return False, 0, "OTP expired"
            
            return True, ttl_seconds, "Valid OTP exists"
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Fallback OTP check failed for {user_email}: {e}")
            return False, 0, "OTP check failed"
    
    def create_otp_optimized(self, user_email: str) -> str:
        """
        Create OTP with optimized Redis operations.
        
        Returns:
            Generated OTP string
        """
        otp = generate_otp(DEFAULT_OTP_LENGTH)
        otp_hash = hash_otp(otp)
        otp_key = RedisKeys.otp_key(user_email)
        
        now_ts = int(time.time())
        
        otp_data = {
            "otp_hash": otp_hash,
            "generated_at": str(now_ts),
            "failed_attempts": "0",
            "lockout_until": "0"
        }
        
        try:
            # Use pipeline for atomic OTP creation
            pipe = self.redis_client.pipeline()
            pipe.hset(otp_key, mapping=otp_data)
            pipe.expire(otp_key, RedisConfig.OTP_TTL)
            
            # Optional: Set rate limiting key
            rate_limit_key = f"CreatorVerse:otp_rate_limit:{user_email}"
            pipe.incr(rate_limit_key)
            pipe.expire(rate_limit_key, 3600)  # 1 hour rate limit window
            
            results = pipe.execute()
            
            if results[0]:  # HSET was successful
                APP_CONFIG.logger.info(f"OTP created successfully for {user_email}")
                return otp
            else:
                raise Exception("Failed to store OTP in Redis")
                
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to create OTP for {user_email}: {e}")
            # Fallback to individual operations
            return self._create_otp_fallback(user_email, otp_length)
    
    def _create_otp_fallback(self, user_email: str, otp_length: int) -> str:
        """Fallback method for OTP creation."""
        otp = generate_otp(otp_length)
        otp_hash = hash_otp(otp)
        otp_key = RedisKeys.otp_key(user_email)
        
        now_ts = int(time.time())
        
        otp_data = {
            "otp_hash": otp_hash,
            "generated_at": str(now_ts),
            "failed_attempts": "0",
            "lockout_until": "0"
        }
        
        try:
            self.redis_client.hset(otp_key, mapping=otp_data)
            self.redis_client.expire(otp_key, RedisConfig.OTP_TTL)
            APP_CONFIG.logger.info(f"OTP created with fallback method for {user_email}")
            return otp
        except Exception as e:
            APP_CONFIG.logger.error(f"Fallback OTP creation failed for {user_email}: {e}")
            raise
    
    def batch_check_otp_status(self, user_emails: List[str]) -> Dict[str, Tuple[bool, int, str]]:
        """
        Check OTP status for multiple emails in a single pipeline operation.
        
        Args:
            user_emails: List of email addresses to check
            
        Returns:
            Dictionary mapping email to (has_valid_otp, remaining_seconds, message)
        """
        if not user_emails:
            return {}
        
        try:
            # Build pipeline for batch operations
            pipe = self.redis_client.pipeline()
            
            for email in user_emails:
                otp_key = RedisKeys.otp_key(email)
                pipe.hgetall(otp_key)
                pipe.ttl(otp_key)
            
            # Execute all operations at once
            results = pipe.execute()
            
            # Process results in pairs (hgetall, ttl)
            otp_status = {}
            for i, email in enumerate(user_emails):
                stored_data = results[i * 2]
                ttl_seconds = results[i * 2 + 1]
                
                if not stored_data or ttl_seconds <= 0:
                    otp_status[email] = (False, 0, "No valid OTP found")
                else:
                    otp_status[email] = (True, ttl_seconds, "Valid OTP exists")
            
            APP_CONFIG.logger.info(f"Batch OTP status check completed for {len(user_emails)} emails")
            return otp_status
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Batch OTP status check failed: {e}")
            # Fallback to individual checks
            return {email: self._check_otp_status_fallback(email) for email in user_emails}
    
    def cleanup_expired_otps_batch(self, user_emails: List[str]) -> int:
        """
        Clean up expired OTPs for multiple users in batch.
        
        Args:
            user_emails: List of email addresses to clean up
            
        Returns:
            Number of OTPs cleaned up
        """
        if not user_emails:
            return 0
        
        try:
            pipe = self.redis_client.pipeline()
            
            # Check TTL for all OTP keys
            otp_keys = [RedisKeys.otp_key(email) for email in user_emails]
            for key in otp_keys:
                pipe.ttl(key)
            
            ttl_results = pipe.execute()
            
            # Identify expired keys
            expired_keys = [
                otp_keys[i] for i, ttl in enumerate(ttl_results) 
                if ttl <= 0 and ttl != -2  # -2 means key doesn't exist
            ]
            
            if expired_keys:
                # Delete expired keys in batch
                pipe = self.redis_client.pipeline()
                for key in expired_keys:
                    pipe.delete(key)
                pipe.execute()
                
                APP_CONFIG.logger.info(f"Cleaned up {len(expired_keys)} expired OTPs")
                return len(expired_keys)
            
            return 0
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Batch OTP cleanup failed: {e}")
            return 0


def get_optimized_otp_manager(redis_client) -> OptimizedOTPManager:
    """
    Get optimized OTP manager instance.
    
    Args:
        redis_client: Redis client instance
        
    Returns:
        OptimizedOTPManager instance
    """
    return OptimizedOTPManager(redis_client)


def check_existing_otp_status_optimized(redis_client, user_email: str) -> Tuple[bool, int, str]:
    """
    Optimized version of check_existing_otp_status using pipeline operations.
    
    Args:
        redis_client: Redis client instance
        user_email: User email address
        
    Returns:
        (has_valid_otp, remaining_seconds, message)
    """
    otp_manager = get_optimized_otp_manager(redis_client)
    return otp_manager.check_existing_otp_status_pipeline(user_email)


def create_otp_optimized(redis_client, user_email: str) -> str:
    """
    Optimized OTP creation using pipeline operations.
    
    Args:
        redis_client: Redis client instance
        user_email: User email address
        otp_length: Length of OTP to generate
        
    Returns:
        Generated OTP string
    """
    otp_manager = get_optimized_otp_manager(redis_client)
    return otp_manager.create_otp_optimized(user_email)
