"""
Custom Bloom Filter implementation for CreatorVerse microservice.
Compatible with AWS ElastiCache and standard Redis operations.
"""

import hashlib
import math
import threading
from typing import TYPE_CHECKING, Any, cast

import redis

from app.core.redis_keys import RedisKeys

if TYPE_CHECKING:
    from database_helper.database.sync_db2 import SyncDatabaseDB


class CreatorBloomFilter:
    """
    Custom Bloom Filter implementation using standard Redis operations.
    """

    def __init__(
            self,
            redis_client: redis.Redis,
            filter_name: str,
            capacity: int = 10000,
            error_rate: float = 0.01,
    ) -> None:
        """
        Initialize Custom Bloom Filter.

        Args:
            redis_client: Redis client instance
            filter_name: Name of the bloom filter (must follow CreatorVerse:feature:key pattern)
            capacity: Expected number of elements
            error_rate: Desired false positive probability (default: 0.01 = 1%)
        """
        self.redis_client = redis_client
        self.filter_name = filter_name
        self.capacity = capacity
        self.error_rate = error_rate

        # Calculate optimal parameters
        self.bit_array_size = self._calculate_bit_array_size(capacity, error_rate)
        self.hash_count = self._calculate_hash_count(self.bit_array_size, capacity)

        # Redis key for storing filter metadata
        self.metadata_key = f"{filter_name}:metadata"
        self.bitmap_key = f"{filter_name}:bitmap"

        # Initialize filter if it doesn't exist
        self._initialize_filter()

    def _calculate_bit_array_size(self, capacity: int, error_rate: float) -> int:
        """
        Calculate optimal bit array size.

        Args:
            capacity: Expected number of elements
            error_rate: Desired false positive probability

        Returns:
            int: Optimal bit array size
        """
        return int(-capacity * math.log(error_rate) / (math.log(2) ** 2))

    def _calculate_hash_count(self, bit_array_size: int, capacity: int) -> int:
        """
        Calculate optimal number of hash functions.

        Args:
            bit_array_size: Size of bit array
            capacity: Expected number of elements

        Returns:
            int: Optimal number of hash functions
        """
        return int((bit_array_size / capacity) * math.log(2))

    def _initialize_filter(self) -> None:
        """Initialize the bloom filter if it doesn't exist."""
        if not self.redis_client.exists(self.metadata_key):
            # Store filter metadata
            metadata: dict[str, str | int] = {
                "capacity": self.capacity,
                "error_rate": str(self.error_rate),
                "bit_array_size": self.bit_array_size,
                "hash_count": self.hash_count,
                "element_count": 0,
            }
            self.redis_client.hset(self.metadata_key, mapping=metadata)

    def _hash_functions(self, item: str) -> list[int]:
        """
        Generate multiple hash values for an item.

        Args:
            item: Item to hash

        Returns:
            List[int]: List of hash values
        """
        hashes: list[int] = []

        # Use different hash algorithms for diversity
        hash1 = int(hashlib.md5(item.encode()).hexdigest(), 16)
        hash2 = int(hashlib.sha1(item.encode()).hexdigest(), 16)

        for i in range(self.hash_count):
            # Double hashing technique
            hash_val = (hash1 + i * hash2) % self.bit_array_size
            hashes.append(hash_val)

        return hashes

    def add(self, item: str) -> bool:
        """
        Add an item to the bloom filter.

        Args:
            item: Item to add

        Returns:
            bool: True if item was added (new), False if it might already exist
        """
        hash_values = self._hash_functions(item)

        # Check if item might already exist
        was_new = True
        for hash_val in hash_values:
            if self.redis_client.getbit(self.bitmap_key, hash_val):
                was_new = False
                break

        # Set all bits
        pipeline = self.redis_client.pipeline()
        for hash_val in hash_values:
            pipeline.setbit(self.bitmap_key, hash_val, 1)
        pipeline.execute()

        # Increment element count if it's a new item
        if was_new:
            self.redis_client.hincrby(self.metadata_key, "element_count", 1)

        return was_new

    def not_exists(self, item: str) -> bool:
        """
        Check if an item definitely does NOT exist in the bloom filter.

        Returns:
            True if the item is guaranteed NOT to be in the filter (i.e. at least one bit is 0).
            False if it might be in the filter.
        """
        for h in self._hash_functions(item):
            # as soon as we see a 0‐bit, we know "item was never added"
            if self.redis_client.getbit(self.bitmap_key, h) == 0:
                return True
        # all bits were 1 → item might exist
        return False

    def exists(self, item: str) -> bool:
        """
        Check if an item might exist in the bloom filter.

        Args:
            item: Item to check

        Returns:
            bool: True if item might exist, False if it definitely doesn't exist
        """
        hash_values = self._hash_functions(item)

        # Check all bits - if any is 0, item definitely doesn't exist
        pipeline = self.redis_client.pipeline()
        for hash_val in hash_values:
            pipeline.getbit(self.bitmap_key, hash_val)

        results = pipeline.execute()
        return all(results)

    def get_stats(self) -> dict[str, Any]:
        """
        Get bloom filter statistics.

        Returns:
            Dict[str, Any]: Filter statistics
        """
        metadata_raw = cast(
            dict[bytes, bytes], self.redis_client.hgetall(self.metadata_key)
        )
        if not metadata_raw:
            return {}

        # Convert bytes keys to strings and handle type conversion
        metadata: dict[str, str] = {}
        for key, value in metadata_raw.items():
            key_str = key.decode() if isinstance(key, bytes) else str(key)
            value_str = value.decode() if isinstance(value, bytes) else str(value)
            metadata[key_str] = value_str

        element_count = int(metadata.get("element_count", "0"))
        capacity = int(metadata.get("capacity", str(self.capacity)))

        # Calculate current false positive probability
        if element_count > 0:
            current_fpp = (
                                  1 - math.exp(-self.hash_count * element_count / self.bit_array_size)
                          ) ** self.hash_count
        else:
            current_fpp = 0.0

        return {
            "capacity": capacity,
            "element_count": element_count,
            "bit_array_size": int(
                metadata.get("bit_array_size", str(self.bit_array_size))
            ),
            "hash_count": int(metadata.get("hash_count", str(self.hash_count))),
            "current_false_positive_probability": current_fpp,
            "load_factor": element_count / capacity if capacity > 0 else 0.0,
        }

    def clear(self) -> bool:
        """
        Clear the bloom filter.

        Returns:
            bool: True if cleared successfully
        """
        try:
            pipeline = self.redis_client.pipeline()
            pipeline.delete(self.bitmap_key)
            pipeline.delete(self.metadata_key)
            pipeline.execute()

            # Reinitialize
            self._initialize_filter()
            return True
        except Exception:
            return False


class CreatorBloomFilterManager:
    """
    Manager class for CreatorVerse Bloom Filters following the CreatorVerse:feature:key pattern.
    """

    _instances: dict[str, "CreatorBloomFilterManager"] = {}
    _lock = threading.Lock()

    def __init__(self, redis_client: redis.Redis) -> None:
        """
        Initialize Bloom Filter Manager.

        Args:
            redis_client: Redis client instance
        """
        self.redis_client = redis_client
        self._email_filter: CreatorBloomFilter | None = None
        self._filter_lock = threading.Lock()

    def get_email_filter(
            self, capacity: int = 100000, error_rate: float = 0.001
    ) -> CreatorBloomFilter:
        """
        Get email bloom filter following CreatorVerse pattern.

        Args:
            capacity: Expected number of emails (default: 100,000)
            error_rate: Desired false positive rate (default: 0.1%)

        Returns:
            CreatorBloomFilter: Email bloom filter instance
        """
        if self._email_filter is None:
            with self._filter_lock:
                if self._email_filter is None:
                    self._email_filter = CreatorBloomFilter(
                        self.redis_client,
                        RedisKeys.get_email_bloom_filter_key(),
                        capacity=capacity,
                        error_rate=error_rate,
                    )
        return self._email_filter

    def check_email_exists(self, email: str) -> bool:
        """
        Check if email exists using bloom filter.

        Args:
            email: Email address to check

        Returns:
            bool: True if email might exist, False if it definitely doesn't exist
        """
        email_filter = self.get_email_filter()
        return email_filter.exists(email.lower())

    def check_email_not_exists(self, email: str) -> bool:
        """
        Check if email definitely does NOT exist in the bloom filter.

        Args:
            email: Email address to check

        Returns:
            bool: True if email is guaranteed NOT to be in the filter, False if it might be in the filter
        """
        email_filter = self.get_email_filter()
        return email_filter.not_exists(email.lower())

    def add_email(self, email: str) -> bool:
        """
        Add email to bloom filter.

        Args:
            email: Email address to add

        Returns:
            bool: True if email was newly added, False if it might already exist
        """
        email_filter = self.get_email_filter()
        return email_filter.add(email.lower())

    def clear_email_filter(self) -> bool:
        """
        Clear the email bloom filter.

        Returns:
            bool: True if cleared successfully
        """
        email_filter = self.get_email_filter()
        success = email_filter.clear()
        if success:
            with self._filter_lock:
                self._email_filter = None
        return success

    def get_email_filter_stats(self) -> dict[str, Any]:
        """
        Get email filter statistics.

        Returns:
            Dict[str, Any]: Email filter statistics
        """
        email_filter = self.get_email_filter()
        return email_filter.get_stats()

    def populate_email_filter_from_database(
            self, db_conn: "SyncDatabaseDB"
    ) -> dict[str, Any]:
        """
        Populate email bloom filter from database if it doesn't exist.

        Args:
            db_conn: Database connection instance

        Returns:
            Dict with population statistics
        """
        from database_helper.database.models import User
        from sqlalchemy import select

        # Check if email filter already exists and has data
        email_filter = self.get_email_filter()
        stats = email_filter.get_stats()

        if stats.get("element_count", 0) > 0:
            return {
                "status": "already_populated",
                "element_count": stats["element_count"],
                "emails_added": 0,
            }

        # Get all emails from database
        try:
            with db_conn.transaction() as session:
                # Query all active and verified users' emails
                email_query = (
                    select(User.email)
                    .where(
                        User.is_active.is_(True),
                        User.is_email_verified.is_(True),
                        User.email.is_not(None),
                    )
                    .distinct()
                )

                result = session.execute(email_query)
                emails = result.scalars().all()

                emails_added = 0
                for email in emails:
                    if email and email.strip():
                        email_filter.add(email.lower().strip())
                        emails_added += 1

                return {
                    "status": "populated",
                    "element_count": emails_added,
                    "emails_added": emails_added,
                }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "element_count": 0,
                "emails_added": 0,
            }


# Global singleton instance storage
_bloom_filter_manager_instance: CreatorBloomFilterManager | None = None
_manager_lock = threading.Lock()


def get_bloom_filter_manager(redis_client: redis.Redis) -> CreatorBloomFilterManager:
    """
    Factory function to create or return existing CreatorBloomFilterManager singleton instance.

    Args:
        redis_client: Redis client instance

    Returns:
        CreatorBloomFilterManager: Singleton bloom filter manager instance
    """
    global _bloom_filter_manager_instance

    if _bloom_filter_manager_instance is None:
        with _manager_lock:
            if _bloom_filter_manager_instance is None:
                _bloom_filter_manager_instance = CreatorBloomFilterManager(redis_client)

    return _bloom_filter_manager_instance
