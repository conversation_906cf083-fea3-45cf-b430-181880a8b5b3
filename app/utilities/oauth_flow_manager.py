"""
Enhanced OAuth flow implementation with support for proper role_uuid handling.
Ensures consistent role management for both brand and influencer flows.
"""
from urllib.parse import urlencode

from app.core.config import APP_CONFIG
from app.core.security import generate_csrf_state
from app.utilities.oauth_role_manager import OAuthRoleManager
from app.utilities.oauth_role_manager import get_oauth_role_manager
from app.utilities.oauth_state_manager import OAuthStateManager
from app.utilities.oauth_state_manager import get_oauth_state_manager


# Factory function to get an OAuthFlowManager instance
def get_oauth_flow_manager(redis_client) -> "OAuthFlowManager":
    """Get an OAuthFlowManager instance with initialized dependencies"""

    # Initialize required dependencies
    state_manager = get_oauth_state_manager(redis_client)
    role_manager = get_oauth_role_manager(redis_client)

    return OAuthFlowManager(redis_client, state_manager, role_manager)


class OAuthFlowManager:
    """
    Handles the enhanced OAuth flow with proper role_uuid support.
    
    This class wraps the existing OAuth functionality to ensure that
    role_uuid is properly managed throughout the authentication process.
    """

    def __init__(self, redis_client, state_manager: OAuthStateManager, role_manager: OAuthRoleManager):
        """Initialize with required dependencies"""
        self.redis_client = redis_client
        self.state_manager = state_manager
        self.role_manager = role_manager

    async def initiate_oauth(self, provider: str, user_role: str) -> tuple[str, str]:
        """
        Initiate OAuth flow for a specific provider.
        
        Args:
            provider: OAuth provider (google, instagram)
            user_role: User type (influencer, brand) uuid of it
        Returns:
            Tuple of (auth_url, state)
        """

        # Generate CSRF state token
        state = generate_csrf_state()

        role_uuid = user_role
        # Store state with all necessary data
        self.state_manager.store_state(
            state=state,
            provider=provider.lower(),
            user_type=user_role,
            role_uuid=role_uuid,
            extra_data={},
            expiry_seconds=300  # 5 minutes
        )

        # Generate provider-specific URL
        if provider.lower() == "google":
            params = {
                "client_id": APP_CONFIG.oauth_google_client_id,
                "redirect_uri": APP_CONFIG.oauth_google_redirect_uri,
                "response_type": "code",
                "scope": f"openid email profile {APP_CONFIG.google_scope_yt}",
                "state": state,
                "access_type": "offline",
                "prompt": "select_account consent"
            }
            auth_url = f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"
        elif provider.lower() == "facebook":
            params = {
                "client_id": APP_CONFIG.oauth_instagram_client_id,
                "redirect_uri": APP_CONFIG.oauth_instagram_redirect_uri,
                "response_type": "code",
                "scope": "public_profile,email,pages_show_list,pages_read_engagement,instagram_basic,instagram_manage_comments,instagram_manage_insights,instagram_content_publish,pages_manage_metadata,instagram_manage_insights",
                "state": state,
                "return_scopes": True,
                "enable_profile_selector": True
            }
            auth_url = f"https://www.facebook.com/v23.0/dialog/oauth?{urlencode(params)}"
        else:
            raise ValueError(f"Unsupported provider: {provider}")
        print(auth_url)
        return auth_url, state

    async def handle_callback(self, code: str, state: str, redirect_uri: str) -> tuple[str, str, str, str]:
        """
        Handle OAuth callback and extract provider, user_type, and role_uuid.
        
        Args:
            code: Authorization code from provider
            state: CSRF state token
            redirect_uri: Callback URL
            
        Returns:
            Tuple of (provider, user_type, role_uuid, redirect_uri)
        """
        # Retrieve state data
        provider, user_type, role_uuid, extra_data = await self.state_manager.pop_state(state)

        if not provider or not user_type:
            raise ValueError("Invalid or expired OAuth state")

        # Log retrieved data
        APP_CONFIG.logger.info(
            f"Retrieved OAuth state: provider={provider}, user_type={user_type}, "
            f"role_uuid={role_uuid or 'None'}"
        )

        # Use stored redirect_uri if available, otherwise use provided one
        callback_redirect_uri = extra_data.get("redirect_uri", redirect_uri)

        # If role_uuid wasn't stored or is invalid, try to resolve it now
        if not role_uuid:
            from app.core.config import get_database
            db_conn = get_database()
            role_uuid = await self.role_manager.get_role_uuid_for_user_type(user_type, db_conn)

        return provider, user_type, role_uuid, callback_redirect_uri
