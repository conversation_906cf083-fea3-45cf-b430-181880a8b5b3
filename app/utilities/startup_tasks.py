import time
import redis
from database_helper.database.models import User
from database_helper.database.sync_db2 import SyncDatabaseDB
from sqlalchemy import select

from app.core.config import APP_CONFIG
from app.utilities.bloom_filter import get_bloom_filter_manager
from app.utilities.rbac_service import get_rbac_service


def load_rbac_data_sync(redis_client: redis.Redis, db_conn: SyncDatabaseDB) -> None:
    """Synchronous task to load RBAC data on application startup."""
    try:
        APP_CONFIG.logger.info("Starting RBAC data loading...")
        # Test Redis connection first
        try:
            redis_client.ping()
        except redis.ConnectionError:
            APP_CONFIG.logger.warning("Redis is not available - RBAC will use database fallback")
            return

        rbac_service = get_rbac_service(redis_client, db_conn)
        rbac_service.load_rbac_data()
        APP_CONFIG.logger.info("RBAC data loading completed successfully")
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to load RBAC data on startup: {e}")


def initialize_bloom_filters_sync(
        redis_client: redis.Redis, db_conn: SyncDatabaseDB
) -> None:
    """Synchronous task to initialize Bloom filters from database on application startup."""
    try:
        APP_CONFIG.logger.info("Starting Bloom filter initialization...")
        # Test Redis connection first
        try:
            redis_client.ping()
        except redis.ConnectionError:
            APP_CONFIG.logger.warning("Redis is not available - Bloom filters will use database fallback")
            return

        bloom_filter_manager = get_bloom_filter_manager(redis_client)

        # Initialize email filter
        email_result = bloom_filter_manager.populate_email_filter_from_database(db_conn)
        if email_result["status"] == "populated":
            APP_CONFIG.logger.info(
                f"Email Bloom filter populated with {email_result['emails_added']} emails"
            )
        elif email_result["status"] == "already_populated":
            APP_CONFIG.logger.info(
                f"Email Bloom filter already populated with {email_result['element_count']} elements"
            )
        elif email_result["status"] == "error":
            APP_CONFIG.logger.error(
                f"Failed to populate email Bloom filter: {email_result['error']}"
            )

        APP_CONFIG.logger.info("Bloom filter initialization completed successfully")

    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to initialize Bloom filters on startup: {e}")


def warm_up_email_validation_cache_sync(redis_client, db_conn: SyncDatabaseDB) -> None:
    """
    Warm up email validation cache with common domains on startup.
    This reduces latency for common email providers.
    """
    try:
        APP_CONFIG.logger.info("Starting email validation cache warm-up...")
        
        # Test Redis connection first
        try:
            redis_client.ping()
        except redis.ConnectionError:
            APP_CONFIG.logger.warning("Redis is not available - email validation cache warm-up skipped")
            return
        
        # Common email domains to pre-cache
        common_domains = [
            "gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "icloud.com",
            "protonmail.com", "aol.com", "live.com", "msn.com", "yandex.com",
            "mail.ru", "qq.com", "163.com", "126.com", "sina.com"
        ]
        
        # Also get domains from existing users in database
        try:
            with db_conn.transaction() as session:
                # Get unique domains from user emails
                user_emails = session.execute(
                    select(User.email).where(User.email.isnot(None)).distinct()
                ).scalars().all()
                
                user_domains = set()
                for email in user_emails:
                    if email and "@" in email:
                        domain = email.split("@")[1].lower()
                        user_domains.add(domain)
                
                # Add user domains to common domains
                common_domains.extend(list(user_domains)[:50])  # Limit to top 50 user domains
                
                APP_CONFIG.logger.info(f"Found {len(user_domains)} unique user domains for cache warm-up")
                
        except Exception as e:
            APP_CONFIG.logger.warning(f"Could not fetch user domains for cache warm-up: {e}")
          # Pre-cache MX records for common domains
        from app.utilities.validation_functions import _validate_domain_mx_with_cache, RESERVED_DOMAINS, BLOCKED_TEST_DOMAINS
        
        # Filter out reserved domains
        blocked_domains = RESERVED_DOMAINS | BLOCKED_TEST_DOMAINS
        valid_domains = [domain for domain in set(common_domains) if domain.lower() not in blocked_domains]
        
        cached_count = 0
        for domain in valid_domains:  # Only cache valid, non-reserved domains
            try:
                # This will cache the MX validation result
                _validate_domain_mx_with_cache(domain, redis_client, 2.0)
                cached_count += 1
                
                # Small delay to avoid overwhelming DNS servers
                if cached_count % 10 == 0:
                    time.sleep(0.1)
                    
            except Exception as e:
                APP_CONFIG.logger.debug(f"Failed to cache domain {domain}: {e}")
        
        APP_CONFIG.logger.info(f"Email validation cache warm-up completed. Cached {cached_count} valid domains (excluded {len(set(common_domains)) - len(valid_domains)} reserved/test domains)")
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to warm up email validation cache: {e}")


def optimize_redis_pipeline_operations_sync(redis_client) -> None:
    """
    Optimize Redis operations by pre-allocating pipeline resources.
    """
    try:
        APP_CONFIG.logger.info("Starting Redis pipeline optimization...")
        
        # Test Redis connection and pipeline functionality
        try:
            redis_client.ping()
            
            # Test pipeline operations
            pipe = redis_client.pipeline()
            pipe.set("CreatorVerse:startup:test", "pipeline_test", ex=60)
            pipe.get("CreatorVerse:startup:test")
            pipe.delete("CreatorVerse:startup:test")
            results = pipe.execute()
            
            if results[0]:  # SET operation successful
                APP_CONFIG.logger.info("Redis pipeline operations verified successfully")
            
        except redis.ConnectionError:
            APP_CONFIG.logger.warning("Redis is not available - pipeline optimization skipped")
            return
            
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to optimize Redis pipeline operations: {e}")


def validate_email_validation_config_sync() -> None:
    """
    Validate email validation configuration on startup.
    """
    try:
        APP_CONFIG.logger.info("Validating email validation configuration...")
        
        # Log current configuration
        APP_CONFIG.logger.info(f"Environment: {APP_CONFIG.environ}")
        APP_CONFIG.logger.info(f"Email validation enabled: {APP_CONFIG.email_validation_enabled}")
        APP_CONFIG.logger.info(f"SMTP validation enabled: {APP_CONFIG.email_smtp_validation_enabled}")
        APP_CONFIG.logger.info(f"Validation timeout: {APP_CONFIG.email_validation_timeout}s")
        APP_CONFIG.logger.info(f"Domain cache TTL: {APP_CONFIG.email_domain_cache_ttl}s")
        APP_CONFIG.logger.info(f"Validation environments: {APP_CONFIG.email_validation_environments}")
        
        # Validate configuration values
        if APP_CONFIG.email_validation_timeout <= 0:
            APP_CONFIG.logger.warning("Email validation timeout should be greater than 0")
        
        if APP_CONFIG.email_domain_cache_ttl <= 0:
            APP_CONFIG.logger.warning("Email domain cache TTL should be greater than 0")
        
        APP_CONFIG.logger.info("Email validation configuration validated")
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to validate email validation configuration: {e}")


def initialize_email_optimization_startup_tasks_sync(redis_client, db_conn: SyncDatabaseDB) -> None:
    """
    Initialize all email optimization startup tasks.
    """
    try:
        APP_CONFIG.logger.info("Starting email optimization startup tasks...")
        
        # Validate configuration
        validate_email_validation_config_sync()
        
        # Optimize Redis operations
        optimize_redis_pipeline_operations_sync(redis_client)
        
        # Warm up email validation cache
        warm_up_email_validation_cache_sync(redis_client, db_conn)
        
        APP_CONFIG.logger.info("Email optimization startup tasks completed successfully")
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Failed to initialize email optimization startup tasks: {e}")


def initialize_all_startup_tasks_sync(redis_client, db_conn: SyncDatabaseDB) -> None:
    """Initialize all startup tasks synchronously."""
    try:
        # Load RBAC data
        load_rbac_data_sync(redis_client, db_conn)

        # Initialize Bloom filters
        initialize_bloom_filters_sync(redis_client, db_conn)

        # Initialize email optimization tasks
        initialize_email_optimization_startup_tasks_sync(redis_client, db_conn)

    except Exception as e:
        print(f"Failed to initialize startup tasks: {e}")
