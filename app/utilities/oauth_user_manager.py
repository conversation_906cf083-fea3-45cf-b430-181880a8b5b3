from datetime import UTC, datetime
from typing import Any, Dict

from database_helper.database.models import Organization, DomainClaim
from database_helper.database.models import User, OAuthAccount, SocialProfile
from database_helper.database.sync_db2 import SyncDatabaseDB

from app.api.deps import BLOCKED_CONSUMER_DOMAINS
from app.core.config import APP_CONFIG
from app.core.database_helper.users_helper import create_user_only, create_user_oauth
from app.core.database_helper.users_helper import create_user_role
from app.core.enums_data import SourceRegister, UserType
from app.core.redis_keys import RedisKeys, RedisConfig
from app.utilities.jwt_oauth_utilities import issue_tokens_for_user_with_uuid_role
from app.utilities.oauth_handlers import get_oauth_handler
from app.utilities.rbac_service import get_rbac_service


class OAuthUserManager:
    """Manages OAuth user creation and updates"""

    def __init__(self, redis_client):
        self.redis_client = redis_client

    async def create_or_update_user_from_oauth(
            self,
            user_info: Any,
            provider: int,
            user_type: str,
            access_token: str,
            refresh_token: str | None = None,
            db_conn: SyncDatabaseDB | None = None,
            provider_user_id: str | None = None,
            scope: str | None = None,
            expires_at: datetime | None = None,
    ) -> Dict[str, Any]:
        """Create or update user from OAuth data"""
        if db_conn is None:
            raise ValueError("Database connection is required")

        user_email = user_info.email.lower().strip()

        # Get RBAC service
        rbac_service = get_rbac_service(self.redis_client, db_conn)

        # Convert string user_type to UserType enum
        try:
            user_type_enum = UserType[user_type.upper()]
        except (KeyError, AttributeError):
            raise Exception(f"Unsupported user type: {user_type}")

        # Get role UUID
        role_uuid = await self._get_role_uuid_for_user_type(user_type, db_conn)
        if not role_uuid:
            raise Exception(f"Role not found for user type: {user_type}")

        # Determine provider string and auth method
        provider_str, auth_method_key = self._get_provider_info(provider)

        try:
            # Get auth method ID
            auth_method_id = await self._get_auth_method_id_cache_aside(auth_method_key, db_conn)
            if not auth_method_id:
                raise Exception(f"Auth method not found: {auth_method_key}")

            with db_conn.transaction() as session:
                # Check if user exists
                user = await self._get_or_create_user(
                    session, user_email, user_info, provider, user_type_enum,
                    auth_method_key, auth_method_id, role_uuid
                )

                # Create or update OAuth account
                oauth_account = await self._create_oauth_account_entry(
                    session, str(user.id), provider_str,
                    provider_user_id or getattr(user_info, 'sub', user_info.id),
                    access_token, refresh_token, expires_at, scope
                )

                # Create social profiles
                await self._create_social_profiles(
                    session, oauth_account, provider_str, user_info,
                    access_token, refresh_token, expires_at
                )

                # Update user cache
                self._update_user_cache(user, user_email)

                # Issue tokens
                token_response, _ = issue_tokens_for_user_with_uuid_role(
                    str(user.id), role_uuid,
                    redis_client=self.redis_client,
                    session=session,
                    ip_address=None,
                    user_agent=None
                )

                return {
                    "access_token": token_response.access_token,
                    "refresh_token": token_response.refresh_token,
                    "token_type": token_response.token_type,
                    "expires_in": token_response.expires_in,
                    "user_id": str(user.id),
                    "is_new_user": getattr(user, '_is_new_user', False)
                }

        except Exception as e:
            APP_CONFIG.logger.error(f"Error creating/updating user from OAuth: {str(e)}")
            raise Exception(f"Failed to create or update user: {str(e)}")

    async def _get_role_uuid_for_user_type(self, user_type: str, db_conn: SyncDatabaseDB) -> str | None:
        """Get role UUID for user type"""
        roles_cache = self.redis_client.hgetall(RedisKeys.rbac_roles())

        # Try cache first
        for uuid, name in roles_cache.items():
            if name.lower() == user_type.lower():
                return uuid

        # Fallback to database
        with db_conn.transaction() as session:
            from database_helper.database.models import MasterRole
            role = session.query(MasterRole).filter(
                MasterRole.role_name.ilike(f"%{user_type}%")
            ).first()
            return str(role.id) if role else None

    def _get_provider_info(self, provider: int) -> tuple[str, str]:
        """Get provider string and auth method key"""
        if provider == SourceRegister.GOOGLE_OAUTH.value:
            return "google", "oauth_google"
        elif provider == SourceRegister.INSTAGRAM_OAUTH.value:
            return "instagram", "oauth_instagram"
        else:
            raise Exception(f"Unsupported OAuth provider: {provider}")

    async def _get_or_create_user(
            self, session, user_email: str, user_info: Any, provider: int,
            user_type_enum: UserType, auth_method_key: str, auth_method_id: str, role_uuid: str
    ) -> User:
        """Get existing user or create new one"""

        # Check cache first
        redis_key = RedisKeys.user_by_email(user_email)
        cached_user = self.redis_client.hgetall(redis_key)

        user = None
        if cached_user:
            user = session.query(User).filter_by(email=user_email).first()
        else:
            user = session.query(User).filter_by(email=user_email).first()

        if not user:
            # Create new user
            user_data = {
                "name": user_info.name or user_email.split('@')[0],
                "profile_image": getattr(user_info, 'picture', '') or "",
                "is_email_verified": True,
                "is_active": True,
                "status": "active",
                "register_source": provider,
            }

            user = await create_user_only(session, user_email, user_data, self.redis_client)
            user._is_new_user = True

            # Store user ID immediately to avoid session boundary issues
            user_id_str = str(user.id)

            # Create auth method and role - use stored user ID
            await create_user_oauth(session, user_id_str, self.redis_client,
                                    register_channel=auth_method_key, auth_method_id=auth_method_id)
            await create_user_role(session, user_id_str, role_uuid)

            # Handle brand-admin role for first user from domain
            if user_type_enum == UserType.BRAND:
                await self._handle_brand_admin_role(session, user, user_email)

            APP_CONFIG.logger.info(f"New user created via OAuth: {user_email}")
        else:
            # Update existing user
            user.name = user_info.name or user.name
            user.profile_image = getattr(user_info, 'picture', '') or user.profile_image
            user.is_email_verified = True
            user.is_active = True
            user.status = "active"
            session.add(user)
            user._is_new_user = False

            APP_CONFIG.logger.info(f"Existing user updated via OAuth: {user_email}")

        return user

    async def _handle_brand_admin_role(self, session, user: User, user_email: str) -> None:
        """Handle brand-admin role assignment for first user from domain"""

        domain = user_email.split('@')[-1].lower()

        if domain not in BLOCKED_CONSUMER_DOMAINS:
            existing_org = session.query(Organization).filter_by(domain=domain).first()
            existing_claim = session.query(DomainClaim).filter_by(domain=domain).first()

            if not existing_org and not existing_claim:
                # First user from domain, assign brand-admin role and create domain claim
                roles_cache = self.redis_client.hgetall(RedisKeys.rbac_roles())
                brand_admin_role = None

                for uuid, name in roles_cache.items():
                    # Look for brand-related roles (brand_user, org_owner, etc.)
                    if name.lower() in ["brand_user", "org_owner", "brand", "brand_admin", "brand-admin"]:
                        brand_admin_role = uuid
                        APP_CONFIG.logger.info(f"Found brand role '{name}' with UUID {uuid} for first user from domain {domain}")
                        break

                if brand_admin_role:
                    await create_user_role(session, str(user.id), brand_admin_role)

                    # Create domain claim for brand-admin user
                    from app.core.security import generate_verification_token
                    domain_claim = DomainClaim(
                        domain=domain,
                        verification_token=generate_verification_token(),
                        claimed_by_user_id=user.id,
                        created_at=datetime.now(UTC),
                        verified_at=None
                    )
                    session.add(domain_claim)

                    APP_CONFIG.logger.info(
                        f"Assigned brand-admin role and created domain claim for first user from domain {domain}")
            else:
                # Domain already has organization or claim, assign regular brand role
                APP_CONFIG.logger.info(f"Domain {domain} already has organization/claim, user gets regular brand role")

    async def _create_oauth_account_entry(
            self, session, user_id: str, provider: str, provider_user_id: str,
            access_token: str, refresh_token: str | None, expires_at: datetime | None, scope: str | None
    ) -> OAuthAccount:
        """Create or update OAuth account entry"""
        # Use user_id as string directly since it's passed as string
        existing_account = session.query(OAuthAccount).filter_by(
            user_id=user_id,
            provider=provider,
            provider_user_id=provider_user_id
        ).first()

        if existing_account:
            existing_account.access_token = access_token
            existing_account.refresh_token = refresh_token
            existing_account.expires_at = expires_at
            existing_account.scope = scope
            existing_account.updated_at = datetime.now(UTC)
            session.add(existing_account)
            return existing_account
        else:
            oauth_account = OAuthAccount(
                user_id=user_id,
                provider=provider,
                provider_user_id=provider_user_id,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=expires_at,
                scope=scope,
                created_at=datetime.now(UTC),
                updated_at=datetime.now(UTC),
            )
            session.add(oauth_account)
            return oauth_account

    async def _create_social_profiles(
            self, session, oauth_account: OAuthAccount, provider_str: str,
            user_info: Any, access_token: str, refresh_token: str | None, expires_at: datetime | None
    ) -> None:
        """Create social profiles based on provider"""
        # Get user from oauth_account to check if new user
        user = session.query(User).filter_by(id=oauth_account.user_id).first()
        if not user:
            APP_CONFIG.logger.warning(f"User not found for oauth_account_id {oauth_account.id}")
            return

        # Check if this is a newly created user or an existing one
        is_new_user = getattr(user, '_is_new_user', False)

        # Only fetch YouTube profile for new users to prevent profile enrichment for existing accounts
        if provider_str == "google" and is_new_user:
            try:
                handler = get_oauth_handler("google")
                yt_json = await handler.fetch_youtube_profile(access_token, refresh_token, expires_at)

                snippet = yt_json.get("snippet", {})
                stats = yt_json.get("statistics", {})
                thumbnails = snippet.get("thumbnails", {})
                avatar_url = thumbnails.get("default", {}).get("url") if thumbnails else None

                self._upsert_social_profile(
                    session=session,
                    oauth_account_id=str(oauth_account.id),
                    service="youtube",
                    external_id=yt_json["id"],
                    username=None,
                    display_name=snippet.get("title"),
                    avatar_url=avatar_url,
                    follower_count=int(stats.get("subscriberCount", 0)),
                    post_count=int(stats.get("videoCount", 0)),
                    raw_json=yt_json,
                )
                APP_CONFIG.logger.info(f"YouTube profile created for new influencer with ID: {user.id}")

            except Exception as e:
                APP_CONFIG.logger.warning(f"YouTube profile fetch failed: {e}")
        elif provider_str == "google" and not is_new_user:
            APP_CONFIG.logger.info(f"Skipping YouTube profile enrichment for existing user: {user.email}")

        elif provider_str == "instagram" and is_new_user:
            self._upsert_social_profile(
                session=session,
                oauth_account_id=str(oauth_account.id),
                service="instagram",
                external_id=getattr(user_info, 'id', ''),
                username=getattr(user_info, 'username', None),
                display_name=getattr(user_info, 'username', None),
                avatar_url=None,
                follower_count=None,
                post_count=None,
                raw_json=getattr(user_info, 'model_dump', lambda: {})(),
            )
            APP_CONFIG.logger.info(f"Instagram profile created for new influencer with ID: {user.id}")
        elif provider_str == "instagram" and not is_new_user:
            APP_CONFIG.logger.info(f"Skipping Instagram profile enrichment for existing user: {user.email}")

    def _upsert_social_profile(self, **kwargs) -> SocialProfile:
        """Create or update social profile entry"""
        session = kwargs.pop('session')
        oauth_account_id = kwargs.pop('oauth_account_id')
        service = kwargs.pop('service')

        profile = session.query(SocialProfile).filter_by(
            oauth_account_id=oauth_account_id,
            service=service
        ).first()

        if profile is None:
            profile = SocialProfile(
                oauth_account_id=oauth_account_id,
                service=service,
                external_id=kwargs.get('external_id')
            )
            session.add(profile)

        # Update profile data
        for key, value in kwargs.items():
            if hasattr(profile, key):
                setattr(profile, key, value)

        profile.fetched_at = datetime.now(UTC)
        return profile

    def _update_user_cache(self, user: User, user_email: str) -> None:
        """Update user cache"""
        redis_key = RedisKeys.user_by_email(user_email)
        user_dict = {
            "id": str(user.id),
            "email": user.email,
            "name": user.name or "",
            "phone_number": user.phone_number or "",
            "status": user.status or "",
            "is_active": str(user.is_active),
            "is_email_verified": str(user.is_email_verified),
        }

        self.redis_client.hset(redis_key, mapping=user_dict)
        self.redis_client.expire(redis_key, RedisConfig.USER_TTL)

    async def _get_auth_method_id_cache_aside(self, method_key: str, db_conn: SyncDatabaseDB) -> str | None:
        """Get auth method ID using cache-aside pattern"""
        auth_methods_key = "CreatorVerse:auth:methods"
        cached_auth_method_id = self.redis_client.hget(auth_methods_key, method_key)

        if cached_auth_method_id:
            return cached_auth_method_id

        try:
            with db_conn.transaction() as session:
                from database_helper.database.models import MasterAuthMethod
                auth_methods = session.query(MasterAuthMethod).all()

                if not auth_methods:
                    return None

                auth_methods_dict = {method.method_key: str(method.id) for method in auth_methods}

                if auth_methods_dict:
                    pipe = self.redis_client.pipeline()
                    pipe.hmset(auth_methods_key, auth_methods_dict)
                    pipe.expire(auth_methods_key, RedisConfig.RBAC_TTL)
                    pipe.execute()

                return auth_methods_dict.get(method_key)

        except Exception as e:
            APP_CONFIG.logger.error(f"Error fetching auth method from database: {str(e)}")
            return None
