from datetime import UTC, datetime
from typing import Dict, Any

from database_helper.database.models import User, Organization, DomainClaim, UserRoleModel

from app.api.deps import BLOCKED_CONSUMER_DOMAINS
from app.core.config import APP_CONFIG
from app.core.database_helper.users_helper import create_user_role
from app.core.enums_data import UserType
from app.core.security import generate_verification_token


class OAuthBrandService:
    """Handles brand-specific OAuth operations separate from general OAuth flow"""

    def __init__(self, redis_client):
        self.redis_client = redis_client

    async def handle_brand_user_creation(
            self,
            session,
            user: User,
            user_email: str,
            user_type_enum: UserType,
            roles_cache: Dict[str, str]
    ) -> None:
        """
        Handle brand-specific user creation logic including domain claims and role assignment.
        This is separated from the main OAuth flow to prevent duplicate role assignments.
        """
        if user_type_enum != UserType.BRAND:
            return

        domain = user_email.split('@')[-1].lower()

        if domain in BLOCKED_CONSUMER_DOMAINS:
            APP_CONFIG.logger.warning(f"Blocked consumer domain attempted brand registration: {domain}")
            return

        # Check if domain already has organization or claim
        existing_org = session.query(Organization).filter_by(domain=domain).first()
        existing_claim = session.query(DomainClaim).filter_by(domain=domain).first()

        if not existing_org and not existing_claim:
            # First user from domain - create domain claim and assign brand-admin role
            await self._handle_first_brand_user_from_domain(
                session, user, domain, roles_cache
            )
        else:
            # Domain already has organization or claim
            if existing_org:
                APP_CONFIG.logger.info(f"Domain {domain} already has organization, user gets regular brand role")
            else:
                APP_CONFIG.logger.info(f"Domain {domain} already claimed, user gets regular brand role")

    async def _handle_first_brand_user_from_domain(
            self,
            session,
            user: User,
            domain: str,
            roles_cache: Dict[str, str]
    ) -> None:
        """Handle first brand user from domain - create organization, membership and domain claim"""

        # Find brand-admin role (look for brand-related roles)
        brand_admin_role_uuid = None
        for uuid, name in roles_cache.items():
            # Look for brand-related roles (brand_user, org_owner, etc.)
            if name.lower() in ["brand_user", "org_owner", "brand", "brand_admin"]:
                brand_admin_role_uuid = uuid
                APP_CONFIG.logger.info(f"Found brand role '{name}' with UUID {uuid} for first user from domain {domain}")
                break

        if not brand_admin_role_uuid:
            APP_CONFIG.logger.error(f"Brand-admin role not found in cache. Available roles: {list(roles_cache.values())}")
            return

        # Check if user already has brand-admin role to prevent duplicates
        existing_brand_role = session.query(UserRoleModel).filter_by(
            user_id=user.id,
            role_id=brand_admin_role_uuid
        ).first()

        if not existing_brand_role:
            # Assign brand-admin role only if not already assigned
            await create_user_role(session, str(user.id), brand_admin_role_uuid)
            APP_CONFIG.logger.info(f"Assigned brand-admin role to first user from domain {domain}")
        else:
            APP_CONFIG.logger.info(f"User already has brand-admin role for domain {domain}")

        # Create organization, membership and domain claim for first user
        await self._create_organization_for_first_user(session, user, domain)

    async def _create_organization_for_first_user(self, session, user, domain: str) -> None:
        """
        Create organization for first user from domain within existing session.
        Simplified version without domain claims or memberships.
        """
        try:
            from app.core.database_helper.brand_organization_helper import generate_organization_code
            
            # Generate organization details
            org_name = domain.split('.')[0].capitalize()
            org_code = generate_organization_code(domain)
            
            # Create organization within existing session
            organization = Organization(
                domain=domain,
                organization_code=org_code,
                name=f"{org_name} Organization",
                description=f"Organization for {domain}",
                is_active=True,
                total_members=1
            )
            
            session.add(organization)
            session.flush()  # Get the organization ID
            
            # Invalidate relevant caches
            cache_keys_to_invalidate = [
                f"CreatorVerse:organization:domain:{domain}",
                f"CreatorVerse:domain:orgs:{domain}",
                f"CreatorVerse:user:organizations:{user.id}"
            ]
            
            for cache_key in cache_keys_to_invalidate:
                try:
                    self.redis_client.delete(cache_key)
                except Exception as cache_error:
                    APP_CONFIG.logger.warning(f"Failed to invalidate cache key {cache_key}: {str(cache_error)}")
            
            APP_CONFIG.logger.info(f"Created organization {organization.id} for first OAuth user from domain {domain}")
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to create organization for OAuth user from domain {domain}: {str(e)}")
            raise

    def _invalidate_domain_caches(self, domain: str, organization_id: str) -> None:
        """Invalidate domain and organization related caches"""
        try:
            cache_keys = [
                f"CreatorVerse:domain:orgs:{domain}",
                f"CreatorVerse:org:members:{organization_id}",
                f"CreatorVerse:domain:status:{domain}"
            ]

            for cache_key in cache_keys:
                self.redis_client.delete(cache_key)

            APP_CONFIG.logger.debug(f"Invalidated caches for domain {domain}")

        except Exception as e:
            APP_CONFIG.logger.warning(f"Failed to invalidate caches for domain {domain}: {e}")

    async def _create_domain_claim(self, session, user: User, domain: str) -> None:
        """Create domain claim for verification (legacy method - now handled in _create_organization_for_first_user)"""
        # This method is now primarily used for non-first users who need to claim domains
        # First users get auto-verified claims through _create_organization_for_first_user
        try:
            # Check if domain claim already exists
            existing_claim = session.query(DomainClaim).filter_by(domain=domain).first()

            if existing_claim:
                APP_CONFIG.logger.info(f"Domain claim already exists for {domain}")
                return

            # Create new domain claim (unverified for non-first users)
            domain_claim = DomainClaim(
                domain=domain,
                verification_token=generate_verification_token(),
                claimed_by_user_id=user.id,
                verified_at=None  # Requires verification for non-first users
            )
            session.add(domain_claim)

            # Invalidate domain cache
            domain_cache_key = f"CreatorVerse:domain:orgs:{domain}"
            try:
                self.redis_client.delete(domain_cache_key)
            except Exception as e:
                APP_CONFIG.logger.warning(f"Failed to invalidate domain cache: {e}")

            APP_CONFIG.logger.info(f"Created domain claim for brand user from domain {domain}")

        except Exception as e:
            APP_CONFIG.logger.error(f"Error creating domain claim for {domain}: {str(e)}")
            # Don't fail the entire flow for domain claim creation errors

    async def check_brand_domain_eligibility(self, user_email: str) -> Dict[str, Any]:
        """
        Check if a brand domain is eligible for registration.
        Returns status information about domain eligibility.
        """

        domain = user_email.split('@')[-1].lower()

        if domain in BLOCKED_CONSUMER_DOMAINS:
            return {
                "eligible": False,
                "reason": "consumer_domain_blocked",
                "message": f"Consumer email domains are not allowed for brand registration: {domain}"
            }

        return {
            "eligible": True,
            "domain": domain,
            "message": "Domain is eligible for brand registration"
        }


def get_oauth_brand_service(redis_client) -> OAuthBrandService:
    """Factory function to create OAuthBrandService instance"""
    return OAuthBrandService(redis_client)
