"""
Utility functions to help migrate from HTTPException to centralized exception handling.
This provides backwards compatibility while encouraging use of new centralized exceptions.
"""

from typing import Optional, Any, Dict
from fastapi import HTTPException, status

from app.core.exceptions import (
    EmailValidationError,
    EmailAlreadyExistsError, 
    EmailNotFoundError,
    OTPError,
    BusinessLogicError,
    ServiceUnavailableError,
    CreatorVerseError
)


def convert_http_exception_to_centralized(exc: HTTPException) -> CreatorVerseError:
    """
    Convert HTTPException to appropriate centralized exception.
    This helps with gradual migration from HTTPException to centralized exceptions.
    """
    detail = str(exc.detail)
    status_code = exc.status_code
    
    # Email validation errors
    if "email" in detail.lower() and "does not exist" in detail.lower():
        return EmailValidationError("The email address does not exist. Please check for typos and try again.")
    
    if "email" in detail.lower() and "already exists" in detail.lower():
        # Extract email if possible
        words = detail.split()
        email = next((word for word in words if "@" in word), "unknown")
        return EmailAlreadyExistsError(email)
    
    if "email" in detail.lower() and "not found" in detail.lower():
        words = detail.split()
        email = next((word for word in words if "@" in word), "unknown")
        return EmailNotFoundError(email)
    
    # OTP errors
    if "otp" in detail.lower():
        if "expired" in detail.lower():
            return OTPError("OTP has expired. Please request a new one.")
        elif "invalid" in detail.lower():
            return OTPError("Invalid OTP. Please check and try again.")
        elif "not found" in detail.lower():
            return OTPError("OTP not found or has expired. Please request a new one.")
        else:
            return OTPError("OTP verification failed. Please try again.")
    
    # Service unavailable errors
    if status_code == status.HTTP_503_SERVICE_UNAVAILABLE:
        service = "Unknown"
        if "redis" in detail.lower():
            service = "Redis"
        elif "database" in detail.lower():
            service = "Database"
        return ServiceUnavailableError(service, detail)
    
    # Business logic errors (400 Bad Request)
    if status_code == status.HTTP_400_BAD_REQUEST:
        return BusinessLogicError(detail)
    
    # Default to CreatorVerseError for other cases
    return CreatorVerseError(detail, status_code)


def raise_for_email_validation(detail: str) -> None:
    """
    Convenience function to raise appropriate email validation error.
    """
    if "does not exist" in detail.lower() or "5.1.1" in detail:
        raise EmailValidationError("The email address does not exist. Please check for typos and try again.")
    elif "blocked" in detail.lower() or "5.7.1" in detail:
        raise EmailValidationError("Email delivery blocked. Please try a different email address.")
    elif "timeout" in detail.lower():
        raise EmailValidationError("Email verification timed out. Please try again.")
    else:
        raise EmailValidationError("Email verification failed. Please check the email address and try again.")


def raise_for_business_logic(detail: str, status_code: int = status.HTTP_400_BAD_REQUEST) -> None:
    """
    Convenience function to raise business logic error.
    """
    raise BusinessLogicError(detail)


def raise_for_service_unavailable(service: str, detail: Optional[str] = None) -> None:
    """
    Convenience function to raise service unavailable error.
    """
    raise ServiceUnavailableError(service, detail)


# Migration helpers - these provide backwards compatibility
def safe_raise_http_exception(status_code: int, detail: str) -> None:
    """
    Safely raise an exception using centralized system instead of HTTPException.
    This function helps with gradual migration.
    """
    # Create a temporary HTTPException to use conversion logic
    temp_exc = HTTPException(status_code=status_code, detail=detail)
    centralized_exc = convert_http_exception_to_centralized(temp_exc)
    raise centralized_exc


def get_error_response_for_status_code(status_code: int, message: str) -> Dict[str, Any]:
    """
    Get consistent error response format for any status code.
    """
    from app.utilities.response_handler import create_error_response
    
    if status_code >= 500:
        return create_error_response("An unexpected error occurred. Please try again later.")
    elif status_code == 404:
        return create_error_response("The requested resource was not found.")
    elif status_code == 409:
        return create_error_response(message or "A conflict occurred with the current state.")
    elif status_code == 429:
        return create_error_response("Too many requests. Please try again later.")
    else:
        return create_error_response(message or "An error occurred. Please try again.")
