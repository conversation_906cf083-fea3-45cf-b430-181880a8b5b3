from typing import List, Dict, Any, Optional
from datetime import datetime, UTC
import asyncio

from app.core.config import APP_CONFIG
from app.utilities.oauth_handlers import YOUTUBE_AVAILABLE, GOOGLE_SCOPE_YT
from app.core.redis_keys import RedisKeys, RedisConfig

# YouTube API imports
try:
    from google.oauth2.credentials import Credentials
    from googleapiclient.discovery import build
except ImportError:
    pass


class YouTubeService:
    """Service for YouTube operations including channel listing and selection"""

    def __init__(self, redis_client=None):
        """Initialize YouTube service with Redis client for caching"""
        self.redis_client = redis_client
        if not YOUTUBE_AVAILABLE:
            raise ImportError("YouTube API client not available. Install google-api-python-client for YouTube integration.")

    async def list_user_channels(
        self, 
        access_token: str,
        refresh_token: str,
        expires_at: datetime
    ) -> List[Dict[str, Any]]:
        """List all YouTube channels accessible to the authenticated user
        
        Args:
            access_token: OAuth access token
            refresh_token: OAuth refresh token
            expires_at: Token expiry timestamp
            
        Returns:
            List of channel data dictionaries
        """
        try:
            # Run YouTube API calls in thread pool
            loop = asyncio.get_running_loop()
            return await loop.run_in_executor(
                None, 
                self._list_channels_sync,
                access_token, 
                refresh_token, 
                expires_at
            )
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to list YouTube channels: {str(e)}")
            raise

    def _list_channels_sync(
        self,
        access_token: str,
        refresh_token: str,
        expires_at: datetime
    ) -> List[Dict[str, Any]]:
        """Synchronous implementation of YouTube channel listing
        
        This method runs in a separate thread via run_in_executor
        """
        try:
            yt = self._build_youtube_client(access_token, refresh_token, expires_at)
            
            # Get all channels the user has access to
            response = yt.channels().list(
                part="snippet,statistics,status",
                mine=True,
                maxResults=50
            ).execute()

            channels = []
            for item in response.get("items", []):
                snippet = item.get("snippet", {})
                statistics = item.get("statistics", {})
                status = item.get("status", {})
                
                # Extract thumbnail URL - prefer standard resolution if available
                thumbnails = snippet.get("thumbnails", {})
                thumbnail_url = None
                for size in ["standard", "high", "medium", "default"]:
                    if size in thumbnails:
                        thumbnail_url = thumbnails[size].get("url")
                        break

                # Create channel object with sanitized data
                channel = {
                    "channel_id": item["id"],
                    "title": snippet.get("title", "Untitled Channel"),
                    "description": snippet.get("description", ""),
                    "thumbnail_url": thumbnail_url,
                    "subscriber_count": int(statistics.get("subscriberCount", 0)),
                    "video_count": int(statistics.get("videoCount", 0)),
                    "view_count": int(statistics.get("viewCount", 0)),
                    "is_verified": status.get("isLinked", False)
                }
                channels.append(channel)
            
            return channels

        except Exception as e:
            APP_CONFIG.logger.error(f"YouTube API error in _list_channels_sync: {str(e)}")
            raise

    async def get_channel_by_id(
        self, 
        channel_id: str,
        access_token: str,
        refresh_token: str,
        expires_at: datetime
    ) -> Optional[Dict[str, Any]]:
        """Retrieve a specific YouTube channel by ID
        
        Args:
            channel_id: YouTube channel ID
            access_token: OAuth access token
            refresh_token: OAuth refresh token
            expires_at: Token expiry timestamp
            
        Returns:
            Channel data dictionary or None if not found
        """
        try:
            # Run YouTube API calls in thread pool
            loop = asyncio.get_running_loop()
            return await loop.run_in_executor(
                None, 
                self._get_channel_by_id_sync,
                channel_id,
                access_token, 
                refresh_token, 
                expires_at
            )
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to get YouTube channel {channel_id}: {str(e)}")
            raise

    def _get_channel_by_id_sync(
        self,
        channel_id: str,
        access_token: str,
        refresh_token: str,
        expires_at: datetime
    ) -> Optional[Dict[str, Any]]:
        """Synchronous implementation of YouTube channel retrieval
        
        This method runs in a separate thread via run_in_executor
        """
        try:
            yt = self._build_youtube_client(access_token, refresh_token, expires_at)
            
            # Get specific channel by ID
            response = yt.channels().list(
                part="snippet,statistics,status",
                id=channel_id
            ).execute()

            items = response.get("items", [])
            if not items:
                return None
                
            item = items[0]
            snippet = item.get("snippet", {})
            statistics = item.get("statistics", {})
            status = item.get("status", {})
            
            # Extract thumbnail URL - prefer standard resolution if available
            thumbnails = snippet.get("thumbnails", {})
            thumbnail_url = None
            for size in ["standard", "high", "medium", "default"]:
                if size in thumbnails:
                    thumbnail_url = thumbnails[size].get("url")
                    break

            # Create channel object with sanitized data
            return {
                "channel_id": item["id"],
                "title": snippet.get("title", "Untitled Channel"),
                "description": snippet.get("description", ""),
                "thumbnail_url": thumbnail_url,
                "subscriber_count": int(statistics.get("subscriberCount", 0)),
                "video_count": int(statistics.get("videoCount", 0)),
                "view_count": int(statistics.get("viewCount", 0)),
                "is_verified": status.get("isLinked", False)
            }

        except Exception as e:
            APP_CONFIG.logger.error(f"YouTube API error in _get_channel_by_id_sync: {str(e)}")
            raise

    def _build_youtube_client(self, access_token: str, refresh_token: str, expires_at: datetime):
        """Build authenticated YouTube API client
        
        Args:
            access_token: OAuth access token
            refresh_token: OAuth refresh token
            expires_at: Token expiry timestamp
            
        Returns:
            YouTube API client instance
        """
        # Ensure timezone awareness for expiry datetime
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=UTC)

        # Create credentials from tokens
        creds = Credentials(
            token=access_token,
            refresh_token=refresh_token,
            token_uri="https://oauth2.googleapis.com/token",
            client_id=APP_CONFIG.oauth_google_client_id,
            client_secret=APP_CONFIG.oauth_google_client_secret,
            scopes=[GOOGLE_SCOPE_YT],
            expiry=expires_at
        )
        
        # Build YouTube API client with credentials
        return build("youtube", "v3", credentials=creds, cache_discovery=False)


def get_youtube_service(redis_client=None):
    """Factory function to get YouTube service instance"""
    return YouTubeService(redis_client)
