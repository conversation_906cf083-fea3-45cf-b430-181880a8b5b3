"""
Centralized OTP Service for CreatorVerse User Backend
Handles all OTP operations including generation, storage, verification, and email sending
"""
import asyncio
import time
from typing import Dict, List, Optional, Tuple, Any, Union
from enum import Enum

from app.core.config import APP_CONFIG, DEFAULT_OTP_LENGTH, get_locobuzz_redis, get_database
from app.core.redis_keys import RedisKeys, RedisConfig
from app.core.security import hash_otp
from app.core.enums_data import SourceRegister
from app.utilities.validation_functions import generate_otp, verify_otp
from app.utilities.email_service import EmailService
from app.utilities.optimized_otp_manager import get_optimized_otp_manager
from app.core.database_helper.users_helper import create_otp_entry


class OTPType(Enum):
    """OTP request types"""
    REGISTRATION = "registration"
    LOGIN = "login"
    BRAND_REGISTRATION = "brand_registration"
    PASSWORD_RESET = "password_reset"


class OTPChannel(Enum):
    """OTP delivery channels"""
    EMAIL = "email"
    SMS = "sms"


class CentralizedOTPService:
    """
    Centralized service for all OTP operations following cache-aside pattern
    Implements 30-second resend cooldown for better user experience
    """
    
    # Constants for OTP timing
    RESEND_COOLDOWN_SECONDS = 30  # 30 seconds cooldown for resend
    
    def __init__(self, redis_client=None, db_conn=None):
        self.redis_client = redis_client or get_locobuzz_redis()
        self.db_conn = db_conn or get_database()
        self.otp_manager = get_optimized_otp_manager(self.redis_client)
        # Initialize email service - get singleton instance
        self.email_service = self._get_email_service()
    
    def _get_email_service(self) -> EmailService:
        """Get email service singleton instance"""
        from app.utilities.email_service import get_email_service
        return get_email_service()
    
    async def request_otp(
        self,
        user_email: str,
        otp_type: OTPType,
        role_uuid: str,
        channel: OTPChannel = OTPChannel.EMAIL,
        register_source: str = "web",
        register_source_name: str = "website",
        force_resend: bool = False,
        **additional_data
    ) -> Dict[str, Any]:
        """
        Central method to request OTP for any purpose with 30-second resend cooldown
        
        Args:
            user_email: User's email address
            otp_type: Type of OTP request (registration, login, etc.)
            role_uuid: UUID of the user role
            channel: Delivery channel (email, sms)
            register_source: Source identifier for registration
            register_source_name: Human readable source name
            force_resend: If True, allows resend after 30 seconds cooldown
            **additional_data: Additional context data
            
        Returns:
            Dict containing success status and message
        """
        try:
            user_email = user_email.strip().lower()
            
            # 1. Check existing OTP status and resend cooldown
            otp_status = self._check_otp_resend_status(user_email, force_resend)
            
            if not otp_status["can_send"]:
                return {
                    "success": False,
                    "message": otp_status["message"],
                    "email": user_email,
                    "retry_after": otp_status["retry_after"],
                    "can_resend": otp_status["can_resend"]
                }
            
            # 2. Generate and store OTP
            otp_data = await self._generate_and_store_otp(
                user_email=user_email,
                otp_type=otp_type,
                role_uuid=role_uuid,
                channel=channel,
                register_source=register_source,
                register_source_name=register_source_name,
                **additional_data
            )
            
            # 3. Send OTP via specified channel
            user_role = self._determine_user_role(role_uuid)
            await self._send_otp(
                user_email=user_email,
                otp=otp_data["otp"],
                otp_type=otp_type,
                channel=channel,
                user_role=user_role
            )
            
            # 4. Store in database if needed
            if otp_type in [OTPType.REGISTRATION, OTPType.LOGIN]:
                await self._store_otp_in_database(
                    user_email=user_email,
                    otp_hash=otp_data["otp_hash"],
                    otp_type=otp_type,
                    register_source=register_source,
                    register_source_name=register_source_name,
                    **additional_data
                )
            
            APP_CONFIG.logger.info(f"OTP sent successfully for {user_email} - Type: {otp_type.value}")
            
            return {
                "success": True,
                "message": f"{otp_type.value.title()} OTP sent. Please verify your {channel.value} with the OTP sent.",
                "email": user_email
            }
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to send OTP for {user_email}: {str(e)}")
            # Cleanup on failure
            self._cleanup_otp(user_email)
            raise
    
    async def verify_otp(
        self,
        user_email: str,
        otp: str,
        cleanup: bool = True
    ) -> Dict[str, Any]:
        """
        Central method to verify OTP
        
        Args:
            user_email: User's email address
            otp: OTP to verify
            cleanup: Whether to delete OTP after successful verification
            
        Returns:
            Dict containing verification data
        """
        try:
            user_email = user_email.strip().lower()
            
            # Use existing verification logic
            otp_data = verify_otp(self.redis_client, user_email, otp)
            
            # Cleanup OTP if requested
            if cleanup:
                self._cleanup_otp(user_email)
            
            APP_CONFIG.logger.info(f"OTP verified successfully for {user_email}")
            return otp_data
            
        except Exception as e:
            APP_CONFIG.logger.error(f"OTP verification failed for {user_email}: {str(e)}")
            raise
    
    async def resend_otp(
        self,
        user_email: str,
        otp_type: OTPType,
        role_uuid: str,
        channel: OTPChannel = OTPChannel.EMAIL,
        register_source: str = "web",
        register_source_name: str = "website",
        **additional_data
    ) -> Dict[str, Any]:
        """
        Resend OTP with proper cooldown validation
        
        Args:
            user_email: User's email address
            otp_type: Type of OTP request (registration, login, etc.)
            role_uuid: UUID of the user role
            channel: Delivery channel (email, sms)
            register_source: Source identifier for registration
            register_source_name: Human readable source name
            **additional_data: Additional context data
            
        Returns:
            Dict containing success status and message
        """
        try:
            user_email = user_email.strip().lower()
            
            # Check if resend is allowed (30-second cooldown)
            otp_status = self._check_otp_resend_status(user_email, force_resend=True)
            
            if not otp_status["can_send"]:
                return {
                    "success": False,
                    "message": otp_status["message"],
                    "email": user_email,
                    "retry_after": otp_status["retry_after"]
                }
            
            # Use the main request_otp method with force_resend=True
            return await self.request_otp(
                user_email=user_email,
                otp_type=otp_type,
                role_uuid=role_uuid,
                channel=channel,
                register_source=register_source,
                register_source_name=register_source_name,
                force_resend=True,
                **additional_data
            )
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to resend OTP for {user_email}: {str(e)}")
            return {
                "success": False,
                "message": "Failed to resend OTP. Please try again later.",
                "email": user_email,
                "retry_after": 0
            }
    
    def _check_otp_status(self, user_email: str) -> Tuple[bool, int, str]:
        """Check if user has a valid OTP using optimized manager"""
        return self.otp_manager.check_existing_otp_status_pipeline(user_email)
    
    def _check_otp_resend_status(self, user_email: str, force_resend: bool = False) -> Dict[str, Any]:
        """
        Check OTP status with 30-second resend cooldown logic
        
        Args:
            user_email: User's email address
            force_resend: If True, allows resend after 30 seconds cooldown
            
        Returns:
            Dict with can_send, message, retry_after, and can_resend flags
        """
        has_valid_otp, remaining_seconds, message = self._check_otp_status(user_email)
        
        if not has_valid_otp:
            # No existing OTP, can send immediately
            return {
                "can_send": True,
                "message": "Ready to send OTP",
                "retry_after": 0,
                "can_resend": False
            }
        
        # OTP exists, check if 30 seconds have passed for resend
        otp_key = RedisKeys.otp_key(user_email)
        try:
            otp_data = self.redis_client.hgetall(otp_key)
            if not otp_data:
                # OTP data missing, can send
                return {
                    "can_send": True,
                    "message": "Ready to send OTP",
                    "retry_after": 0,
                    "can_resend": False
                }
            
            generated_at = int(otp_data.get("generated_at", 0))
            current_time = int(time.time())
            time_elapsed = current_time - generated_at
            
            # 30-second cooldown for resend
            resend_cooldown = 30
            can_resend_after = max(0, resend_cooldown - time_elapsed)
            
            if force_resend and time_elapsed >= resend_cooldown:
                # Allow resend after 30 seconds
                return {
                    "can_send": True,
                    "message": "Resending OTP",
                    "retry_after": 0,
                    "can_resend": True
                }
            elif time_elapsed < resend_cooldown:
                # Within 30-second cooldown
                return {
                    "can_send": False,
                    "message": f"OTP already sent. You can request a new OTP in {can_resend_after} seconds.",
                    "retry_after": can_resend_after,
                    "can_resend": False
                }
            else:
                # After 30 seconds, allow automatic resend
                return {
                    "can_send": True,
                    "message": "Ready to resend OTP",
                    "retry_after": 0,
                    "can_resend": True
                }
                
        except Exception as e:
            APP_CONFIG.logger.error(f"Error checking OTP resend status for {user_email}: {str(e)}")
            # On error, allow sending
            return {
                "can_send": True,
                "message": "Ready to send OTP",
                "retry_after": 0,
                "can_resend": False
            }
    
    async def _generate_and_store_otp(
        self,
        user_email: str,
        otp_type: OTPType,
        role_uuid: str,
        channel: OTPChannel,
        register_source: str,
        register_source_name: str,
        **additional_data
    ) -> Dict[str, Any]:
        """Generate OTP and store in Redis with all required metadata"""
        
        otp = generate_otp(DEFAULT_OTP_LENGTH)
        otp_hash = hash_otp(otp)
        otp_key = RedisKeys.otp_key(user_email)
        
        # Convert string register_source to numeric enum value for consistency
        numeric_register_source = SourceRegister.get_enum_value(register_source)
        
        # Prepare OTP data with comprehensive metadata
        otp_data = {
            "email": user_email,
            "otp_hash": otp_hash,
            "generated_at": str(int(time.time())),
            "failed_attempts": "0",
            "lockout_until": "0",
            "role": role_uuid,
            "otp_type": otp_type.value,
            "channel": channel.value,
            "request_type": otp_type.value,
            "register_channel": str(numeric_register_source),
            "source_register": register_source_name,
            "source_in_user": str(numeric_register_source)
        }
        
        # Add any additional metadata
        for key, value in additional_data.items():
            if key not in otp_data and value is not None:
                otp_data[key] = str(value)
        
        try:
            # Use pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            pipe.hset(otp_key, mapping=otp_data)
            pipe.expire(otp_key, RedisConfig.OTP_TTL)
            pipe.execute()
            
            return {
                "otp": otp,
                "otp_hash": otp_hash,
                "otp_data": otp_data
            }
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to store OTP in Redis for {user_email}: {e}")
            raise
    
    async def _send_otp(
        self,
        user_email: str,
        otp: str,
        otp_type: OTPType,
        channel: OTPChannel,
        user_role: str,
        retry_count: int = 3
    ) -> None:
        """Send OTP via specified channel with retry logic"""
        
        if channel == OTPChannel.EMAIL:
            await self._send_email_otp(user_email, otp, otp_type, user_role, retry_count)
        elif channel == OTPChannel.SMS:
            await self._send_sms_otp(user_email, otp, otp_type, retry_count)
        else:
            raise ValueError(f"Unsupported OTP channel: {channel}")
    
    async def _send_email_otp(
        self,
        user_email: str,
        otp: str,
        otp_type: OTPType,
        user_role: str,
        retry_count: int
    ) -> None:
        """Send OTP via email with retry logic"""
        
        retry_delay = 1.0
        
        for attempt in range(retry_count):
            try:
                success = False
                
                # Choose the right email method based on OTP type
                if otp_type == OTPType.REGISTRATION:
                    success = await self.email_service.send_registration_otp_email(
                        user_email, otp, user_role
                    )
                elif otp_type == OTPType.BRAND_REGISTRATION:
                    success = await self.email_service.send_brand_registration_otp_email(
                        user_email, otp
                    )
                elif otp_type == OTPType.LOGIN:
                    success = await self.email_service.send_login_otp_email(
                        user_email, otp, user_role
                    )
                elif otp_type == OTPType.PASSWORD_RESET:
                    success = await self.email_service.send_password_reset_otp_email(
                        user_email, otp, user_role
                    )
                else:
                    # For other types, use login template as default
                    APP_CONFIG.logger.warning(f"Unknown OTP type {otp_type.value}. Falling back to login template for {user_email}")
                    success = await self.email_service.send_login_otp_email(
                        user_email, otp, user_role
                    )
                
                if success:
                    APP_CONFIG.logger.info(f"Email OTP sent successfully to {user_email} on attempt {attempt + 1} - Type: {otp_type.value}")
                    return
                else:
                    APP_CONFIG.logger.warning(f"Email OTP send attempt {attempt + 1} failed for {user_email} - Type: {otp_type.value}")
                    
                # If this wasn't the last attempt, wait before retry
                if not success and attempt < retry_count - 1:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
            except Exception as e:
                APP_CONFIG.logger.error(f"Email OTP send attempt {attempt + 1} failed for {user_email}: {str(e)}")
                
                if attempt == retry_count - 1:  # Last attempt
                    # Cleanup OTP on final failure
                    self._cleanup_otp(user_email)
                    raise
                
                # Wait before retry
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
    
    async def _send_sms_otp(
        self,
        phone_number: str,
        otp: str,
        otp_type: OTPType,
        retry_count: int
    ) -> None:
        """Send OTP via SMS - placeholder for future implementation"""
        # TODO: Implement SMS sending logic
        APP_CONFIG.logger.info(f"SMS OTP sending not implemented yet for {phone_number}")
        pass
    
    async def _store_otp_in_database(
        self,
        user_email: str,
        otp_hash: str,
        otp_type: OTPType,
        register_source: str,
        register_source_name: str,
        **additional_data
    ) -> None:
        """Store OTP in database if required"""
        
        try:
            # Convert string register_source to numeric enum value for database storage
            numeric_register_source = SourceRegister.get_enum_value(register_source)
            
            await create_otp_entry(
                user_email=user_email,
                otp_hash=otp_hash,
                db_conn=self.db_conn,
                request_type=otp_type.value,
                register_channel=numeric_register_source,
                source_register=register_source_name,
                **additional_data
            )
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to store OTP in database for {user_email}: {str(e)}")
            # Don't raise here - Redis storage is primary, DB is secondary
    
    def _cleanup_otp(self, user_email: str) -> None:
        """Clean up OTP from Redis"""
        try:
            otp_key = RedisKeys.otp_key(user_email)
            self.redis_client.delete(otp_key)
            APP_CONFIG.logger.debug(f"OTP cleaned up for {user_email}")
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to cleanup OTP for {user_email}: {str(e)}")
    
    def _determine_user_role(self, role_uuid: str) -> str:
        """Determine user role type based on role UUID"""
        try:
            # Try to get role name from Redis cache
            from app.core.redis_keys import RedisKeys
            roles_cache = self.redis_client.hgetall(RedisKeys.rbac_roles())
            role_name = roles_cache.get(role_uuid, "").lower()
            
            # If found in cache, determine role type
            if role_name:
                if 'brand' in role_name or 'admin' in role_name:
                    return "brand"
                return "influencer"
            
            # Fallback: check role UUID string for hints
            role_lower = role_uuid.lower()
            if "brand" in role_lower or "admin" in role_lower:
                return "brand"
            return "influencer"
            
        except Exception as e:
            APP_CONFIG.logger.warning(f"Failed to determine role for {role_uuid}: {e}")
            return "influencer"  # Default fallback
    
    # Batch operations for performance
    async def batch_request_otp(
        self,
        requests: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Process multiple OTP requests in batch"""
        results = []
        
        for request_data in requests:
            try:
                result = await self.request_otp(**request_data)
                results.append(result)
            except Exception as e:
                results.append({
                    "success": False,
                    "message": f"Failed to send OTP: {str(e)}",
                    "email": request_data.get("user_email", "unknown")
                })
        
        return results
    
    def batch_check_otp_status(self, user_emails: List[str]) -> Dict[str, Tuple[bool, int, str]]:
        """Check OTP status for multiple users"""
        return self.otp_manager.batch_check_otp_status(user_emails)

# Singleton instance factory
_otp_service_instance: Optional[CentralizedOTPService] = None

def get_otp_service(redis_client=None, db_conn=None) -> CentralizedOTPService:
    """Get singleton instance of OTP service"""
    global _otp_service_instance
    
    if _otp_service_instance is None:
        _otp_service_instance = CentralizedOTPService(redis_client, db_conn)
    
    return _otp_service_instance


# Convenience functions for common operations
async def send_registration_otp(
    user_email: str,
    role_uuid: str,
    register_source: str = "web",
    register_source_name: str = "website"
) -> Dict[str, Any]:
    """Send registration OTP"""
    service = get_otp_service()
    return await service.request_otp(
        user_email=user_email,
        otp_type=OTPType.REGISTRATION,
        role_uuid=role_uuid,
        register_source=register_source,
        register_source_name=register_source_name
    )


async def send_login_otp(
    user_email: str,
    role_uuid: str,
    login_source: str = "web",
    login_source_name: str = "website"
) -> Dict[str, Any]:
    """Send login OTP"""
    service = get_otp_service()
    return await service.request_otp(
        user_email=user_email,
        otp_type=OTPType.LOGIN,
        role_uuid=role_uuid,
        register_source=login_source,
        register_source_name=login_source_name
    )


async def send_brand_registration_otp(
    user_email: str,
    role_uuid: str,
    domain: str,
    register_source: str = "web"
) -> Dict[str, Any]:
    """Send brand registration OTP"""
    service = get_otp_service()
    return await service.request_otp(
        user_email=user_email,
        otp_type=OTPType.BRAND_REGISTRATION,
        role_uuid=role_uuid,
        domain=domain,
        register_source=register_source
    )


async def verify_any_otp(
    user_email: str,
    otp: str,
    cleanup: bool = True
) -> Dict[str, Any]:
    """Verify any type of OTP"""
    service = get_otp_service()
    return await service.verify_otp(user_email, otp, cleanup)


async def resend_registration_otp(
    user_email: str,
    role_uuid: str,
    register_source: str = "web",
    register_source_name: str = "website"
) -> Dict[str, Any]:
    """Resend registration OTP with 30-second cooldown"""
    service = get_otp_service()
    return await service.resend_otp(
        user_email=user_email,
        otp_type=OTPType.REGISTRATION,
        role_uuid=role_uuid,
        register_source=register_source,
        register_source_name=register_source_name
    )


async def resend_login_otp(
    user_email: str,
    role_uuid: str,
    login_source: str = "web",
    login_source_name: str = "website"
) -> Dict[str, Any]:
    """Resend login OTP with 30-second cooldown"""
    service = get_otp_service()
    return await service.resend_otp(
        user_email=user_email,
        otp_type=OTPType.LOGIN,
        role_uuid=role_uuid,
        register_source=login_source,
        register_source_name=login_source_name
    )


async def resend_brand_registration_otp(
    user_email: str,
    role_uuid: str,
    domain: str,
    register_source: str = "web"
) -> Dict[str, Any]:
    """Resend brand registration OTP with 30-second cooldown"""
    service = get_otp_service()
    return await service.resend_otp(
        user_email=user_email,
        otp_type=OTPType.BRAND_REGISTRATION,
        role_uuid=role_uuid,
        register_source=register_source,
        register_source_name=f"brand_registration_{domain}",
        domain=domain
    )


def get_otp_resend_status(user_email: str, force_resend: bool = False) -> Dict[str, Any]:
    """Get OTP resend status for a user"""
    service = get_otp_service()
    return service._check_otp_resend_status(user_email, force_resend)


# Backward compatibility functions
async def send_email_with_retry_and_cleanup_centralized(
    user_email: str,
    is_db_saved: bool,
    redis_client=None,
    **kwargs
) -> None:
    """
    Backward compatibility wrapper for send_email_with_retry_and_cleanup
    
    This function maintains the same interface as the original function
    but uses the centralized OTP service internally.
    """
    try:
        # Extract parameters from kwargs
        role_uuid = kwargs.get("role", "")
        request_type = kwargs.get("request_type", "login")
        register_source = kwargs.get("register_channel", "web")
        register_source_name = kwargs.get("source_register", "website")
        
        # Determine OTP type
        if request_type == "register":
            otp_type = OTPType.REGISTRATION
        elif request_type == "login":
            otp_type = OTPType.LOGIN
        else:
            otp_type = OTPType.LOGIN  # Default
        
        # Use centralized service
        service = get_otp_service(redis_client or get_locobuzz_redis())
        await service.request_otp(
            user_email=user_email,
            otp_type=otp_type,
            role_uuid=role_uuid,
            register_source=register_source,
            register_source_name=register_source_name,
            **kwargs
        )
        
    except Exception as e:
        APP_CONFIG.logger.error(f"Centralized OTP service failed for {user_email}: {str(e)}")
        # If centralized service fails, fall back to original implementation
        from app.core.database_helper.users_helper import send_email_with_retry_and_cleanup
        await send_email_with_retry_and_cleanup(user_email, is_db_saved, redis_client or get_locobuzz_redis(), **kwargs)
