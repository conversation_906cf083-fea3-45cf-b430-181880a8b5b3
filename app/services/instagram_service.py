from typing import List, Dict, Any, Optional
from datetime import datetime, UTC
import asyncio

from app.core.config import APP_CONFIG
from app.core.redis_keys import RedisKeys, RedisConfig

# Instagram/Facebook API imports
try:
    import httpx
    INSTAGRAM_AVAILABLE = True
except ImportError:
    INSTAGRAM_AVAILABLE = False
    APP_CONFIG.logger.warning("Instagram API client not available. Install httpx for Instagram integration.")


class InstagramService:
    """Service for Instagram operations including page listing and selection"""

    def __init__(self, redis_client=None):
        """Initialize Instagram service with Redis client for caching"""
        self.redis_client = redis_client
        if not INSTAGRAM_AVAILABLE:
            raise ImportError("Instagram API client not available. Install httpx for Instagram integration.")

    async def list_user_instagram_pages(
        self, 
        access_token: str,
        user_id: str
    ) -> List[Dict[str, Any]]:
        """List all Instagram pages accessible to the authenticated user
        
        Args:
            access_token: Facebook access token
            user_id: Facebook user ID
            
        Returns:
            List of Instagram page data dictionaries
        """
        try:
            # First get Facebook pages that are connected to Instagram
            facebook_pages = await self._get_facebook_pages_with_instagram(access_token)
            
            # Then fetch Instagram account details for each page
            instagram_pages = []
            for page in facebook_pages:
                instagram_account = await self._get_instagram_account_for_page(
                    page['id'], 
                    page['access_token']
                )
                if instagram_account:
                    # Get Instagram account metrics
                    instagram_details = await self._get_instagram_account_details(
                        instagram_account['id'],
                        page['access_token']
                    )
                    instagram_pages.append({
                        "page_id": page['id'],
                        "page_name": page['name'],
                        "instagram_id": instagram_account['id'],
                        "username": instagram_account.get('username', ''),
                        "name": instagram_details.get('name', page['name']),
                        "biography": instagram_details.get('biography', ''),
                        "profile_picture_url": instagram_details.get('profile_picture_url', ''),
                        "followers_count": instagram_details.get('followers_count', 0),
                        "media_count": instagram_details.get('media_count', 0),
                        "follows_count": instagram_details.get('follows_count', 0),
                        "account_type": instagram_details.get('account_type', 'BUSINESS'),
                        "page_access_token": page['access_token']
                    })
            
            return instagram_pages

        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to list Instagram pages: {str(e)}")
            raise

    async def _get_facebook_pages_with_instagram(self, access_token: str) -> List[Dict[str, Any]]:
        """Get Facebook pages that have Instagram accounts connected"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Get user's Facebook pages
                pages_url = "https://graph.facebook.com/v23.0/me/accounts"
                params = {
                    "access_token": access_token,
                    "fields": "id,name,access_token,instagram_business_account"
                }
                
                response = await client.get(pages_url, params=params)
                response.raise_for_status()
                pages_data = response.json()
                
                # Filter pages that have Instagram business accounts
                pages_with_instagram = []
                for page in pages_data.get('data', []):
                    if 'instagram_business_account' in page:
                        pages_with_instagram.append(page)
                
                return pages_with_instagram
                
        except Exception as e:
            APP_CONFIG.logger.error(f"Error fetching Facebook pages: {str(e)}")
            raise

    async def _get_instagram_account_for_page(
        self, 
        page_id: str, 
        page_access_token: str
    ) -> Optional[Dict[str, Any]]:
        """Get Instagram account connected to a Facebook page"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Get Instagram business account for the page
                instagram_url = f"https://graph.facebook.com/v23.0/{page_id}"
                params = {
                    "access_token": page_access_token,
                    "fields": "instagram_business_account{id,username}"
                }
                
                response = await client.get(instagram_url, params=params)
                response.raise_for_status()
                page_data = response.json()
                
                return page_data.get('instagram_business_account')
                
        except Exception as e:
            APP_CONFIG.logger.error(f"Error fetching Instagram account for page {page_id}: {str(e)}")
            return None

    async def _get_instagram_account_details(
        self, 
        instagram_account_id: str, 
        access_token: str
    ) -> Dict[str, Any]:
        """Get detailed information about an Instagram account"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # Get Instagram account details
                instagram_url = f"https://graph.facebook.com/v23.0/{instagram_account_id}"
                params = {
                    "access_token": access_token,
                    "fields": "name,biography,profile_picture_url,followers_count,media_count,follows_count,account_type"
                }
                
                response = await client.get(instagram_url, params=params)
                response.raise_for_status()
                instagram_data = response.json()
                
                return instagram_data
                
        except Exception as e:
            APP_CONFIG.logger.error(f"Error fetching Instagram account details for {instagram_account_id}: {str(e)}")
            return {}

    async def get_instagram_page_by_id(
        self, 
        instagram_id: str,
        access_token: str,
        user_id: str
    ) -> Optional[Dict[str, Any]]:
        """Retrieve a specific Instagram page by ID
        
        Args:
            instagram_id: Instagram account ID
            access_token: Facebook access token
            user_id: Facebook user ID
            
        Returns:
            Instagram page data dictionary or None if not found
        """
        try:
            # Get all pages and find the matching one
            pages = await self.list_user_instagram_pages(access_token, user_id)
            
            for page in pages:
                if page["instagram_id"] == instagram_id:
                    return page
                    
            return None
            
        except Exception as e:
            APP_CONFIG.logger.error(f"Failed to get Instagram page {instagram_id}: {str(e)}")
            raise


def get_instagram_service(redis_client=None):
    """Factory function to get Instagram service instance"""
    return InstagramService(redis_client)
