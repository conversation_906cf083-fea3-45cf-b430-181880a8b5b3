from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime
import json

from app.core.config import APP_CONFIG
from app.models.users import User, Brand, Organization, Membership
from app.schemas.brand_membership import BrandMembershipResponseSchema, MembershipRequestListSchema
from app.core.database_helper.membership_helper import MembershipHelper
from app.core.database_helper.brand_helper import BrandHelper

class BrandMembershipService:
    def __init__(self, db_conn: Session, redis_client):
        self.db_conn = db_conn
        self.redis_client = redis_client
        self.membership_helper = MembershipHelper(db_conn, redis_client)
        self.brand_helper = BrandHelper(db_conn, redis_client)

    async def create_membership_request(
        self,
        user_id: str,
        brand_id: str,
        requested_role: str,
        message: Optional[str] = None
    ) -> BrandMembershipResponseSchema:
        """Create a membership request for a brand using cache-aside pattern."""
        try:
            # Check if user already has membership or pending request
            existing_membership = await self._get_existing_membership_cache_aside(user_id, brand_id)
            if existing_membership:
                if existing_membership.status == "requested":
                    raise ValueError("Membership request already pending")
                elif existing_membership.status == "active":
                    raise ValueError("User already member of this brand")

            # Verify brand exists using direct query to avoid session issues
            brand = self.db_conn.query(Brand).filter(Brand.id == brand_id).first()
            if not brand:
                raise ValueError("Brand not found")

            # Create membership request
            membership_data = {
                "user_id": user_id,
                "brand_id": brand_id,
                "organization_id": brand.organization_id,
                "role": requested_role,
                "status": "requested",
                "requested_at": datetime.utcnow(),
                "request_message": message
            }

            membership = await self.membership_helper.create_membership(membership_data)

            # Invalidate relevant caches
            await self._invalidate_membership_caches(user_id, brand_id, brand.organization_id)

            return BrandMembershipResponseSchema(
                id=str(membership.id),
                brand_id=brand_id,
                brand_name=brand.name,
                status="requested",
                requested_role=requested_role,
                message="Membership request created successfully"
            )

        except Exception as e:
            APP_CONFIG.logger.error(f"Error creating membership request: {str(e)}")
            raise

    async def get_pending_requests(self, brand_id: str, admin_user_id: str) -> List[MembershipRequestListSchema]:
        """Get pending membership requests for a brand with admin permission check."""
        try:
            # Verify admin permissions
            has_permission = await self._check_admin_permission_cache_aside(admin_user_id, brand_id)
            if not has_permission:
                raise ValueError("Insufficient permissions to view membership requests")

            # Get pending requests directly from database to avoid cache-aside complexity
            requests = self._fetch_pending_requests_from_db(brand_id)

            return [
                MembershipRequestListSchema(
                    id=str(req.id),
                    user_id=str(req.user_id),
                    user_email=req.user.email,
                    user_name=req.user.name or req.user.email.split('@')[0],
                    requested_role=req.role,
                    request_message=req.request_message,
                    requested_at=req.requested_at
                )
                for req in requests
            ]

        except Exception as e:
            APP_CONFIG.logger.error(f"Error fetching pending requests: {str(e)}")
            raise

    async def accept_membership_request(self, request_id: str, admin_user_id: str) -> Dict[str, Any]:
        """Accept a membership request and activate the membership."""
        try:
            # Get membership request directly from DB
            membership = self.db_conn.query(Membership).filter(Membership.id == request_id).first()
            if not membership or membership.status != "requested":
                raise ValueError("Invalid or already processed membership request")

            # Verify admin permissions
            has_permission = await self._check_admin_permission_cache_aside(admin_user_id, membership.brand_id)
            if not has_permission:
                raise ValueError("Insufficient permissions to accept membership request")

            # Update membership status
            update_data = {
                "status": "active",
                "approved_at": datetime.utcnow(),
                "approved_by": admin_user_id
            }

            updated_membership = await self.membership_helper.update_membership(request_id, update_data)

            # Invalidate caches
            await self._invalidate_membership_caches(
                membership.user_id, 
                membership.brand_id, 
                membership.organization_id
            )

            return {
                "membership_id": str(updated_membership.id),
                "user_id": str(membership.user_id),
                "brand_id": str(membership.brand_id),
                "role": membership.role,
                "status": "active"
            }

        except Exception as e:
            APP_CONFIG.logger.error(f"Error accepting membership request: {str(e)}")
            raise

    async def reject_membership_request(self, request_id: str, admin_user_id: str):
        """Reject a membership request by updating status to rejected."""
        try:
            # Get membership request directly from DB
            membership = self.db_conn.query(Membership).filter(Membership.id == request_id).first()
            if not membership or membership.status != "requested":
                raise ValueError("Invalid or already processed membership request")

            # Verify admin permissions
            has_permission = await self._check_admin_permission_cache_aside(admin_user_id, membership.brand_id)
            if not has_permission:
                raise ValueError("Insufficient permissions to reject membership request")

            # Update membership status
            update_data = {
                "status": "rejected",
                "rejected_at": datetime.utcnow(),
                "rejected_by": admin_user_id
            }

            await self.membership_helper.update_membership(request_id, update_data)

            # Invalidate caches
            await self._invalidate_membership_caches(
                membership.user_id, 
                membership.brand_id, 
                membership.organization_id
            )

        except Exception as e:
            APP_CONFIG.logger.error(f"Error rejecting membership request: {str(e)}")
            raise

    async def _get_existing_membership_cache_aside(self, user_id: str, brand_id: str):
        """Get existing membership using direct database query."""
        try:
            return self.db_conn.query(Membership).filter(
                and_(
                    Membership.user_id == user_id,
                    Membership.brand_id == brand_id,
                    Membership.status.in_(["requested", "active"])
                )
            ).first()
        except Exception as e:
            APP_CONFIG.logger.error(f"Error checking existing membership: {str(e)}")
            return None

    async def _check_admin_permission_cache_aside(self, user_id: str, brand_id: str) -> bool:
        """Check if user has admin permission for brand."""
        cache_key = f"CreatorVerse:brand:admin:{user_id}:brand:{brand_id}"
        
        try:
            # Try to get from cache first
            cached_result = self.redis_client.get(cache_key)
            if cached_result is not None:
                return json.loads(cached_result)
        except Exception as e:
            APP_CONFIG.logger.warning(f"Redis cache miss for admin permission: {str(e)}")

        # Get from database
        result = self._check_admin_permission_from_db(user_id, brand_id)
        
        try:
            # Cache the result
            self.redis_client.setex(cache_key, 900, json.dumps(result))  # 15 minutes
        except Exception as e:
            APP_CONFIG.logger.warning(f"Failed to cache admin permission: {str(e)}")
        
        return result

    def _check_admin_permission_from_db(self, user_id: str, brand_id: str) -> bool:
        """Check admin permission from database."""
        try:
            # Check if user is brand admin
            brand_membership = self.db_conn.query(Membership).filter(
                and_(
                    Membership.user_id == user_id,
                    Membership.brand_id == brand_id,
                    Membership.status == "active",
                    Membership.role == "brand-admin"
                )
            ).first()

            if brand_membership:
                return True

            # Check if user is organization admin
            brand = self.db_conn.query(Brand).filter(Brand.id == brand_id).first()
            if brand:
                org_membership = self.db_conn.query(Membership).filter(
                    and_(
                        Membership.user_id == user_id,
                        Membership.organization_id == brand.organization_id,
                        Membership.status == "active",
                        Membership.role == "organization-admin"
                    )
                ).first()
                return org_membership is not None

            return False
        except Exception as e:
            APP_CONFIG.logger.error(f"Error checking admin permission from DB: {str(e)}")
            return False

    def _fetch_pending_requests_from_db(self, brand_id: str) -> List[Membership]:
        """Fetch pending membership requests from database."""
        try:
            return self.db_conn.query(Membership).join(User).filter(
                and_(
                    Membership.brand_id == brand_id,
                    Membership.status == "requested"
                )
            ).all()
        except Exception as e:
            APP_CONFIG.logger.error(f"Error fetching pending requests: {str(e)}")
            return []

    async def _invalidate_membership_caches(self, user_id: str, brand_id: str, organization_id: str):
        """Invalidate relevant membership caches."""
        cache_keys = [
            f"CreatorVerse:membership:user:{user_id}:brand:{brand_id}",
            f"CreatorVerse:brand:requests:{brand_id}",
            f"CreatorVerse:brand:admin:{user_id}:brand:{brand_id}",
            f"CreatorVerse:user:memberships:{user_id}",
            f"CreatorVerse:brand:members:{brand_id}",
            f"CreatorVerse:organization:members:{organization_id}"
        ]
        
        try:
            for key in cache_keys:
                self.redis_client.delete(key)
        except Exception as e:
            APP_CONFIG.logger.warning(f"Failed to invalidate caches: {str(e)}")
