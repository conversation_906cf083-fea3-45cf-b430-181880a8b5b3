# OAuth and Google API dependencies
google-api-python-client>=2.126.0
google-auth>=2.28.1
google-auth-oauthlib>=1.2.0

# JWT Authentication packages (both for fallback support)
python-jose[cryptography]>=3.3.0
PyJWT>=2.8.0

# FastAPI and web framework
fastapi>=0.110.0
uvicorn[standard]>=0.27.0
pydantic>=2.6.0
pydantic-settings>=2.1.0
python-multipart>=0.0.9
python-dotenv>=1.0.0
email-validator>=2.1.0

# Database and caching
redis[hiredis]>=5.0.1
psycopg2>=2.9.9
dnspython>=2.5.0

# Security and utilities
passlib[bcrypt]>=1.7.4
httpx>=0.27.0
setuptools>=69.0.0
Cython>=3.0.8

# Production dependencies
flake8>=6.1.0
autopep8>=2.0.4



# Configuration note:
# All linting configuration is moved to VS Code settings.json
