#!/usr/bin/env python3
"""
CreatorVerse Backend - Postman Collection Runner
==============================================

This script runs Postman collections programmatically to test all scenarios
without affecting the main codebase. It simulates the Postman test execution
and provides detailed reporting.

Usage: python postman_runner.py
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

import httpx


class PostmanCollectionRunner:
    """Runner for Postman collections with detailed reporting"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8002"):
        self.base_url = base_url
        self.collections_dir = Path(__file__).parent / "postman_collections"
        self.results = {
            "execution_started": datetime.now().isoformat(),
            "collections": {},
            "summary": {
                "total_collections": 0,
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0,
                "total_execution_time": 0
            }
        }
        
    async def load_collection(self, collection_file: Path) -> Dict[str, Any]:
        """Load a Postman collection from file"""
        try:
            with open(collection_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Failed to load collection {collection_file.name}: {e}")
            return {}
    
    def extract_variables(self, collection: Dict[str, Any]) -> Dict[str, str]:
        """Extract variables from collection"""
        variables = {}
        
        # Collection variables
        for var in collection.get('variable', []):
            variables[var['key']] = var.get('value', '')
        
        # Set default values for common variables
        variables.update({
            'base_url': self.base_url,
            'test_email': f'test.{int(time.time())}@example.com',
            'otp_code': '123456',  # Mock OTP for testing
            'access_token': '',
            'user_id': '',
            'organization_id': ''
        })
        
        return variables
    
    def replace_variables(self, text: str, variables: Dict[str, str]) -> str:
        """Replace {{variable}} placeholders with actual values"""
        if not isinstance(text, str):
            return text
            
        for key, value in variables.items():
            text = text.replace(f'{{{{{key}}}}}', str(value))
        
        return text
    
    async def execute_request(self, request: Dict[str, Any], variables: Dict[str, str]) -> Dict[str, Any]:
        """Execute a single request"""
        result = {
            "name": request.get('name', 'Unnamed Request'),
            "status": "pending",
            "response_time": 0,
            "status_code": None,
            "error": None,
            "tests_passed": 0,
            "tests_failed": 0
        }
        
        try:
            # Extract request details
            method = request.get('request', {}).get('method', 'GET')
            url_info = request.get('request', {}).get('url', {})
            
            # Build URL
            if isinstance(url_info, str):
                url = self.replace_variables(url_info, variables)
            else:
                raw_url = url_info.get('raw', '')
                url = self.replace_variables(raw_url, variables)
            
            # Extract headers
            headers = {}
            for header in request.get('request', {}).get('header', []):
                if isinstance(header, dict) and not header.get('disabled', False):
                    key = header.get('key', '')
                    value = self.replace_variables(header.get('value', ''), variables)
                    headers[key] = value
            
            # Extract body
            body = None
            body_data = request.get('request', {}).get('body', {})
            if body_data.get('mode') == 'raw':
                body = self.replace_variables(body_data.get('raw', ''), variables)
                if body and 'Content-Type' not in headers:
                    headers['Content-Type'] = 'application/json'
            
            # Execute request
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                if method.upper() == 'GET':
                    response = await client.get(url, headers=headers)
                elif method.upper() == 'POST':
                    response = await client.post(url, headers=headers, content=body)
                elif method.upper() == 'PUT':
                    response = await client.put(url, headers=headers, content=body)
                elif method.upper() == 'DELETE':
                    response = await client.delete(url, headers=headers)
                else:
                    raise ValueError(f"Unsupported method: {method}")
            
            result["response_time"] = time.time() - start_time
            result["status_code"] = response.status_code
            
            # Update variables with response data
            try:
                response_data = response.json()
                
                # Extract common response fields
                if 'access_token' in response_data:
                    variables['access_token'] = response_data['access_token']
                
                if 'user' in response_data and isinstance(response_data['user'], dict):
                    variables['user_id'] = str(response_data['user'].get('id', ''))
                
                if 'organization' in response_data and isinstance(response_data['organization'], dict):
                    variables['organization_id'] = str(response_data['organization'].get('id', ''))
                    
            except:
                pass  # Response might not be JSON
            
            # Simulate test execution
            if response.status_code < 400:
                result["status"] = "success"
                result["tests_passed"] = 1
            else:
                result["status"] = "failed"
                result["tests_failed"] = 1
                
        except Exception as e:
            result["status"] = "error"
            result["error"] = str(e)
            result["tests_failed"] = 1
        
        return result
    
    def extract_requests_from_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Recursively extract requests from collection items"""
        requests = []
        
        for item in items:
            if 'item' in item:  # This is a folder
                requests.extend(self.extract_requests_from_items(item['item']))
            else:  # This is a request
                requests.append(item)
        
        return requests
    
    async def run_collection(self, collection_file: Path) -> Dict[str, Any]:
        """Run a complete collection"""
        print(f"\n📦 Running collection: {collection_file.name}")
        
        collection = await self.load_collection(collection_file)
        if not collection:
            return {"status": "error", "error": "Failed to load collection"}
        
        collection_name = collection.get('info', {}).get('name', collection_file.name)
        variables = self.extract_variables(collection)
        
        # Extract all requests
        requests = self.extract_requests_from_items(collection.get('item', []))
        
        print(f"   Found {len(requests)} requests to execute")
        
        collection_result = {
            "name": collection_name,
            "file": collection_file.name,
            "total_requests": len(requests),
            "successful_requests": 0,
            "failed_requests": 0,
            "requests": []
        }
        
        # Execute requests sequentially
        for i, request in enumerate(requests, 1):
            print(f"   [{i}/{len(requests)}] {request.get('name', 'Unnamed')}", end=" ... ")
            
            result = await self.execute_request(request, variables)
            collection_result["requests"].append(result)
            
            if result["status"] == "success":
                collection_result["successful_requests"] += 1
                print(f"✅ {result['status_code']} ({result['response_time']:.2f}s)")
            else:
                collection_result["failed_requests"] += 1
                status_info = f"{result['status_code']}" if result['status_code'] else "ERROR"
                print(f"❌ {status_info} ({result['response_time']:.2f}s)")
                if result["error"]:
                    print(f"      Error: {result['error']}")
            
            # Small delay between requests
            await asyncio.sleep(0.1)
        
        return collection_result
    
    async def run_all_collections(self):
        """Run all available Postman collections"""
        print("🚀 STARTING POSTMAN COLLECTION EXECUTION")
        print("=" * 60)
        
        if not self.collections_dir.exists():
            print("❌ Postman collections directory not found")
            return
        
        collection_files = list(self.collections_dir.glob("*.json"))
        if not collection_files:
            print("❌ No Postman collections found")
            return
        
        print(f"📁 Found {len(collection_files)} collections to run")
        
        start_time = time.time()
        
        for collection_file in collection_files:
            try:
                result = await self.run_collection(collection_file)
                self.results["collections"][result["name"]] = result
                
                self.results["summary"]["total_requests"] += result["total_requests"]
                self.results["summary"]["successful_requests"] += result["successful_requests"]
                self.results["summary"]["failed_requests"] += result["failed_requests"]
                
            except Exception as e:
                print(f"❌ Failed to run collection {collection_file.name}: {e}")
                self.results["collections"][collection_file.name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        self.results["summary"]["total_collections"] = len(collection_files)
        self.results["summary"]["total_execution_time"] = time.time() - start_time
        self.results["execution_completed"] = datetime.now().isoformat()
        
        # Print summary
        self.print_summary()
        
        # Save results
        self.save_results()
    
    def print_summary(self):
        """Print execution summary"""
        summary = self.results["summary"]
        
        print("\n" + "=" * 60)
        print("📊 POSTMAN EXECUTION SUMMARY")
        print("=" * 60)
        print(f"Collections Executed: {summary['total_collections']}")
        print(f"Total Requests: {summary['total_requests']}")
        print(f"Successful Requests: {summary['successful_requests']} ✅")
        print(f"Failed Requests: {summary['failed_requests']} ❌")
        print(f"Total Execution Time: {summary['total_execution_time']:.2f} seconds")
        
        if summary['total_requests'] > 0:
            success_rate = (summary['successful_requests'] / summary['total_requests']) * 100
            print(f"Success Rate: {success_rate:.1f}%")
        
        # Collection breakdown
        print("\n📦 COLLECTION BREAKDOWN:")
        for name, result in self.results["collections"].items():
            if isinstance(result, dict) and "total_requests" in result:
                print(f"  {name}: {result['successful_requests']}/{result['total_requests']} successful")
    
    def save_results(self):
        """Save results to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"postman_execution_results_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, default=str)
            
            print(f"\n📄 Detailed results saved to: {filename}")
            
        except Exception as e:
            print(f"❌ Failed to save results: {e}")


async def main():
    """Main execution function"""
    try:
        runner = PostmanCollectionRunner()
        await runner.run_all_collections()
        
        print("\n✨ Postman collection execution completed!")
        print("🔍 This execution tested all available scenarios without modifying the codebase")
        
    except KeyboardInterrupt:
        print("\n⚠️  Execution interrupted by user")
    except Exception as e:
        print(f"\n❌ Execution failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
