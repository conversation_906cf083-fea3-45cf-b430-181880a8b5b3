# OAuth Profile Enrichment Settings

## Overview

This document explains the profile enrichment behavior during OAuth registration and account linking in the CreatorVerse platform.

## Current Behavior

When a user registers via OAuth, the system:
1. Creates or updates the user record based on OAuth provider information
2. Links the OAuth account to the user
3. For new users only: Enriches the profile with additional data from the provider (e.g., YouTube statistics)

## Profile Enrichment Control

Profile enrichment is now controlled by the `skip_profile_enrichment_for_existing_users` setting in `app/core/config.py`.

- When `true` (default): Fetches profile data only for new users
- When `false`: Fetches profile data for all users during OAuth authentication

## Account Linking Without Enrichment

The system now differentiates between:
- **New User Registration**: Full profile creation with enrichment
- **Existing User OAuth Link**: Only basic account linking without changing existing profile data

## Implementation Details

The profile enrichment control is implemented in:
1. `oauth_service.py` - In the `create_or_update_user_from_oauth` method
2. `oauth_user_manager.py` - In the `_create_social_profiles` method

Both implementations check if the user is new before attempting to fetch YouTube or Instagram profile data.

## Logs to Monitor

Look for these log messages to confirm proper behavior:
- New user: `"YouTube profile created for new influencer: {email}"`
- Existing user: `"Skipping YouTube profile enrichment for existing user: {email}"`

## Rationale

This change prevents unwanted profile updates when users who registered via email later link their Google/YouTube account. It ensures profile data is only fetched during initial registration, not during subsequent account linking.
