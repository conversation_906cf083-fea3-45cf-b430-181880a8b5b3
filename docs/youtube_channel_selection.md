# YouTube Channel Selection Flow

This document explains the YouTube channel selection functionality in the CreatorVerse User Backend service.

## Overview

When users authenticate with Google OAuth and grant YouTube access permissions, they may have multiple YouTube channels associated with their Google account. This feature allows users to choose which YouTube channel they want to use with their CreatorVerse profile instead of automatically selecting the first channel found.

## Database Schema

The YouTube channel integration utilizes two primary database tables:

1. **OAuthAccount**:
   - Stores OAuth credentials for various providers
   - Contains `access_token`, `refresh_token`, and `expires_at` for accessing YouTube API

2. **SocialProfile**:
   - Links a YouTube channel to a user's profile
   - Store YouTube channel details like subscriber count, video count, etc.
   - References `oauth_account_id` to link back to OAuth credentials

## Flow Description

### 1. Google OAuth Authentication

When a user authenticates with Google OAuth:

1. The system requests YouTube access permissions (`https://www.googleapis.com/auth/youtube.readonly` scope)
2. After successful authentication, OAuth tokens are stored in the `OAuthAccount` table
3. The system checks how many YouTube channels the user has access to
4. If multiple channels exist, it sets a flag in the user's metadata: `needs_youtube_channel_selection`

### 2. Channel Selection Process

#### A. Redirect to Channel Selection Interface

After OAuth authentication, if multiple channels are detected:

1. The OAuth callback handler checks for the `needs_youtube_channel_selection` flag
2. The user is redirected to `/api/v1/youtube/redirect?user_id={user_id}`
3. This endpoint redirects to the channel selection UI at `/api/v1/youtube/channels?user_id={user_id}`

#### B. Listing Available Channels

1. The frontend calls `GET /api/v1/youtube/channels` endpoint
2. The backend retrieves OAuth tokens for the user from the database
3. It uses the tokens to call the YouTube API and fetch all accessible channels
4. Channel information is returned as a list of `YouTubeChannelInfo` objects

#### C. Channel Selection

1. The user selects their preferred channel from the UI
2. The frontend calls `POST /api/v1/youtube/channels/select` with the selected `channel_id`
3. The backend verifies the channel is accessible to the user
4. It then creates or updates a `SocialProfile` record for the YouTube service with the selected channel's details

### 3. Data Storage

When a channel is selected, the following data is stored in the `SocialProfile` table:

- `oauth_account_id`: Links to the user's Google OAuth account
- `service`: "youtube"
- `external_id`: YouTube channel ID
- `display_name`: Channel title
- `avatar_url`: Channel thumbnail URL
- `follower_count`: Subscriber count
- `post_count`: Video count
- `raw_json`: Complete channel details
- `fetched_at`: Timestamp

## API Endpoints

### 1. List YouTube Channels

```
GET /api/v1/youtube/channels
```

**Authentication**: Required (JWT token)

**Response**:
```json
{
  "channels": [
    {
      "channel_id": "UC1234567890abcdef",
      "title": "My Tech Channel",
      "description": "This is my tech channel",
      "thumbnail_url": "https://yt3.googleusercontent.com/...",
      "subscriber_count": 5000,
      "video_count": 42,
      "view_count": 250000,
      "is_verified": true
    },
    {
      "channel_id": "UC0987654321fedcba",
      "title": "My Gaming Channel",
      "description": "Gaming content",
      "thumbnail_url": "https://yt3.googleusercontent.com/...",
      "subscriber_count": 1200,
      "video_count": 18,
      "view_count": 45000,
      "is_verified": false
    }
  ]
}
```

### 2. Select YouTube Channel

```
POST /api/v1/youtube/channels/select
```

**Authentication**: Required (JWT token)

**Request Body**:
```json
{
  "channel_id": "UC1234567890abcdef"
}
```

**Response**:
```json
{
  "status": "success",
  "message": "YouTube channel selected successfully",
  "data": {
    "channel": {
      "channel_id": "UC1234567890abcdef",
      "title": "My Tech Channel",
      "description": "This is my tech channel",
      "thumbnail_url": "https://yt3.googleusercontent.com/...",
      "subscriber_count": 5000,
      "video_count": 42,
      "view_count": 250000,
      "is_verified": true
    }
  }
}
```

### 3. YouTube Redirect (Internal)

```
GET /api/v1/youtube/redirect
```

**Query Parameters**:
- `user_id`: User's UUID
- `original_redirect` (optional): URL to redirect after channel selection

This endpoint redirects to the channel selection UI.

## Implementation Details

### Schema Models

The feature uses the following Pydantic models:

1. **YouTubeChannelInfo**:
   - Schema for YouTube channel information
   - Contains metadata like channel ID, title, subscriber count, etc.

2. **YouTubeChannelListResponse**:
   - Response schema for channel listing endpoint
   - Contains list of YouTubeChannelInfo objects

3. **YouTubeChannelSelectRequest**:
   - Request schema for channel selection endpoint
   - Contains the selected channel ID

### OAuth Enhancement

The Google OAuth flow was enhanced to:
- Detect multiple YouTube channels
- Set flags in user metadata when selection is needed
- Add support for redirecting users to the selection flow

## Integration with Frontend

The frontend should:

1. Direct users who complete Google OAuth through the channel selection flow
2. Display a list of available YouTube channels with relevant metrics
3. Allow users to select their preferred channel
4. Handle the redirect after channel selection is complete

## Error Handling

The system handles the following error scenarios:

1. No Google OAuth tokens found (HTTP 404)
2. Invalid or expired tokens (HTTP 401)
3. YouTube API client unavailable (HTTP 501)
4. Selected channel not found or not accessible (HTTP 404)
5. Google account not found for user (HTTP 404)

## Cache Invalidation

After channel selection, the following cache keys are invalidated:

- `CreatorVerse:user:profile_completion:{user_id}`

This ensures user profile data remains up-to-date after channel selection.
