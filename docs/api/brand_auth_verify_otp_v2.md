# Brand Auth Verify OTP API v2 - Updated Flow Analysis

**Endpoint:** `POST /v1/brand/auth/verify-otp`  
**Method:** POST  
**Description:** Verifies the one-time password (OTP) sent to a brand user's email during registration and handles organization creation. User must request brand membership separately.

## Updated Flow Chart

```
┌─────────────────┐
│ Client Submits  │
│ Email + OTP +   │
│ Role UUID       │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Validate Request│
│ Parameters      │ Redis Check: None
│ Strip & Lower   │ DB Calls: 0
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Validate Redis  │
│ Client          │ Redis Check: Connection validation
└────────┬────────┘ DB Calls: 0
         │
         ▼
┌─────────────────┐
│ Centralized OTP │
│ Service Verify  │ Redis Operations:
│                 │ - HGETALL CreatorVerse:otp:{email}
└────────┬────────┘ - DEL CreatorVerse:otp:{email} (on success)
         │         │ DB Calls: 0
         ▼         │
┌─────────────────┐│
│ OTP Valid?      ││ If Invalid/Expired/Locked:
│                 ││ - HSET failed_attempts++
└────────┬────────┘│ - HSET lockout_until (if needed)
         │ No      │ - Return Error
         ▼         │
┌─────────────────┐│
│ Prepare User    ││
│ Creation Data   ││ Data from Redis OTP +
│                 ││ Client Info merge
└────────┬────────┘│
         │ Yes     │
         ▼         │
┌─────────────────┐│
│ Create Brand    ││ This is the MAIN function
│ User with Org   ││ Multiple DB + Redis operations
│ Handling        ││ See detailed breakdown below
└────────┬────────┘│
         │         │
         ▼         │
┌─────────────────┐│
│ Add Email to    ││ Redis Operation:
│ Bloom Filter    ││ - BF.ADD CreatorVerse:bloom:emails
└────────┬────────┘│ DB Calls: 0
         │         │
         ▼         │
┌─────────────────┐│
│ Send Welcome    ││ Redis Operations:
│ Email (Async)   ││ - GET/SET welcome email tracking
└────────┬────────┘│ DB Calls: 0 (email service)
         │         │
         ▼         │
┌─────────────────┐│
│ Return Success  ││ User created but NO brand membership
│ Response with   ││ Must use /brand/membership/request
│ JWT Token       ││ to request brand access
└─────────────────┘│
```

## Updated Flow Chart - Request OTP

```
┌─────────────────┐
│ Client Requests │
│ OTP for Brand   │
│ Registration    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Validate Domain │
│ Not Consumer    │ Check against BLOCKED_CONSUMER_DOMAINS
│ Domain          │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Check Email in  │ Redis: BF.EXISTS CreatorVerse:bloom:emails
│ Bloom Filter    │ Fast probabilistic check
└────────┬────────┘
         │ Exists? │
         ▼         │
┌─────────────────┐│ If Bloom filter says "exists":
│ Verify with DB  ││ DB Call: SELECT from users WHERE email
│ (False Positive ││ If actually exists → Error
│ Check)          ││ If false positive → Continue
└────────┬────────┘│
         │         │
         ▼         │
┌─────────────────┐│
│ Check OTP       ││ Redis: GET CreatorVerse:otp:{email}
│ Resend Status   ││ Check 30-second cooldown
└────────┬────────┘│
         │         │
         ▼         │
┌─────────────────┐│
│ Verify Email    ││ External: MX record lookup
│ via MX Lookup   ││ Validate email deliverability
└────────┬────────┘│
         │         │
         ▼         │
┌─────────────────┐│
│ Generate & Send ││ Redis: HSET CreatorVerse:otp:{email}
│ OTP via Email   ││ Store hashed OTP with metadata
└────────┬────────┘│
         │         │
         ▼         │
┌─────────────────┐│
│ Return Success  ││
│ Response        ││
└─────────────────┘│
```

## Detailed create_brand_user_with_organization_handling Flow

```
┌─────────────────┐
│ Check if Email  │ Redis: BF.EXISTS CreatorVerse:bloom:emails
│ Already Exists  │ If exists → EmailAlreadyExistsError
│ (Bloom Filter)  │ DB Calls: 0
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Extract Domain  │ Split email at '@', get domain
│ from Email      │ Redis/DB: 0
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Check Domain    │ Redis: GET CreatorVerse:organization:domain:{domain}
│ Organization    │ If cache miss:
│ (Cache-Aside)   │ DB Call 1: SELECT from organizations WHERE domain
└────────┬────────┘ Redis: SET cache result (1 hour TTL)
         │
         ▼
┌─────────────────┐
│ First User from │ If no organization exists:
│ Domain?         │ - Create Organization (DB Call 2)
│                 │ - NO memberships created
└────────┬────────┘ - User has organization but no access
         │ YES     │
         ▼         │ If organization exists:
┌─────────────────┐│ - Just create user (DB Call 2)
│ Create User     ││ - No organization membership
│ Account         ││ - No brand access
└────────┬────────┘│
         │         │
         ▼         │
┌─────────────────┐│
│ Create User     ││ DB Call: INSERT into users
│ Session & JWT   ││ DB Call: INSERT into user_sessions
└────────┬────────┘│ Redis: Cache user session
         │         │
         ▼         │
┌─────────────────┐│
│ Return User +   ││ User has no brand access
│ Organization    ││ Must request membership separately
│ Details         ││ via /brand/membership/request
└─────────────────┘│
```

## Request Structure

```json
{
  "email": "<EMAIL>",
  "otp": "123456", 
  "role_uuid": "uuid-of-brand-role"
}
```

### Request Parameters

| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| email | string | Yes | Brand user email address | Email format, domain validation |
| otp | string | Yes | 6-digit OTP from email | Numeric, 6 characters |
| role_uuid | string | Yes | UUID of brand role | Valid UUID format |

## Response Structure

### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Brand user created successfully",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer", 
    "expires_in": 3600,
    "user": {
      "id": "uuid-string",
      "email": "<EMAIL>",
      "status": "requested",
      "is_active": true,
      "created_at": "2024-01-15T10:30:00Z"
    },
    "organization": {
      "id": "org-uuid",
      "name": "Company Inc",
      "domain": "company.com",
      "is_first_user": true,
      "user_role": "none"
    },
    "next_steps": {
      "action": "request_brand_membership",
      "message": "Use /v1/brand/membership/request to request access to specific brands",
      "endpoint": "/v1/brand/membership/request"
    }
  }
}
```

### Error Responses

#### Invalid OTP (400)
```json
{
  "success": false,
  "message": "Invalid or expired OTP",
  "error": {
    "code": "OTP_INVALID",
    "details": "OTP verification failed"
  }
}
```

#### OTP Expired (400)
```json
{
  "success": false, 
  "message": "OTP has expired",
  "error": {
    "code": "OTP_EXPIRED",
    "details": "Please request a new OTP"
  }
}
```

#### Account Locked (429)
```json
{
  "success": false,
  "message": "Too many failed attempts", 
  "error": {
    "code": "OTP_TOO_MANY_ATTEMPTS",
    "details": "Account locked for 5 minutes"
  }
}
```

#### Email Already Exists (400)
```json
{
  "success": false,
  "message": "Email already exists in the system",
  "error": {
    "code": "EMAIL_ALREADY_EXISTS", 
    "details": "Please use login instead"
  }
}
```

## Database Operations

### Total DB Calls per Scenario

#### First User from Domain (New Organization)
- **Total DB Calls: 3-4**
  1. Check domain organization existence (if cache miss)
  2. Create organization 
  3. Create user account
  4. Create user session

#### Existing Domain User  
- **Total DB Calls: 2-3**
  1. Check domain organization (if cache miss)
  2. Create user account
  3. Create user session

### Database Tables Modified

| Table | Operation | Purpose |
|-------|-----------|---------|
| `users.users` | INSERT | Create user account |
| `users.organizations` | INSERT (first user only) | Create organization |
| `users.user_sessions` | INSERT | Create login session |

## Redis Operations

### Redis Keys Used

| Redis Key Pattern | Type | Purpose | TTL | Data Stored |
|-------------------|------|---------|-----|-------------|
| `CreatorVerse:otp:{email}` | Hash | OTP verification | 600s | `otp_hash`, `generated_at`, `failed_attempts`, `lockout_until` |
| `CreatorVerse:bloom:emails` | Bloom Filter | Email existence check | Permanent | Email addresses (probabilistic) |
| `CreatorVerse:organization:domain:{domain}` | String | Domain organization cache | 3600s | Organization JSON data |
| `CreatorVerse:user:session:{user_id}` | Hash | User session cache | 86400s | Session data and JWT info |
| `CreatorVerse:domain:orgs:{domain}` | String | Domain organizations list | 3600s | List of organizations for domain |
| `CreatorVerse:welcome:email:{email}` | String | Welcome email tracking | 604800s | Email send status |

### Redis Operations Count

#### OTP Verification Phase
- **HGETALL** `CreatorVerse:otp:{email}` (1 operation)
- **DEL** `CreatorVerse:otp:{email}` (1 operation on success)
- **HSET** failed attempts (1 operation on failure)

#### User Creation Phase  
- **BF.EXISTS** `CreatorVerse:bloom:emails` (1 operation)
- **GET** domain organization cache (1 operation)
- **SET** domain organization cache (1 operation if cache miss)
- **BF.ADD** `CreatorVerse:bloom:emails` (1 operation)
- **DEL** cache invalidation (3-5 operations)

#### Welcome Email Phase
- **GET/SET** welcome email tracking (2 operations)

**Total Redis Operations: 8-12 per request**

## File Structure & Control Points

### Main Files Involved

| File | Purpose | Key Functions |
|------|---------|---------------|
| `app/api/api_v1/endpoints/auth_brands.py` | API endpoint | `verify_brand_otp()` |
| `app/services/centralized_otp_service.py` | OTP verification | `verify_otp()` |
| `app/core/database_helper/users_helper.py` | User creation | `create_brand_user_with_organization_handling()` |
| `app/core/database_helper/organization_helper.py` | Organization logic | `check_and_create_organization_for_user()` |
| `app/utilities/bloom_filter.py` | Email tracking | `add_email()`, `check_email_exists()` |
| `app/utilities/welcome_email_service.py` | Email notifications | `send_welcome_email_if_not_sent()` |

### Configuration Control Points

| Setting | File | Variable | Purpose |
|---------|------|----------|---------|
| OTP TTL | `app/core/redis_keys.py` | `RedisConfig.OTP_TTL` | OTP expiration time |
| OTP Length | `app/core/config.py` | `DEFAULT_OTP_LENGTH` | OTP digit count |
| Max OTP Attempts | `app/utilities/validation_functions.py` | Hard-coded `3` | Lockout threshold |
| Lockout Duration | `app/utilities/validation_functions.py` | Hard-coded `5 * 60` | Account lockout time |
| Cache TTL | Various helpers | Function parameters | Cache expiration times |

## Security Features

### OTP Security
- **Hashed Storage**: OTPs stored as hashed values in Redis
- **Brute Force Protection**: Max 3 attempts before 5-minute lockout
- **Time-based Expiry**: OTPs expire after 10 minutes
- **Single Use**: OTPs deleted after successful verification

### Domain Security  
- **Consumer Domain Blocking**: Prevents registration with consumer emails
- **Domain Verification**: Auto-verification for first user from domain
- **Organization Isolation**: Users can only access their domain's organization

### Session Security
- **JWT Tokens**: Secure token-based authentication
- **Session Tracking**: All sessions stored in database
- **IP/User Agent Logging**: Request metadata for audit

## Performance Optimizations

### Caching Strategy
- **Cache-Aside Pattern**: Implemented for all database lookups
- **Bloom Filter**: O(1) email existence checks
- **Domain Caching**: 1-hour TTL for organization lookups
- **Batch Operations**: Multiple cache invalidations in single pipeline

### Bloom Filter False Positive Handling
- **Two-Stage Verification**: Bloom filter for fast check, DB lookup for verification
- **False Positive Rate**: ~1% false positive rate acceptable for performance gains
- **Fail-Open Strategy**: If DB verification fails, allow registration to proceed
- **Logging**: Track false positives for Bloom filter optimization

### Database Optimizations
- **Transaction Boundaries**: All related operations in single transaction
- **Minimal Queries**: Cache-first approach reduces DB load
- **Index Usage**: Email and domain fields are indexed

## Error Handling

### Rollback Scenarios
- **Database Errors**: Full transaction rollback
- **Redis Failures**: Graceful degradation, operations continue
- **Email Service Errors**: Non-blocking, registration succeeds
- **Cache Errors**: Fall back to database queries

### Monitoring Points
- **OTP Failure Rates**: Track failed verification attempts
- **Registration Success**: Monitor successful brand user creation
- **Cache Hit Rates**: Monitor Redis performance
- **Email Delivery**: Track welcome email success rates

## Business Logic

### First User from Domain
1. **Automatic Organization Creation**: Creates organization with domain name
2. **No Domain Claims**: Domain ownership is implicit through organization creation
3. **No Organization Membership**: User is not automatically added to organization membership table
4. **No Default Brand**: User must create brands manually using `/create-brand` endpoint

### Subsequent Users
1. **No Automatic Organization Access**: Users don't get automatic organization membership
2. **Manual Brand Creation**: Users must create or be invited to brands
3. **Independent User Account**: Each user has their own account without organization ties

This comprehensive flow ensures secure, scalable brand user registration with proper organization and brand hierarchy management.
