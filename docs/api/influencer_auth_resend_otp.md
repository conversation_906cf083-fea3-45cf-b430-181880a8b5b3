# Influencer Auth Resend OTP API - Detailed Flow Analysis

**Endpoint:** `POST /v1/influencer/auth/register/resend-otp`  
**Method:** POST  
**Description:** Resends the one-time password (OTP) to an influencer's email during registration process with rate limiting and security measures.

## Flow Diagram

```
┌─────────────────┐
│ Client Requests │
│ OTP Resend for  │
│ Influencer      │
│ Registration    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Validate Request│ Operation: Input validation
│ Parameters      │ Redis Calls: 0
│ (Email)         │ DB Calls: 0
└────────┬────────┘ Validate email format, normalize to lowercase
         │
         ▼
┌─────────────────┐
│ Check Email     │ Redis Operation 1:
│ Existence in    │ BF.EXISTS CreatorVerse:bloom:emails {email}
│ Bloom Filter    │ DB Calls: 0
└────────┬────────┘ Fast probabilistic check for email existence
         │
         ▼
┌─────────────────┐
│ Handle Bloom    │ If Bloom Filter = TRUE:
│ Filter Result   │ DB Call 1: SELECT * FROM users.users WHERE email = ?
│                 │ Check if false positive or actual user exists
└────────┬────────┘ If user exists → throw EmailAlreadyExistsError
         │
         ▼
┌─────────────────┐
│ Check OTP       │ Redis Operation 2:
│ Resend Status   │ HGETALL CreatorVerse:otp:{email}
│ & Rate Limits   │ Check current OTP status and resend eligibility
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Validate Resend │ Check multiple conditions:
│ Eligibility     │ - 30-second cooldown between resends
│                 │ - Max 3 resends per OTP session
└────────┬────────┘ - Daily limit check (if implemented)
         │
         ▼
┌─────────────────┐
│ Verify Email    │ External Operation: DNS MX lookup
│ Deliverability  │ Redis Operation 3 (cache check):
│ (MX Lookup)     │ GET CreatorVerse:email:mx:{domain}
└────────┬────────┘ Cache TTL: 3600s (1 hour)
         │
         ▼
┌─────────────────┐
│ Generate New    │ Redis Operations 4-6:
│ OTP & Update    │ HSET CreatorVerse:otp:{email} (new OTP data)
│ Redis           │ EXPIRE CreatorVerse:otp:{email} 600
└────────┬────────┘ INCR resend_count in hash
         │
         ▼
┌─────────────────┐
│ Send Email      │ External Operation: Email service call
│ via Service     │ Redis Operations 7-8:
│                 │ SET CreatorVerse:rate:otp:cooldown:{email} timestamp
└────────┬────────┘ EXPIRE CreatorVerse:rate:otp:cooldown:{email} 30
         │
         ▼
┌─────────────────┐
│ Update Rate     │ Redis Operation 9:
│ Limiting        │ INCR CreatorVerse:rate:otp:daily:{email}
│ Counters        │ EXPIRE (if first increment) 86400
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Return Success  │ Response with confirmation
│ Response        │ Include retry timing info
└─────────────────┘
```

## Request Structure

```json
{
  "email": "<EMAIL>"
}
```

### Request Parameters

| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| email | string | Yes | Influencer email address | Email format validation, max 255 chars |

## Response Structure

### Success Response (200 OK)

```json
{
  "success": true,
  "message": "OTP resent successfully. Please check your email.",
  "data": {
    "email": "<EMAIL>",
    "otp_sent": true,
    "expires_in": 600,
    "resend_available_in": 30,
    "daily_limit_remaining": 4,
    "resend_count": 2
  }
}
```

### Rate Limited Response (429 Too Many Requests)

```json
{
  "success": false,
  "message": "Too many OTP requests. Please wait before requesting again.",
  "error": {
    "code": "OTP_RATE_LIMITED",
    "details": "Maximum 5 OTP requests per day exceeded",
    "retry_after": 43200
  }
}
```

### Cooldown Response (400 Bad Request)

```json
{
  "success": false,
  "message": "Please wait before requesting a new OTP",
  "error": {
    "code": "OTP_COOLDOWN_ACTIVE",
    "details": "Must wait 30 seconds between OTP requests",
    "retry_after": 15
  }
}
```

## Detailed Redis Operations

### Redis Keys Used

| Redis Key Pattern | Type | Purpose | TTL | Data Stored | Operations |
|-------------------|------|---------|-----|-------------|------------|
| `CreatorVerse:otp:{email}` | Hash | Current OTP session | 600s | `otp_hash`, `generated_at`, `failed_attempts`, `resend_count` | HGETALL, HSET, EXPIRE |
| `CreatorVerse:bloom:emails` | Bloom Filter | Email existence check | Permanent | Email addresses (probabilistic) | BF.EXISTS |
| `CreatorVerse:rate:otp:daily:{email}` | String | Daily OTP limit | 86400s | Count of OTPs sent today | INCR, EXPIRE |
| `CreatorVerse:rate:otp:cooldown:{email}` | String | Resend cooldown | 30s | Timestamp of last send | SET, EXPIRE |
| `CreatorVerse:email:mx:{domain}` | String | MX lookup cache | 3600s | Email deliverability status | GET, SET |

### Redis Operations Breakdown

#### Pre-Flight Checks (Operations 1-3)
```redis
# Check if email exists (Bloom Filter)
BF.EXISTS CreatorVerse:bloom:emails "<EMAIL>"

# Get current OTP status  
HGETALL CreatorVerse:otp:<EMAIL>

# Check MX record cache
GET CreatorVerse:email:mx:gmail.com
```

#### OTP Generation & Storage (Operations 4-6)
```redis
# Store new OTP with metadata
HSET CreatorVerse:otp:<EMAIL> 
     "otp_hash" "hashed_otp_value"
     "generated_at" "1642123456" 
     "failed_attempts" "0"
     "resend_count" "2"
     "email" "<EMAIL>"
     "role" "influencer"
     "request_type" "register"

# Set expiration
EXPIRE CreatorVerse:otp:<EMAIL> 600
```

#### Rate Limiting (Operations 7-9)
```redis
# Set cooldown period
SETEX CreatorVerse:rate:otp:cooldown:<EMAIL> 30 "1642123456"

# Update daily counter
INCR CreatorVerse:rate:otp:daily:<EMAIL>
EXPIRE CreatorVerse:rate:otp:daily:<EMAIL> 86400
```

## Database Operations

### Scenarios and DB Calls

#### New User (Never Registered)
- **Total DB Calls: 0-1**
  - Bloom filter check: 0 DB calls (fast path)
  - If Bloom filter returns false: No DB verification needed
  - If Bloom filter returns true: 1 DB call to verify (handles false positives)

#### Existing User Detection
- **Total DB Calls: 1**
  - Bloom filter indicates existence: 1 DB call to verify user exists
  - Throws EmailAlreadyExistsError if confirmed

#### False Positive Handling
- **Total DB Calls: 1**
  - Bloom filter false positive: 1 DB call to verify user doesn't exist
  - Continues with OTP resend process

### Database Tables Queried

| Table | Operation | Purpose | SQL Query |
|-------|-----------|---------|-----------|
| `users.users` | SELECT | Verify email existence | `SELECT id, email, status FROM users.users WHERE email = ?` |

## Performance Metrics

### Redis Operations Count per Request

#### Standard Flow (New User)
- **BF.EXISTS**: 1 operation (Bloom filter check)
- **HGETALL**: 1 operation (OTP status check)
- **GET**: 1 operation (MX cache check)
- **HSET**: 1 operation (Store new OTP)
- **EXPIRE**: 3 operations (OTP, cooldown, daily limit)
- **SETEX**: 1 operation (Cooldown timestamp)
- **INCR**: 1 operation (Daily counter)

**Total Redis Operations: 9 per request**

#### With Cache Misses
- **Additional SET**: 1-2 operations (MX lookup caching)
- **Additional EXPIRE**: 1-2 operations (Cache TTL)

**Total with Cache Miss: 11-13 operations**

### Email Validation Performance

#### MX Lookup Optimization
```python
# Cached MX lookup result structure
{
    "domain": "gmail.com",
    "deliverable": true,
    "mx_records": ["gmail-smtp-in.l.google.com"],
    "cached_at": "2024-01-15T10:30:00Z",
    "ttl": 3600
}
```

## Rate Limiting Implementation

### Multi-Level Rate Limiting

#### 1. Immediate Cooldown (30 seconds)
```redis
# Check if cooldown is active
EXISTS CreatorVerse:rate:otp:cooldown:<EMAIL>

# If exists, calculate remaining time
TTL CreatorVerse:rate:otp:cooldown:<EMAIL>
```

#### 2. Resend Limit per OTP (3 resends)
```redis
# Check resend count in OTP hash
HGET CreatorVerse:otp:<EMAIL> resend_count

# If >= 3, force new OTP generation
```

#### 3. Daily Limit (5 OTPs per day)
```redis
# Check daily count
GET CreatorVerse:rate:otp:daily:<EMAIL>

# If >= 5, block further requests
```

### Rate Limit Response Headers
```http
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 3
X-RateLimit-Reset: 1642123456
Retry-After: 30
```

## Security Features

### OTP Security
- **Hashed Storage**: OTPs stored as SHA-256 hashes
- **Time-based Expiry**: 10-minute expiration window
- **Resend Tracking**: Limited to 3 resends per OTP session
- **Single Use**: OTPs invalidated after successful verification

### Anti-Abuse Measures
- **Bloom Filter**: Fast duplicate email detection with ~1% false positive rate
- **Rate Limiting**: Multiple layers of request limiting
- **MX Validation**: Verify email can receive messages
- **Cooldown Periods**: Prevent rapid-fire requests

### Audit & Monitoring
- **Request Logging**: All OTP requests logged with metadata
- **Failed Attempt Tracking**: Monitor abuse patterns
- **Performance Metrics**: Track success rates and latency

## Error Handling & Rollback

### Error Scenarios

#### Redis Failure
```python
try:
    # Redis operations
    redis_client.hgetall(otp_key)
except redis.RedisError:
    # Graceful degradation
    # Allow OTP resend but lose rate limiting
    logger.warning("Redis unavailable, proceeding without rate limits")
```

#### Email Service Failure
```python
try:
    # Send OTP email
    email_service.send_otp_email(email, otp)
except EmailServiceError:
    # Rollback Redis state
    redis_client.delete(otp_key)
    redis_client.delete(cooldown_key)
    raise HTTPException(500, "Email service unavailable")
```

#### Database Verification Failure
```python
try:
    # Verify user existence
    user = session.query(User).filter_by(email=email).first()
except DatabaseError:
    # Fail-open approach for availability
    logger.error("DB verification failed, allowing registration")
    # Continue with OTP process
```

## Monitoring & Alerting

### Key Metrics to Monitor

#### Performance Metrics
- **Average Response Time**: Target < 500ms
- **Redis Operation Latency**: Target < 10ms per operation
- **Email Delivery Rate**: Target > 95%
- **MX Lookup Success Rate**: Target > 99%

#### Business Metrics
- **OTP Resend Rate**: Percentage of users who resend
- **Daily OTP Volume**: Total OTP requests per day
- **Rate Limit Hit Rate**: Abuse detection metric
- **False Positive Rate**: Bloom filter effectiveness

#### Alert Thresholds
```yaml
alerts:
  high_error_rate:
    threshold: "> 5% in 5 minutes"
    action: "Page on-call engineer"
  
  redis_latency:
    threshold: "> 50ms average"
    action: "Slack notification"
  
  email_delivery_failure:
    threshold: "> 10% in 10 minutes" 
    action: "Email alert to team"
```

## Configuration Parameters

### Configurable Settings

| Setting | Default | Purpose | Configuration File |
|---------|---------|---------|-------------------|
| OTP_TTL | 600s | OTP expiration time | `app/core/redis_keys.py` |
| RESEND_COOLDOWN | 30s | Time between resends | `app/services/centralized_otp_service.py` |
| MAX_RESENDS_PER_OTP | 3 | Resend limit per session | `app/services/centralized_otp_service.py` |
| DAILY_OTP_LIMIT | 5 | OTPs per day per email | `app/services/centralized_otp_service.py` |
| MX_CACHE_TTL | 3600s | MX lookup cache duration | `app/utilities/validation_functions.py` |

### Environment-Specific Settings
```python
# Development settings
OTP_TTL = 300  # 5 minutes for faster testing
DAILY_OTP_LIMIT = 10  # Higher limit for testing

# Production settings  
OTP_TTL = 600  # 10 minutes
DAILY_OTP_LIMIT = 5  # Stricter limit
```

This comprehensive analysis shows the influencer OTP resend endpoint is optimized for performance with minimal database operations, extensive Redis caching, and multiple layers of security and rate limiting.
