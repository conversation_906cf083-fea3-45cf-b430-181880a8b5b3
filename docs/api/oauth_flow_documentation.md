# OAuth Authentication Flow API Documentation

**Base Path:** `/v1/oauth`  
**Description:** OAuth 2.0 authentication flow for third-party social login integration (Google, Facebook, etc.)

## API Endpoints Overview

1. **POST /oauth/initiate** - Initialize OAuth flow
2. **GET /oauth/callback** - Handle OAuth provider callback

## Flow Chart - Complete OAuth Authentication

```
┌─────────────────┐
│ Client Requests │
│ OAuth Login     │
│ (Google/FB/etc) │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ POST            │
│ /oauth/initiate │ Redis: Store state + PKCE
│                 │ DB Calls: 0
└────────┬────────┘ Generate: state, code_verifier, code_challenge
         │
         ▼
┌─────────────────┐
│ Redirect Client │
│ to OAuth        │ Return: authorization_url with state
│ Provider        │ Redis: SET CreatorVerse:oauth:state:{state}
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ User Authorizes │
│ on Provider     │ User interaction with Google/Facebook
│ (External)      │ Outside our system
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Provider        │
│ Redirects to    │ GET /oauth/callback?code=xxx&state=yyy
│ /oauth/callback │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Validate State  │ Redis: GET CreatorVerse:oauth:state:{state}
│ & Code          │ Redis: DEL CreatorVerse:oauth:state:{state}
└────────┬────────┘ Validate PKCE code_verifier
         │
         ▼
┌─────────────────┐
│ Exchange Code   │ HTTP: POST to provider token endpoint
│ for Access      │ Get: access_token, refresh_token
│ Token           │ Redis/DB: 0 (external API call)
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Fetch User      │ HTTP: GET provider user info API
│ Profile from    │ Get: user_id, email, name, avatar
│ Provider        │ Redis/DB: 0 (external API call)
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Check if User   │ Redis: BF.EXISTS CreatorVerse:bloom:emails
│ Exists (Bloom   │ Fast probabilistic check
│ Filter)         │ DB Calls: 0
└────────┬────────┘
         │ No     │ Yes
         ▼        ▼
┌─────────────────┐ ┌─────────────────┐
│ Create New User │ │ Link OAuth      │
│ Account         │ │ Account to      │
│                 │ │ Existing User   │
└────────┬────────┘ └────────┬────────┘
         │                   │
         ▼                   ▼
┌─────────────────┐ ┌─────────────────┐
│ Create OAuth    │ │ Update OAuth    │
│ Account Record  │ │ Account Tokens  │
│                 │ │                 │
└────────┬────────┘ └────────┬────────┘
         │                   │
         ▼                   ▼
┌─────────────────┐ ┌─────────────────┐
│ Generate JWT    │ │ Generate JWT    │
│ Session Token   │ │ Session Token   │
└────────┬────────┘ └────────┬────────┘
         │                   │
         ▼                   ▼
┌─────────────────┐
│ Return Success  │
│ with JWT &      │
│ User Profile    │
└─────────────────┘
```

## Detailed API Documentation

### 1. POST /oauth/initiate

**Description:** Initialize OAuth authentication flow with a social provider

#### Request

```json
{
  "provider": "google",
  "redirect_uri": "https://app.creatorverse.com/auth/callback",
  "scopes": ["email", "profile"]
}
```

#### Request Parameters

| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| provider | string | Yes | OAuth provider name | Enum: google, facebook, twitter, linkedin |
| redirect_uri | string | Yes | Client redirect URI | Must match registered URIs |
| scopes | array | No | Requested permissions | Provider-specific scopes |

#### Response (200 OK)

```json
{
  "success": true,
  "data": {
    "authorization_url": "https://accounts.google.com/oauth/authorize?client_id=...&state=abc123",
    "state": "abc123xyz789",
    "expires_in": 600
  }
}
```

#### Implementation Details

##### Redis Operations (3 operations)
1. **Generate State**: `SET CreatorVerse:oauth:state:{state}` (TTL: 600s)
   - Stored Data: `{"provider": "google", "code_verifier": "xyz", "redirect_uri": "...", "created_at": timestamp}`
2. **Store PKCE**: Embedded in state data
3. **Rate Limiting**: `INCR CreatorVerse:oauth:rate:{ip}` (TTL: 3600s)

##### Database Operations
- **DB Calls: 0** (Only state storage in Redis)

##### Files Involved
- **Controller**: `app/api/v1/endpoints/oauth.py` - `initiate_oauth()`
- **Service**: `app/services/oauth_service.py` - OAuth provider implementations
- **Config**: `app/core/oauth_config.py` - Provider configurations

---

### 2. GET /oauth/callback

**Description:** Handle OAuth provider callback and complete authentication

#### Request (Query Parameters)

```
GET /oauth/callback?code=auth_code_123&state=abc123xyz789&scope=email+profile
```

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| code | string | Yes | Authorization code from provider |
| state | string | Yes | State parameter for CSRF protection |
| scope | string | No | Granted scopes (space-delimited) |
| error | string | No | Error code if authorization failed |

#### Success Response (200 OK)

```json
{
  "success": true,
  "message": "Authentication successful",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
      "id": "uuid-string",
      "email": "<EMAIL>",
      "name": "John Doe",
      "profile_image": "https://lh3.googleusercontent.com/...",
      "is_new_user": true,
      "provider": "google"
    },
    "oauth_account": {
      "provider": "google",
      "provider_user_id": "google_123456789",
      "connected_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

#### Error Responses

##### Invalid State (400)
```json
{
  "success": false,
  "message": "Invalid or expired state parameter",
  "error": {
    "code": "INVALID_STATE",
    "details": "OAuth state validation failed"
  }
}
```

##### Provider Error (400)
```json
{
  "success": false,
  "message": "OAuth authorization failed",
  "error": {
    "code": "OAUTH_ERROR",
    "details": "access_denied: User denied access"
  }
}
```

##### Token Exchange Failed (500)
```json
{
  "success": false,
  "message": "Failed to exchange authorization code",
  "error": {
    "code": "TOKEN_EXCHANGE_FAILED",
    "details": "Provider token endpoint error"
  }
}
```

#### Implementation Details

##### Redis Operations (5-8 operations)

1. **State Validation**: `GET CreatorVerse:oauth:state:{state}` 
2. **State Cleanup**: `DEL CreatorVerse:oauth:state:{state}`
3. **Email Check**: `BF.EXISTS CreatorVerse:bloom:emails`
4. **User Session**: `SET CreatorVerse:user:session:{user_id}` (TTL: 86400s)
5. **Email Add**: `BF.ADD CreatorVerse:bloom:emails` (if new user)
6. **Cache User**: `SET CreatorVerse:user:profile:{user_id}` (TTL: 1800s)
7. **Provider Cache**: `SET CreatorVerse:oauth:profile:{provider}:{provider_user_id}` (TTL: 3600s)

##### Database Operations

###### New User Flow (4-5 DB operations)
1. **Create User**: `INSERT INTO users.users`
2. **Create OAuth Account**: `INSERT INTO users.oauth_accounts`
3. **Create User Session**: `INSERT INTO users.user_sessions`
4. **Store Social Profile**: `INSERT INTO users.social_profiles`
5. **User Role Assignment**: `INSERT INTO users.user_roles` (if applicable)

###### Existing User Flow (2-3 DB operations)
1. **Update OAuth Account**: `UPDATE users.oauth_accounts` (tokens, scope)
2. **Create User Session**: `INSERT INTO users.user_sessions`
3. **Update Social Profile**: `UPDATE users.social_profiles` (if changed)

##### Database Tables Modified

| Table | Operation | Purpose | Data Stored |
|-------|-----------|---------|-------------|
| `users.users` | INSERT/SELECT | User account | Basic user info from OAuth |
| `users.oauth_accounts` | INSERT/UPDATE | OAuth linkage | Provider tokens, user mapping |
| `users.social_profiles` | INSERT/UPDATE | Social data | Profile info, follower counts |
| `users.user_sessions` | INSERT | Login session | JWT session tracking |
| `users.user_roles` | INSERT | User permissions | Default role assignment |

---

## Redis Keys Reference

### OAuth Flow Keys

| Redis Key Pattern | Type | TTL | Purpose | Data Structure |
|-------------------|------|-----|---------|----------------|
| `CreatorVerse:oauth:state:{state}` | String | 600s | CSRF protection | `{"provider": "google", "code_verifier": "xyz", "redirect_uri": "...", "created_at": timestamp}` |
| `CreatorVerse:oauth:rate:{ip}` | Counter | 3600s | Rate limiting | Request count per IP |
| `CreatorVerse:oauth:profile:{provider}:{id}` | Hash | 3600s | Provider profile cache | User profile from OAuth provider |

### User Session Keys

| Redis Key Pattern | Type | TTL | Purpose | Data Structure |
|-------------------|------|-----|---------|----------------|
| `CreatorVerse:user:session:{user_id}` | Hash | 86400s | Active sessions | `{"access_token": "...", "issued_at": timestamp, "ip": "...", "user_agent": "..."}` |
| `CreatorVerse:user:profile:{user_id}` | String | 1800s | User profile cache | JSON user profile data |

### Email Tracking Keys

| Redis Key Pattern | Type | TTL | Purpose | Data Structure |
|-------------------|------|-----|---------|----------------|
| `CreatorVerse:bloom:emails` | Bloom Filter | Permanent | Email existence | Probabilistic set of emails |

---

## Configuration & Control Points

### OAuth Provider Configuration

#### File: `app/core/oauth_config.py`

```python
OAUTH_PROVIDERS = {
    "google": {
        "client_id": APP_CONFIG.google_client_id,
        "client_secret": APP_CONFIG.google_client_secret,
        "authorize_url": "https://accounts.google.com/oauth/authorize",
        "token_url": "https://oauth2.googleapis.com/token",
        "user_info_url": "https://www.googleapis.com/oauth2/v2/userinfo",
        "scopes": ["email", "profile"]
    },
    "facebook": {
        "client_id": APP_CONFIG.facebook_app_id,
        "client_secret": APP_CONFIG.facebook_app_secret,
        "authorize_url": "https://www.facebook.com/v18.0/dialog/oauth",
        "token_url": "https://graph.facebook.com/v18.0/oauth/access_token",
        "user_info_url": "https://graph.facebook.com/v18.0/me",
        "scopes": ["email", "public_profile"]
    }
}
```

### Security Controls

| Setting | File | Variable | Purpose |
|---------|------|----------|---------|
| State TTL | `app/core/oauth_config.py` | `OAUTH_STATE_TTL` | CSRF token lifetime |
| PKCE Enabled | `app/core/oauth_config.py` | `OAUTH_USE_PKCE` | Enhanced security |
| Allowed Redirects | `app/core/oauth_config.py` | `ALLOWED_REDIRECT_URIS` | Redirect URI validation |
| Rate Limits | `app/core/oauth_config.py` | `OAUTH_RATE_LIMIT` | Requests per hour |

### Error Handling

#### File: `app/services/oauth_service.py`

- **Token Exchange Retry**: 3 attempts with exponential backoff
- **Profile Fetch Timeout**: 10 seconds timeout
- **State Validation**: Cryptographically secure random state
- **Provider Error Mapping**: Standardized error responses

---

## Performance Optimizations

### Caching Strategy
- **Provider Profile Cache**: 1-hour TTL for user profile data
- **State Storage**: Short-lived Redis keys for CSRF protection
- **Bloom Filter**: Fast email existence checks
- **Session Cache**: 24-hour TTL for active sessions

### Database Optimizations
- **Batch Operations**: Multiple inserts in single transaction
- **Index Usage**: OAuth account lookups use provider + provider_user_id index
- **Connection Pooling**: Reused database connections

### External API Optimizations
- **HTTP Timeouts**: 10-second timeout for provider APIs
- **Retry Logic**: Exponential backoff for failed requests
- **Connection Reuse**: HTTP client connection pooling

---

## Security Features

### CSRF Protection
- **State Parameter**: Cryptographically random 32-byte state
- **State Validation**: Server-side state verification
- **State Expiry**: 10-minute TTL prevents replay attacks

### PKCE (Proof Key for Code Exchange)
- **Code Challenge**: SHA256 hash of code verifier
- **Code Verifier**: Cryptographically random 128-byte string
- **Challenge Validation**: Provider validates code_verifier

### Token Security
- **Secure Storage**: OAuth tokens encrypted in database
- **Token Rotation**: Refresh tokens rotated on each use
- **Scope Validation**: Requested scopes validated against permissions

### Rate Limiting
- **IP-based Limits**: 100 requests per hour per IP
- **User-based Limits**: 20 OAuth flows per hour per user
- **Provider Limits**: Respect provider rate limits

---

## Monitoring & Logging

### Key Metrics
- **OAuth Success Rate**: Percentage of successful authentications
- **Provider Response Times**: Average response time per provider
- **Error Rates**: Failed authentications by error type
- **New User Registrations**: OAuth-based user acquisition

### Audit Logging
- **Authentication Events**: All OAuth attempts logged
- **Token Operations**: Token creation, refresh, revocation
- **Profile Updates**: Changes to user profiles from OAuth
- **Security Events**: Failed state validation, rate limit hits

This comprehensive OAuth flow ensures secure, scalable social authentication with proper error handling, caching, and monitoring capabilities.
