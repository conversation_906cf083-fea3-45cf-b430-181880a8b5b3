# Brand Membership Request API Documentation

**Base Path:** `/v1/brand/membership`  
**Description:** Manages brand membership requests using the membership table with "requested" status, requiring approval from brand-admin or organization-admin.

## API Endpoints Overview

1. **POST /membership/request** - Create brand membership request
2. **GET /membership/requests** - List pending requests (admin only)
3. **POST /membership/requests/{id}/accept** - Accept membership request (admin only)
4. **POST /membership/requests/{id}/reject** - Reject membership request (admin only)

## Complete Membership Flow Chart

```
┌─────────────────┐
│ User Registers  │
│ via OTP Flow    │ Creates user + organization
│                 │ NO brand membership created
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ POST            │ User requests access to specific brand
│ /membership/    │ Redis: Check existing membership
│ request         │ DB: Insert membership with status="requested"
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Membership      │ Stored in membership table:
│ Record Created  │ - status: "requested"
│ Status:         │ - role: requested role
│ "requested"     │ - brand_id: target brand
└────────┬────────┘ - user_id: requesting user
         │
         ▼
┌─────────────────┐
│ Admin Views     │ GET /membership/requests?brand_id=xyz
│ Pending         │ Redis: Cache pending requests
│ Requests        │ DB: Query memberships with status="requested"
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Admin Decision  │ POST /membership/requests/{id}/accept
│ Accept/Reject   │ or POST /membership/requests/{id}/reject
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│ Membership      │ Update membership table:
│ Status Updated  │ - status: "active" or "rejected"
│                 │ - approved_by/rejected_by: admin_user_id
└─────────────────┘ - approved_at/rejected_at: timestamp
```

## Detailed API Documentation

### 1. POST /membership/request

**Description:** Create a membership request for a specific brand

#### Request

```json
{
  "brand_id": "550e8400-e29b-41d4-a716-************",
  "requested_role": "brand-user",
  "message": "I would like to contribute to this brand's social media strategy"
}
```

#### Request Parameters

| Parameter | Type | Required | Description | Validation |
|-----------|------|----------|-------------|------------|
| brand_id | string | Yes | UUID of target brand | Valid UUID, brand must exist |
| requested_role | string | Yes | Role being requested | Enum: brand-user, brand-admin |
| message | string | No | Message to brand admins | Max 500 characters |

#### Response (200 OK)

```json
{
  "success": true,
  "data": {
    "id": "membership-request-uuid",
    "brand_id": "550e8400-e29b-41d4-a716-************",
    "brand_name": "Awesome Brand",
    "status": "requested",
    "requested_role": "brand-user",
    "message": "Membership request created successfully"
  }
}
```

#### Error Responses

##### Already Member (400)
```json
{
  "success": false,
  "message": "User already member of this brand",
  "error": {
    "code": "ALREADY_MEMBER",
    "details": "Active membership exists"
  }
}
```

##### Pending Request Exists (400)
```json
{
  "success": false,
  "message": "Membership request already pending",
  "error": {
    "code": "REQUEST_PENDING",
    "details": "Please wait for admin approval"
  }
}
```

#### Implementation Details

##### Redis Operations (4-5 operations)
1. **Check Existing**: `GET CreatorVerse:membership:user:{user_id}:brand:{brand_id}`
2. **Brand Validation**: `GET CreatorVerse:brand:details:{brand_id}`
3. **Cache Invalidation**: `DEL CreatorVerse:brand:requests:{brand_id}`
4. **Membership Cache**: `SET CreatorVerse:membership:user:{user_id}:brand:{brand_id}`

##### Database Operations (2 operations)
1. **Brand Verification**: `SELECT from brands WHERE id = ?`
2. **Create Membership**: `INSERT INTO memberships (user_id, brand_id, organization_id, role, status, requested_at, request_message)`

---

### 2. GET /membership/requests

**Description:** List pending membership requests for a brand (admin access only)

#### Request (Query Parameters)

```
GET /membership/requests?brand_id=550e8400-e29b-41d4-a716-************
```

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| brand_id | string | Yes | Brand UUID to get requests for |

#### Response (200 OK)

```json
{
  "success": true,
  "data": [
    {
      "id": "request-uuid-1",
      "user_id": "user-uuid-1",
      "user_email": "<EMAIL>",
      "user_name": "John Doe",
      "requested_role": "brand-user",
      "request_message": "I would like to join this brand",
      "requested_at": "2024-01-15T10:30:00Z"
    },
    {
      "id": "request-uuid-2", 
      "user_id": "user-uuid-2",
      "user_email": "<EMAIL>",
      "user_name": "Jane Smith",
      "requested_role": "brand-admin",
      "request_message": null,
      "requested_at": "2024-01-15T11:45:00Z"
    }
  ]
}
```

#### Authorization

**Required Roles:**
- `brand-admin` for the specific brand
- `organization-admin` for the brand's organization

#### Implementation Details

##### Redis Operations (3 operations)
1. **Permission Check**: `GET CreatorVerse:brand:admin:{user_id}:brand:{brand_id}`
2. **Cached Requests**: `GET CreatorVerse:brand:requests:{brand_id}`
3. **Cache Update**: `SET CreatorVerse:brand:requests:{brand_id}` (if cache miss)

##### Database Operations (1-2 operations)
1. **Permission Verification**: Check user admin role in membership table
2. **Fetch Requests**: `SELECT m.*, u.email, u.name FROM memberships m JOIN users u WHERE m.brand_id = ? AND m.status = 'requested'`

---

### 3. POST /membership/requests/{request_id}/accept

**Description:** Accept a pending membership request and activate the membership

#### Request

```
POST /membership/requests/request-uuid-123/accept
```

#### Response (200 OK)

```json
{
  "success": true,
  "message": "Membership request accepted",
  "data": {
    "membership_id": "request-uuid-123",
    "user_id": "user-uuid",
    "brand_id": "brand-uuid",
    "role": "brand-user",
    "status": "active"
  }
}
```

#### Implementation Details

##### Redis Operations (6-8 operations)
1. **Get Request**: `GET CreatorVerse:membership:id:{request_id}`
2. **Permission Check**: `GET CreatorVerse:brand:admin:{admin_id}:brand:{brand_id}`
3. **Cache Invalidations**: Multiple DEL operations for affected caches

##### Database Operations (1 operation)
1. **Update Membership**: `UPDATE memberships SET status='active', approved_by=?, approved_at=? WHERE id=?`

---

### 4. POST /membership/requests/{request_id}/reject

**Description:** Reject a pending membership request

#### Request

```
POST /membership/requests/request-uuid-123/reject
```

#### Response (200 OK)

```json
{
  "success": true,
  "message": "Membership request rejected"
}
```

#### Implementation Details

##### Redis Operations (6-8 operations)
1. **Get Request**: `GET CreatorVerse:membership:id:{request_id}`
2. **Permission Check**: `GET CreatorVerse:brand:admin:{admin_id}:brand:{brand_id}`
3. **Cache Invalidations**: Multiple DEL operations for affected caches

##### Database Operations (1 operation)
1. **Update Membership**: `UPDATE memberships SET status='rejected', rejected_by=?, rejected_at=? WHERE id=?`

---

## Redis Keys Reference

### Membership Keys

| Redis Key Pattern | Type | TTL | Purpose | Data Structure |
|-------------------|------|-----|---------|----------------|
| `CreatorVerse:membership:user:{user_id}:brand:{brand_id}` | String | 1800s | User-brand membership status | `{"id": "...", "status": "requested", "role": "brand-user", "created_at": "..."}` |
| `CreatorVerse:brand:requests:{brand_id}` | String | 300s | Pending requests list | JSON array of request objects |
| `CreatorVerse:membership:id:{membership_id}` | String | 1800s | Membership by ID | Complete membership object JSON |

### Permission Keys

| Redis Key Pattern | Type | TTL | Purpose | Data Structure |
|-------------------|------|-----|---------|----------------|
| `CreatorVerse:brand:admin:{user_id}:brand:{brand_id}` | String | 900s | Admin permission cache | `{"is_admin": true, "roles": ["brand-admin"]}` |
| `CreatorVerse:user:memberships:{user_id}` | String | 1800s | User's all memberships | JSON array of membership objects |

---

## Database Schema

### Membership Table Fields

| Field | Type | Description | Index |
|-------|------|-------------|-------|
| id | UUID | Primary key | PK |
| user_id | UUID | Foreign key to users | FK, Index |
| brand_id | UUID | Foreign key to brands | FK, Index |
| organization_id | UUID | Foreign key to organizations | FK, Index |
| role | VARCHAR(50) | User role in brand | Index |
| status | VARCHAR(20) | Membership status | Index |
| requested_at | TIMESTAMP | When request was made | Index |
| request_message | TEXT | User's request message | - |
| approved_at | TIMESTAMP | When request was approved | - |
| approved_by | UUID | Admin who approved | FK |
| rejected_at | TIMESTAMP | When request was rejected | - |
| rejected_by | UUID | Admin who rejected | FK |

### Status Values

| Status | Description | Next Possible States |
|--------|-------------|---------------------|
| requested | Initial request state | active, rejected |
| active | Approved membership | inactive, removed |
| rejected | Declined request | - (terminal) |
| inactive | Suspended membership | active, removed |
| removed | Removed from brand | - (terminal) |

---

## Security & Authorization

### Permission Matrix

| Action | Required Role | Scope | Validation |
|--------|--------------|-------|------------|
| Create Request | Any authenticated user | Self only | User cannot request for others |
| View Requests | brand-admin, organization-admin | Brand/Org level | Admin role for specific brand/org |
| Accept Request | brand-admin, organization-admin | Brand/Org level | Admin role for specific brand/org |
| Reject Request | brand-admin, organization-admin | Brand/Org level | Admin role for specific brand/org |

### Rate Limiting

| Endpoint | Limit | Scope | Reset Period |
|----------|-------|-------|--------------|
| POST /request | 5 requests | Per user per brand | 24 hours |
| GET /requests | 100 requests | Per user | 1 hour |
| Accept/Reject | 50 requests | Per admin | 1 hour |

---

## Error Handling

### Common Error Codes

| Code | HTTP Status | Description | Resolution |
|------|-------------|-------------|------------|
| ALREADY_MEMBER | 400 | User already has active membership | Use existing membership |
| REQUEST_PENDING | 400 | Request already exists | Wait for admin action |
| BRAND_NOT_FOUND | 404 | Brand doesn't exist | Verify brand ID |
| INSUFFICIENT_PERMISSIONS | 403 | User lacks admin rights | Get admin privileges |
| REQUEST_NOT_FOUND | 404 | Membership request not found | Verify request ID |
| INVALID_STATUS | 400 | Request already processed | Check current status |

### Rollback Scenarios

#### Request Creation Failure
```sql
-- If membership insert fails, no cleanup needed
-- Redis caches remain consistent
```

#### Accept/Reject Failure
```sql
-- Database transaction rollback
BEGIN;
UPDATE memberships SET status=?, approved_by=?, approved_at=? WHERE id=?;
-- If error occurs, rollback
ROLLBACK;
```

---

## Performance Optimizations

### Caching Strategy
- **Request Lists**: 5-minute cache for pending requests
- **Membership Status**: 30-minute cache for user-brand relationships
- **Admin Permissions**: 15-minute cache for permission checks
- **Brand Details**: 1-hour cache for brand information

### Database Optimizations
- **Compound Indexes**: (user_id, brand_id, status) for fast lookups
- **Status Filtering**: Efficient queries using status index
- **Join Optimization**: Minimized joins in request listing queries

### Monitoring Points
- **Request Creation Rate**: Track membership request volume
- **Approval Rate**: Monitor admin response times
- **Cache Hit Rate**: Optimize caching strategies
- **Permission Check Latency**: Monitor authorization performance

This comprehensive membership request system ensures secure, scalable brand access management with proper admin controls and audit trails.
