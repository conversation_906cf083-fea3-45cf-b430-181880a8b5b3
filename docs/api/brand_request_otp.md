# Brand Registration OTP Request API

## Endpoint Details
- **URL**: `/api/v1/brand/request-otp`
- **Method**: `POST`
- **Handler**: `request_brand_otp` in `app/api/api_v1/endpoints/auth_brands.py`
- **Description**: Sends a one-time password (OTP) to the provided email for brand registration verification.

## Flowchart

```
┌─────────────────────┐
│ Receive OTP Request │
└──────────┬──────────┘
           ▼
┌─────────────────────────┐
│ Validate Email Domain   │
│ (Block Consumer Domains)│
└──────────┬──────────────┘
           ▼
┌─────────────────────────┐    Yes    ┌───────────────────────┐
│ Check If Email Exists   ├──────────►│ Return 400 Bad Request│
│ (Bloom Filter)          │           │ "Email Already Exists"│
└──────────┬──────────────┘           └───────────────────────┘
           │ No
           ▼
┌─────────────────────────┐    No     ┌───────────────────────┐
│ Check OTP Resend Status ├──────────►│ Return "Wait Before   │
│ (30-second cooldown)    │           │ Requesting New OTP"   │
└──────────┬──────────────┘           └───────────────────────┘
           │ Can Send
           ▼
┌─────────────────────────┐    No     ┌───────────────────────┐
│ Verify Email Existence  ├──────────►│ Return 400 Bad Request│
│ (MX Lookup - Parallel)  │           │ "Invalid Email"       │
└──────────┬──────────────┘           └───────────────────────┘
           │ Valid
           ▼
┌─────────────────────────┐
│ Generate & Store OTP    │
│ (Centralized OTP Service│
│  with Redis)            │
└──────────┬──────────────┘
           ▼
┌─────────────────────────┐
│ Send OTP to Email       │
└──────────┬──────────────┘
           ▼
┌─────────────────────────┐
│ Return Success Response │
└─────────────────────────┘
```

## Request Format

```json
{
  "email": "<EMAIL>",
  "role_uuid": "brand-user-role-uuid",
  "register_source": "web",
  "register_source_name": "website"
}
```

## Response Format

### Success Response (200 OK)
```json
{
  "message": "OTP sent successfully. Please verify your email with the OTP sent.",
  "email": "<EMAIL>"
}
```

### Error Responses

**400 Bad Request** (Email Already Exists):
```json
{
  "detail": "Email already exists in the system. Please use login instead."
}
```

**400 Bad Request** (Invalid Email):
```json
{
  "detail": "Invalid email: [reason]"
}
```

**429 Too Many Requests** (Rate Limited):
```json
{
  "message": "Please wait 25 seconds before requesting a new OTP.",
  "email": "<EMAIL>"
}
```

**500 Internal Server Error**:
```json
{
  "detail": "Failed to send brand registration OTP. Please try again."
}
```

## Database Operations
- **No direct database calls** in this endpoint
- Email existence check uses Redis-based Bloom filter, not database queries

## Redis Operations
1. **Bloom Filter Check** (`check_email_exists`):
   - Checks if email already exists in the system
   - Key Pattern: Redis Bloom Filter (implementation-specific)
   - File: `app/utilities/bloom_filter.py`

2. **OTP Resend Status Check** (`_check_otp_resend_status`):
   - Enforces 30-second cooldown between OTP requests
   - Key Pattern: `CreatorVerse:otp:cooldown:{email}`
   - File: `app/services/centralized_otp_service.py`

3. **OTP Storage** (`request_otp`):
   - Stores the OTP with user details
   - Key Pattern: `CreatorVerse:otp:{email}`
   - File: `app/services/centralized_otp_service.py`
   - TTL: Typically 10 minutes (600 seconds)

## External Service Calls
- **Email Verification** via MX lookup (Parallel operation)
  - Function: `verify_email_exists_parallel`
  - File: `app/utilities/validation_functions.py`

## Control Parameters
- **OTP Length**: Configured in CentralizedOTPService
- **OTP TTL**: Configured in CentralizedOTPService (default 10 minutes)
- **Cooldown Period**: Configured in CentralizedOTPService (default 30 seconds)
- **Blocked Consumer Domains**: Defined in `app/api/deps.py` as `BLOCKED_CONSUMER_DOMAINS`

## Security Considerations
1. **Domain Validation**: Prevents registrations from consumer domains like gmail.com
2. **Rate Limiting**: 30-second cooldown between OTP requests
3. **Bloom Filter**: Efficiently prevents duplicate registrations
4. **Email Verification**: Validates email domain existence before sending OTP

## Related Files
- `app/api/api_v1/endpoints/auth_brands.py` - Main API handler
- `app/services/centralized_otp_service.py` - OTP generation and verification
- `app/utilities/bloom_filter.py` - Email existence checking
- `app/utilities/validation_functions.py` - Email validation
- `app/utilities/email_service.py` - Email sending service
- `app/templates/email_templates.py` - Email template definitions
- `app/schemas/auth_brand.py` - Request/response schemas

## Monitoring and Logging
- All OTP send operations are logged via `APP_CONFIG.logger`
- Success and failure events are tracked
- Log entries include user email (for troubleshooting)
