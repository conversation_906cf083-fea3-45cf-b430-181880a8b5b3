# Brand Authentication & Management Flow in CreatorVerse Backend

## Core Authentication Flow

### Step 1: Request OTP
**Endpoint**: POST /v1/auth/login/email
- Request: `LoginRequest` with email and role_uuid
- System Actions:
  1. Domain validation (blocks consumer domains for brand roles)
  2. Email existence check via Bloom filter
  3. OTP status check (prevents spam)
  4. Generate and store OTP
- Redis Operations:
  - Check/Store OTP: `CreatorVerse:otp:{email}`
  - Bloom filter check: `CreatorVerse:email:bloom`
- Response: `OtpResponse` with status message

### Step 2: Verify OTP
**Endpoint**: POST /v1/auth/verify-login-otp
- Request: `OtpRequest` with email and OTP
- System Actions:
  1. Validate OTP from Redis
  2. Create/Update user session within transaction
  3. Generate JWT tokens (access + refresh)
- Redis Operations:
  - Delete OTP: `CreatorVerse:otp:{email}`
  - Store session: `CreatorVerse:session:{user_id}:{session_id}`
  - Store tokens: `CreatorVerse:refresh:{user_id}:{token}`
- Database Operations:
  - Update user status in users table
  - Create record in user_sessions table
- Response: `OAuthTokenResponse` with tokens

## Brand Organization Management

### Step 1: Create Organization
**Endpoint**: POST /v1/brand/auth/organizations
- Request: `OrganizationCreateRequest` with domain and org details
- System Actions:
  1. Verify domain claim exists
  2. Check for duplicate org codes
  3. Create organization record
  4. Create owner membership
- Database Operations:
  - Insert into organizations table
  - Insert into organization_memberships table
  - Update domain_claims table
- Redis Operations:
  - Invalidate org cache: `CreatorVerse:domain:orgs:{domain}`
  - Invalidate user roles: `CreatorVerse:rbac:user:{user_id}`

### Step 2: Join Organization
**Endpoint**: POST /v1/brand/auth/organizations/{org_id}/join
- Request: Organization ID in path
- System Actions:
  1. Verify organization exists and is active
  2. Add user as member
  3. Update cache
- Database Operations:
  - Insert into organization_memberships table
- Redis Operations:
  - Invalidate org members cache
  - Update user roles cache

### Step 3: Transfer Ownership
**Endpoint**: POST /v1/brand/auth/organizations/{org_id}/transfer-owner
- Request: `OwnershipTransferRequest` with target user ID
- System Actions:
  1. Verify current user is owner
  2. Update memberships
  3. Record transfer
- Database Operations:
  - Update organization_memberships table
  - Insert into ownership_transfers table
- Redis Operations:
  - Invalidate role caches for both users

## Cache-Aside Patterns

### Brand Profile Loading
**Key**: `CreatorVerse:user:profile:{user_id}`
- TTL: 30 minutes
- Cached Data:
  - User details
  - Organization memberships
  - Organization details
- Invalidation:
  - On membership changes
  - On organization updates

### Organization Members
**Key**: `CreatorVerse:org:members:{org_id}`
- TTL: 15 minutes
- Cached Data:
  - Member details
  - Membership roles
  - Join dates
- Invalidation:
  - On member join/leave
  - On role changes

## RBAC (Role-Based Access Control)
**Redis Keys**:
- Roles: `CreatorVerse:rbac:roles`
- User Roles: `CreatorVerse:rbac:user:{user_id}`
- Role Permissions: `CreatorVerse:rbac:role:{role_id}`

**Operations Per Login**:
- Redis: 8-12 operations (varies with cache hits)
- Database: 3-7 queries (varies with cache hits)

## Domain Verification Flow
- Token Generation: On organization creation
- Verification: GET /v1/brand/auth/domains/verify
- Cache Invalidation: On successful verification

## Token Management Flow

### Logout Process
**Endpoint**: POST /v1/auth/logout
- Request: `LogoutRequest` with refresh_token
- System Actions:
  1. Validate current user matches refresh token
  2. Delete session and token data
  3. Revoke database session record
- Redis Operations:
  - Delete session: `CreatorVerse:session:{user_id}:{session_id}`
  - Delete refresh token: `CreatorVerse:refresh:{user_id}:{token}`
  - Delete user roles: `CreatorVerse:rbac:user:{user_id}`
- Database Operations:
  - Update user_sessions table to mark session as revoked
- Response: Success message

### Token Refresh Process
**Endpoint**: POST /v1/auth/token/refresh
- Request: Current refresh token
- System Actions:
  1. Validate refresh token in database
  2. Create new session with rotated tokens
  3. Update Redis and database atomically
- Security Measures:
  - Token rotation on each refresh
  - Session ID changes with each refresh
  - Previous tokens are immediately invalidated
- Redis Operations:
  1. Delete old session: `CreatorVerse:session:{user_id}:{old_session_id}`
  2. Delete old refresh: `CreatorVerse:refresh:{user_id}:{old_token}`
  3. Create new session: `CreatorVerse:session:{user_id}:{new_session_id}`
  4. Store new refresh: `CreatorVerse:refresh:{user_id}:{new_token}`
- Database Operations:
  - Update user_sessions table with new tokens and expiry
- Response: `OAuthTokenResponse` with new tokens

### Session Management
- **Session Storage**:
  - Redis TTL: 7 days (matches refresh token)
  - Database: Permanent record for audit
- **Token Hierarchy**:
  - Access Token: 30 minutes validity
  - Refresh Token: 7 days validity
  - Session ID: Changes on each refresh
- **Cache Invalidation**:
  - On logout: All related session keys
  - On refresh: Old session and token keys
  - On security events: All user sessions

### Security Considerations
- **Token Rotation**:
  - New tokens on each refresh
  - Old tokens immediately invalidated
  - Grace period for concurrent requests
- **Session Tracking**:
  - IP address and user agent stored
  - Last activity timestamp updated
  - Revocation status tracked
- **Cache Consistency**:
  - Redis pipeline for atomic operations
  - Database transaction for permanent storage
  - Fallback mechanisms for cache failures

## Admin Dashboard Guidelines

### Core Brand Data Verification

1. **Organization Status Checks**
```sql
-- Check organization health
SELECT o.id, o.name, o.domain,
       o.total_members,
       COUNT(om.user_id) as actual_members,
       o.is_active,
       o.created_at
FROM users.organizations o
LEFT JOIN users.organization_memberships om 
  ON o.id = om.organization_id
GROUP BY o.id
HAVING o.total_members != COUNT(om.user_id);

-- Find orphaned organizations (no owner)
SELECT o.* 
FROM users.organizations o
LEFT JOIN users.organization_memberships om 
  ON o.id = om.organization_id 
  AND om.role = 'owner'
WHERE om.user_id IS NULL;
```

2. **Domain Verification Health**
```sql
-- Find unverified domains older than 24h
SELECT dc.domain, dc.claimed_by_user_id,
       dc.created_at, NOW() - dc.created_at as age,
       u.email as claimer_email
FROM users.domain_claims dc
JOIN users.users u ON dc.claimed_by_user_id = u.id
WHERE dc.verified_at IS NULL 
AND dc.created_at < NOW() - INTERVAL '24 hours';

-- Check domain claim consistency
SELECT dc.domain, 
       COUNT(DISTINCT o.id) as org_count,
       STRING_AGG(o.name, ', ') as org_names
FROM users.domain_claims dc
JOIN users.organizations o ON dc.domain = o.domain
GROUP BY dc.domain
HAVING COUNT(DISTINCT o.id) > 1;
```

3. **Role & Membership Audit**
```sql
-- Audit organization roles distribution
SELECT o.name as organization,
       om.role,
       COUNT(*) as member_count
FROM users.organizations o
JOIN users.organization_memberships om ON o.id = om.organization_id
GROUP BY o.id, o.name, om.role
ORDER BY o.name, om.role;

-- Find organizations exceeding max_admins
SELECT o.name, o.max_admins,
       COUNT(*) as current_admins
FROM users.organizations o
JOIN users.organization_memberships om 
  ON o.id = om.organization_id
WHERE om.role = 'admin'
GROUP BY o.id, o.name, o.max_admins
HAVING COUNT(*) > o.max_admins;
```

### Cache Management

1. **Organization Cache Keys**
```bash
# List all organization caches
redis-cli --scan "CreatorVerse:domain:orgs:*"

# Check members cache
redis-cli --scan "CreatorVerse:org:members:*"

# Monitor cache hit rates
redis-cli INFO | grep cache_hit
```

2. **Cache Invalidation Events**
- Organization Updates:
  ```python
  cache_keys = [
      RedisKeys.organization_key(domain),
      f"CreatorVerse:domain:orgs:{domain}"
  ]
  ```
- Membership Changes:
  ```python
  cache_keys = [
      RedisKeys.organization_members(org_id),
      RedisKeys.rbac_user_roles(user_id)
  ]
  ```

### Common Issues & Resolution

1. **Domain Verification Issues**
```sql
-- Find stuck verifications
SELECT dc.*, u.email
FROM users.domain_claims dc
JOIN users.users u ON dc.claimed_by_user_id = u.id
WHERE dc.verified_at IS NULL
ORDER BY dc.created_at ASC;

-- Resolution Steps:
-- 1. Check verification_token validity
-- 2. Verify email delivery logs
-- 3. Consider manual verification if legitimate
UPDATE users.domain_claims 
SET verified_at = NOW() 
WHERE domain = 'example.com';
```

2. **Organization Member Sync**
```sql
-- Fix member count mismatches
UPDATE users.organizations o
SET total_members = (
    SELECT COUNT(*) 
    FROM users.organization_memberships om
    WHERE om.organization_id = o.id
    AND om.status = 'active'
)
WHERE EXISTS (
    SELECT 1
    FROM users.organization_memberships om2
    WHERE om2.organization_id = o.id
    GROUP BY om2.organization_id
    HAVING COUNT(*) != o.total_members
);
```

3. **Ownership Transfer Audit**
```sql
-- Track ownership changes
SELECT ot.*,
       u1.email as from_email,
       u2.email as to_email,
       o.name as organization_name
FROM users.ownership_transfers ot
JOIN users.users u1 ON ot.from_user_id = u1.id
JOIN users.users u2 ON ot.to_user_id = u2.id
JOIN users.organizations o ON ot.organization_id = o.id
ORDER BY ot.transferred_at DESC;
```

### Monitoring Metrics

1. **Organization Health**
```sql
SELECT 
    COUNT(*) as total_orgs,
    COUNT(*) FILTER (WHERE is_active) as active_orgs,
    AVG(total_members) as avg_members,
    MAX(total_members) as max_members
FROM users.organizations;
```

2. **Domain Verification Stats**
```sql
SELECT 
    COUNT(*) as total_claims,
    COUNT(*) FILTER (WHERE verified_at IS NOT NULL) as verified_claims,
    AVG(EXTRACT(EPOCH FROM (verified_at - created_at)))/3600 as avg_verification_hours
FROM users.domain_claims;
```

3. **Member Activity**
```sql
SELECT 
    DATE_TRUNC('day', joined_at) as join_date,
    COUNT(*) as new_members
FROM users.organization_memberships
GROUP BY DATE_TRUNC('day', joined_at)
ORDER BY join_date DESC;
```

### Emergency Response Procedures

1. **Domain Verification Emergency**
```sql
-- Temporarily disable verification requirement
UPDATE users.domain_claims
SET verified_at = NOW()
WHERE domain = 'affected-domain.com'
AND verified_at IS NULL;

-- Clean up pending verifications
DELETE FROM users.domain_claims
WHERE verified_at IS NULL
AND created_at < NOW() - INTERVAL '7 days';
```

2. **Organization Recovery**
```sql
-- Restore accidentally deactivated organization
UPDATE users.organizations
SET is_active = true,
    updated_at = NOW()
WHERE domain = 'affected-domain.com';

-- Restore member access
UPDATE users.organization_memberships
SET status = 'active',
    updated_at = NOW()
WHERE organization_id = 'affected-org-id';
```

3. **Cache Reset**
```bash
# Reset all organization caches
redis-cli DEL $(redis-cli KEYS "CreatorVerse:domain:orgs:*")

# Reset specific organization's caches
redis-cli DEL "CreatorVerse:org:members:{org_id}"
redis-cli DEL "CreatorVerse:domain:orgs:{domain}"
```






