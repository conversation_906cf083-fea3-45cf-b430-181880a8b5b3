# Disabling Profile Enrichment During OAuth Registration

## Issue Analysis

When a user initially registers with email and later signs up with Google OAuth using the same email, the system currently:

- Recognizes the existing user by email
- Updates the user's profile with Google data
- Adds Google OAuth as an authentication method

## Required Changes

We need to **disable profile enrichment during registration** while preserving the account linking functionality.

---

## Implementation Details

### 1. Modify OAuth Service Implementation

**File Location:** `oauth_service.py`

- **What to Change:**  
  Find the section in the `create_or_update_user_from_oauth` method that handles profile enrichment (YouTube data fetching) and modify it to skip this step.

### 2. Add Configuration Flag (Optional Enhancement)

**File Location:** `config.py`

- **What to Add:**  
  A configuration flag to control profile enrichment behavior.

- **Then:**  
  Modify the OAuth service to check this flag.

---

## Expected Behavior After Changes

When a user registers with email first, and later registers with Google OAuth using the same email, the system will:

- Recognize the existing user by email
- Update basic profile information (name, profile image)
- Add Google OAuth as a new authentication method
- Issue new tokens for the existing account

---

## Current Logic Preserved

The following key functionality remains unchanged:

- **User Recognition:** The system will still identify existing users by email
- **Account Linking:** OAuth accounts will still be linked to existing users
- **Token Issuance:** Authentication tokens will be issued properly
- **Session Management:** User sessions will be handled correctly

---

## Additional Considerations

- **Log Entries:** Add appropriate log messages to indicate profile enrichment is being skipped
- **User Experience:** Ensure the frontend handles the absence of YouTube data gracefully

---

## Testing Recommendations

- Test registering with email, then Google OAuth
- Verify no YouTube data is fetched during registration
- Confirm user authentication works with both methods
- Check that existing user data is properly maintained
- Verify proper OAuth account linking in the database

---

> These changes allow users to link their email and OAuth accounts while deferring profile enrichment to a later time.