# CreatorVerse Backend - User Operations Microservice

## 📋 Project Overview

**CreatorVerse Backend** is a specialized microservice within the larger CreatorVerse ecosystem, focused exclusively on user operations and management. Built with Python 3.11 and FastAPI, this service handles authentication, user profiles, social media integrations, and brand organization management.

### 🎯 Primary Purpose
- **User Management**: Complete user lifecycle management including registration, authentication, and profile management
- **Brand Operations**: Organization creation, brand-admin role assignment, and brand verification processes  
- **Social Integration**: Multi-platform social media account linking and profile synchronization
- **Authentication**: OAuth-based authentication with multiple providers and JWT token management

## 🏗️ Technical Architecture

### Core Technology Stack
- **Framework**: FastAPI (Python 3.11)
- **Package Manager**: uv (instead of pip)
- **Database**: SQLAlchemy ORM (no migration tools like Alembic)
- **Caching**: Redis with cache-aside pattern
- **Authentication**: OAuth 2.0 + JWT tokens
- **Validation**: Pydantic schemas
- **Type Safety**: MyPy compliance required
- **Code Quality**: Ruff for linting and static analysis
- **Logging**: Custom logger via APP_CONFIG

### Design Patterns
1. **Cache-Aside Pattern**: Implemented throughout for performance optimization
2. **Repository Pattern**: Database helpers abstract data access layer
3. **Dependency Injection**: FastAPI's built-in DI system
4. **Middleware Pattern**: Rate limiting and tenant validation
5. **Schema Validation**: Pydantic models for all API interactions

## 🚀 Key Features

### Authentication System
- **Multi-Provider OAuth**: Google, LinkedIn, Facebook integration
- **JWT Token Management**: Access/refresh token rotation with blacklisting
- **OTP Verification**: Phone/email-based verification system
- **Brand-Specific Auth**: Specialized authentication flow for brand accounts
- **Role-Based Access**: Automatic brand-admin assignment for first domain registration

### User Management
- **Profile Management**: Complete CRUD operations for user profiles
- **Social Profiles**: Multi-platform social media account integration
- **Account Status**: Active/inactive account management with proper state handling
- **Preference Management**: User settings and configuration storage

### Brand & Organization Management
- **Organization Creation**: Automatic organization setup for new brand domains
- **Role Assignment**: Dynamic role-based access control (RBAC)
- **Brand Verification**: Multi-step brand verification processes
- **Member Management**: Organization member invitation and management

## 🔌 API Endpoints

### Authentication Endpoints (`/api/v1/auth`)
```
POST   /auth/brands/oauth/{provider}     # Brand OAuth authentication
POST   /auth/brands/callback/{provider}  # OAuth callback handling
POST   /auth/influencers/oauth/{provider} # Influencer OAuth authentication
POST   /auth/verify-otp                  # OTP verification
POST   /auth/refresh-token               # Token refresh
POST   /auth/logout                      # User logout
```

### User Profile Endpoints (`/api/v1/user-profile`)
```
GET    /user-profile/me                  # Get current user profile
PUT    /user-profile/me                  # Update user profile
POST   /user-profile/preferences         # Update preferences
GET    /user-profile/status              # Get account status
```

### Social Profiles Endpoints (`/api/v1/social-profiles`)
```
GET    /social-profiles                  # Get linked social accounts
POST   /social-profiles/link             # Link new social account
DELETE /social-profiles/{platform}       # Unlink social account
PUT    /social-profiles/{platform}       # Update social profile
```

### Common Endpoints (`/api/v1/common`)
```
GET    /common/health                    # Health check
GET    /common/config                    # Public configuration
GET    /common/status                    # System status
```

## 🔐 Security Implementation

### JWT Token Security
- **Access Tokens**: Short-lived tokens for API access
- **Refresh Tokens**: Long-lived tokens for token renewal
- **Token Blacklisting**: Secure logout and token invalidation
- **Automatic Rotation**: Refresh tokens rotate on each use

### OAuth Security
- **State Parameter**: CSRF protection for OAuth flows
- **Secure Callbacks**: Validated callback URL handling
- **Profile Validation**: User data verification and sanitization
- **Account Linking**: Secure social account association

### Middleware Security
- **Rate Limiting**: IP-based and endpoint-specific limits
- **Tenant Validation**: Multi-tenant request validation
- **CORS Handling**: Proper cross-origin request management
- **Request Logging**: Comprehensive audit trail

## 💾 Database & Caching Strategy

### Database Design
- **SQLAlchemy ORM**: Type-safe database operations
- **No Migrations**: Manual schema management approach
- **Session Management**: Proper session boundaries to avoid detached instances
- **Connection Pooling**: Optimized database connection handling

### Cache-Aside Pattern Implementation
```python
# Standard pattern used throughout the codebase
def get_user_by_id_cache_aside(user_id: int, redis_client, db_conn):
    # 1. Try cache first
    cache_key = f"CreatorVerse:user:profile:{user_id}"
    cached_data = redis_client.get(cache_key)
    
    if cached_data:
        return parse_cached_data(cached_data)
    
    # 2. Query database if cache miss
    user_data = db_conn.query(User).filter(User.id == user_id).first()
    
    # 3. Update cache
    if user_data:
        redis_client.setex(cache_key, 3600, serialize_user(user_data))
    
    return user_data
```

### Redis Key Convention
All Redis keys follow the pattern: `CreatorVerse:feature:key`

Examples:
- `CreatorVerse:user:profile:{user_id}`
- `CreatorVerse:auth:tokens:{token_hash}`
- `CreatorVerse:social:profiles:{user_id}`
- `CreatorVerse:brand:organization:{org_id}`

## 📁 Project Structure

```
creatorverse_backend/
├── main.py                           # FastAPI application entry point
├── requirements.txt                 # Project dependencies
├── requirements.txt                  # Python dependencies
├── uv.lock                          # UV lock file
├── app/
│   ├── api/
│   │   ├── deps.py                  # Dependency injection
│   │   └── api_v1/
│   │       ├── api.py               # Main API router
│   │       └── endpoints/           # API endpoint implementations
│   ├── core/
│   │   ├── config.py               # APP_CONFIG and configuration management
│   │   ├── security.py             # JWT and security utilities
│   │   ├── redis_keys.py           # Redis key patterns
│   │   ├── enums_data.py           # Enumeration definitions
│   │   └── database_helper/        # Database operation helpers
│   ├── middleware/
│   │   ├── rate_limit.py           # Rate limiting middleware
│   │   └── tenant_middleware.py    # Tenant validation
│   ├── schemas/                    # Pydantic validation schemas
│   └── utilities/                  # Utility services and helpers
└── docs/                          # Project documentation
```

## 🛠️ Development Guidelines

### Code Quality Standards
1. **Type Safety**: All code must pass MyPy type checking
2. **Linting**: Use Ruff exclusively for linting and static analysis
3. **Naming Convention**: Follow proper Python naming conventions
4. **Reusable Functions**: Design for reusability and modularity
5. **Error Handling**: Comprehensive error handling with proper logging

### Database Operations
- **Session Management**: Always handle SQLAlchemy sessions properly
- **Cache-Aside**: Implement cache-aside pattern for all applicable functions
- **No Migrations**: Manual schema management, no Alembic usage
- **Connection Handling**: Use `get_database()` for DB connections

### Redis Usage
- **Key Patterns**: Always follow `CreatorVerse:feature:key` naming
- **Decoded Responses**: Use `redis_client` with decoded responses enabled
- **TTL Management**: Set appropriate expiration times for cached data
- **Bloom Filters**: Use for efficient negative lookups

### Authentication Best Practices
- **OAuth Integration**: Secure callback handling and state management
- **Token Management**: Proper JWT token lifecycle management
- **Role Assignment**: Automatic brand-admin role for first domain registration
- **Session Security**: Avoid detached SQLAlchemy instances

## 🚦 Getting Started

### Prerequisites
- Python 3.11
- UV package manager
- Redis server
- Database (PostgreSQL/MySQL)

### Installation
```bash
# Clone repository
git clone <repository-url>
cd creatorverse_backend

# Install dependencies
uv install

# Set up configuration
cp appsettings.example.json appsettings.json
# Configure your database and Redis connections
```

### Running the Application
```bash
# Development mode
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Production mode
uvicorn main:app --host 0.0.0.0 --port 8000
```

## 📊 Configuration Management

### APP_CONFIG Class
The centralized configuration is managed through `app.core.config.APP_CONFIG`:

```python
from app.core.config import APP_CONFIG

# Logger usage
APP_CONFIG.logger.info("Application started")

# Database connection
db_conn = get_database()

# Redis connection
redis_client = get_locobuzz_redis()
```

### Environment Configuration
- Configuration loaded from `appsettings.json`
- Environment-specific overrides supported
- Secure credential management
- Database and Redis connection strings

## 🔄 Cache-Aside Pattern Implementation

Every database operation follows the cache-aside pattern:

1. **Read Operations**: Check cache → Query DB if miss → Update cache
2. **Write Operations**: Update DB → Invalidate/update cache
3. **Delete Operations**: Delete from DB → Remove from cache

This ensures data consistency while maximizing performance through intelligent caching.

## 🎯 Key Business Logic

### Brand Registration Flow
1. **First-Time Domain**: If no organization exists for domain, create new organization
2. **Role Assignment**: Assign brand-admin role to first user from domain
3. **Organization Setup**: Initialize organization structure and permissions
4. **Verification Process**: Multi-step brand verification workflow

### User Authentication Flow
1. **OAuth Initiation**: Redirect to provider with secure state
2. **Callback Processing**: Validate state and exchange code for tokens
3. **Profile Enrichment**: Fetch and store user profile data
4. **Account Linking**: Link social accounts to existing user profiles
5. **JWT Generation**: Create access and refresh tokens

---

*This microservice is part of the larger CreatorVerse ecosystem, focused exclusively on user operations while maintaining clean separation of concerns and high performance through strategic caching.*
