Below is a concise field-by-field guide to what every table does, how it's used for influencers versus brands.
(Field names in bold, default values in italics.)

1. users.organizations
Purpose: Stores every brand/organization.

Column | Type | Notes
-------|------|-------
id | UUID PK | Generated with gen_random_uuid()
domain | varchar(255) | Used for domain-based discovery (acme.com)
organization_code | varchar(50) *''* | Optional short code, unique within domain
name | varchar(100) | Display name
description | varchar(250) | About / tagline
logo_url | varchar(2048) | CDN link to brand logo
contact_email | varchar(255) | Brand-wide contact
total_members | int *0* | Counter for total members
is_active | bool *true* | Soft switch for suspensions
max_admins | smallint *10* | Guard-rail for RBAC
timezone | varchar(50) | Default timezone
deleted_at | timestamptz | Soft delete
created_at | timestamptz | Creation timestamp
updated_at | timestamptz | Last update timestamp

2. users.users  
Purpose: Central user profile table for all users.

Column | Type | Notes
-------|------|-------
id | UUID PK | Generated with gen_random_uuid()
email | varchar(255) | Unique email address
name | varchar(255) | Display name
profile_image | text | Profile picture URL
status | varchar(20) *'requested'* | Lifecycle state
country_code | varchar(3) | Optional for phone
phone_number | varchar(15) | Optional phone number
is_email_verified | bool *false* | Email verification status
is_phone_verified | bool *false* | Phone verification status
is_active | bool *true* | Account status
deactivated_at | timestamptz | When account was disabled
metadata_json | jsonb *'{}'* | Extensible metadata
last_login_at | timestamptz | Last successful login
register_source | smallint | How user registered (OTP/OAuth)
created_at | timestamptz | Creation timestamp
updated_at | timestamptz | Last update timestamp

3. users.organization_memberships
Purpose: Links users to organizations with roles.

Column | Type | Notes
-------|------|-------
organization_id | UUID FK | References organizations
user_id | UUID FK | References users
role | varchar(20) *'member'* | owner/admin/member
status | varchar(20) *'active'* | pending/active
invited_by | UUID FK | Who sent the invite
joined_at | timestamptz | When user joined

4. users.domain_claims  
Purpose: Tracks domain ownership verification.

Column | Type | Notes
-------|------|-------
domain | varchar(255) PK | Domain being claimed
verification_token | varchar(255) | For verification
verified_at | timestamptz | When verified
claimed_by_user_id | UUID FK | First claiming user
organization_id | UUID FK | Associated organization

// ...existing documentation for other tables...

The schema supports both brand and influencer flows with shared authentication while keeping proper separation of concerns. Key relationships:

```sql
users.users                 ← All users (brands & influencers)
  ↓
  ├─ user_roles            ← Global roles
  ├─ organization_memberships  ← Brand staff only
  └─ social_profiles       ← Influencers only
```