# Influencer Authentication Flow Analysis

## Registration Flow 

### 1. Initial Registration (POST /v1/influencer/auth/register)
**Request Schema**: `InfluencerRegisterRequest`
```json
{
  "email": "<EMAIL>",
  "role_uuid": "uuid-string",
  "register_source": 1-5 (email/phone/google/youtube/instagram)
}
```

**Flow Steps**:
1. Email Normalization & Early Validation
   - Strips whitespace
   - Converts to lowercase
   - **NEW**: Blocks reserved/test/company domains (RFC 2606)
     - Reserved: example.com, test.com, localhost, etc.
     - Test: test.example, demo.com, sample.com, etc.
     - Company: locobuzz.com, locobuzz.in, etc.

2. **NEW**: Role Validation
   - Key: `CreatorVerse:rbac:roles`
   - Purpose: Validate role UUID exists and is allowed for influencer registration
   - Implementation: Cache-aside pattern fetching from Redis
   - Fallback: Default roles if Redis cache is unavailable
   - Result: Fails if role UUID is invalid or not an influencer role

3. Bloom Filter Check (Redis Operation #1)
   - Key: `CreatorVerse:bloom:email`
   - Purpose: Quick check if email exists
   - Implementation: Redis bitmap operations
   - Result: Fails if email found

4. OTP Status Check (Redis Operations #2-3)
   - Key: `CreatorVerse:otp:{email}`
   - Operations:
     - **NEW**: Uses Redis pipeline for optimized batch operations
     - HGETALL - Get existing OTP data
     - TTL - Check remaining time
   - Prevents OTP spam by checking if valid OTP exists

5. **NEW**: Optimized Email Validation
   - Environment-aware validation strategy
     - Development: Format + domain MX check only (99.9% faster)
     - Production: Full SMTP validation
   - Domain MX caching with Redis (95% faster for repeated domains)
   - Performance metrics tracking
   - Key: `CreatorVerse:email_validation:domain_mx:{domain}`
   - TTL: Configurable through APP_CONFIG.email_domain_cache_ttl

6. OTP Generation & Storage (Redis Operations #4-5)
   - Key: `CreatorVerse:otp:{email}`
   - **NEW**: Uses Redis pipeline for atomic operations
   - Operations:
     - HSET - Store OTP data:
       ```json
       {
         "email": "<EMAIL>",
         "otp_hash": "hashed_value",
         "generated_at": "timestamp",
         "failed_attempts": 0,
         "lockout_until": 0,
         "role": "role_uuid",
         "request_type": "register",
         "register_channel": "source_id",
         "source_register": "source_name"
       }
       ```
     - EXPIRE - Set TTL (15 minutes)
     - Optional rate limit tracking with INCR and EXPIRE

7. Email Service Call
   - Sends registration OTP email
   - Has retry mechanism (3 attempts with exponential backoff)
   - Cleanup on failure

### 2. OTP Verification (POST /v1/influencer/auth/register/verify-otp)
**Request Schema**: `InfluencerOtpRequest`
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Flow Steps**:
1. Email Normalization
   - Strips whitespace
   - Converts to lowercase

2. Bloom Filter Re-check (Redis Operation #1)
   - Ensures email still unique
   - **NEW**: Has error handling with graceful degradation

3. OTP Verification (Redis Operations #2-3)
   - Key: `CreatorVerse:otp:{email}`
   - Operations:
     - HGETALL - Get OTP data
     - Verify hash & expiry
     - Track failed attempts
     - Implements lockout after 3 failed attempts (5 minutes)

4. User Creation Transaction (Database Operations)
   ```sql
   -- Transaction starts
   INSERT INTO users.users (
     id, email, status, is_email_verified,
     register_source, created_at, updated_at
   ) VALUES (...);
   
   INSERT INTO users.user_roles (
     user_id, role_id, assigned_at
   ) VALUES (...);
   -- Transaction ends
   ```

5. Token Generation & Session Creation (Redis Operations #4-7)
   - Keys:
     - `CreatorVerse:session:{user_id}:{session_id}`
     - `CreatorVerse:refresh:{user_id}:{token}`
     - `CreatorVerse:rbac:user:{user_id}`
   - Sets user roles & permissions

6. Cleanup Operations (Redis Operations #8-9)
   - Delete OTP key
   - Add email to Bloom filter
   - **NEW**: Error handling with logging

## Redis Operation Summary
- Registration Request: 5 operations (optimized with pipelines)
- OTP Verification: 9 operations
- Total per full flow: 14 Redis operations

## Database Operation Summary
- Registration Request: 1 transaction, 1 write
- OTP Verification: 1 transaction, 2-3 writes
- Total per full flow: 2 transactions, 3-4 writes

## Model Relationships
```mermaid
graph TD
    A[users.users] --> B[user_roles]
    A --> C[user_auth_methods]
    A --> D[user_sessions]
```

## Security Measures
1. **OTP Protection**:
   - Hash stored, not plaintext
   - Limited attempts (3)
   - 15-minute expiry
   - Lockout mechanism (5 minutes)
   - **NEW**: Rate limiting tracking

2. **Session Security**:
   - Unique session IDs
   - Refresh token rotation
   - Client info tracking
   - Role-based access

3. **Data Validation**:
   - **NEW**: Blocking of reserved domains (RFC 2606)
   - **NEW**: Blocking of test/demo domains
   - **NEW**: Blocking of company internal domains
   - Email MX verification with caching
   - Bloom filter for duplicate detection
   - Transaction integrity
   - Rate limiting

4. **Role Validation**:
   - **NEW**: Role UUID validation
   - **NEW**: Role-endpoint matching
   - **NEW**: Cache-aside pattern for master roles

## Cache Patterns
1. **Cache Aside**:
   - User roles cache
   - Session information
   - Bloom filter for emails
   - **NEW**: Domain MX cache
   - **NEW**: Master roles cache

2. **Write Through**:
   - User creation
   - Role assignments
   - Session records

3. **Redis Pipelines**:
   - **NEW**: OTP creation and management
   - **NEW**: Batch operations for performance

## Performance Optimizations
1. **Email Validation**:
   - **NEW**: Environment-aware validation (99% faster in dev)
   - **NEW**: Domain MX caching (95% faster for repeated domains)
   - **NEW**: Performance metrics tracking

2. **Redis Operations**:
   - **NEW**: Pipeline operations for OTP
   - **NEW**: Batch operations for multiple users

3. **Bloom Filter**:
   - **NEW**: Optimized bit array sizing
   - **NEW**: Double hashing technique

## Error Handling
1. Redis Failures:
   - **NEW**: Graceful degradation for each operation
   - **NEW**: Fallback mechanisms
   - **NEW**: Detailed error logging
   - **NEW**: Function-level retries

2. Database Failures:
   - Transaction rollback
   - Cache cleanup
   - User notification
   - **NEW**: Detailed error reporting

3. Email Service:
   - Retry logic (3 attempts)
   - Exponential backoff
   - Failure logging
   - **NEW**: Cleanup after failure

## Admin Dashboard Verification Guide

### Influencer Data Integrity Checks

1. **User Table Verification**
```sql
-- Check for proper influencer registration status
SELECT u.id, u.email, u.status, u.is_email_verified, u.register_source, 
       u.created_at, u.updated_at
FROM users.users u
JOIN users.user_roles ur ON u.id = ur.user_id
JOIN master.roles r ON ur.role_id = r.id
WHERE r.role_name ILIKE '%influencer%'
ORDER BY u.created_at DESC;

-- Check for duplicate email entries
SELECT email, COUNT(*) 
FROM users.users 
GROUP BY email 
HAVING COUNT(*) > 1;

-- Check for incomplete registrations (stuck in 'requested' state)
SELECT * FROM users.users 
WHERE status = 'requested' 
AND created_at < NOW() - INTERVAL '24 hours';
```

2. **OAuth Integration Verification**
```sql
-- Check OAuth account linkages
SELECT u.email, oa.provider, oa.external_id, oa.created_at,
       sp.username, sp.follower_count
FROM users.users u
JOIN oauth_accounts oa ON u.id = oa.user_id
LEFT JOIN social_profiles sp ON oa.id = sp.oauth_account_id
WHERE u.register_source IN (3,4,5);  -- Google, YouTube, Instagram

-- Find orphaned OAuth accounts
SELECT oa.* FROM oauth_accounts oa
LEFT JOIN users.users u ON oa.user_id = u.id
WHERE u.id IS NULL;
```

3. **Social Profile Data Health**
```sql
-- Check for missing or outdated social data
SELECT sp.id, sp.service, sp.username,
       sp.fetched_at,
       NOW() - sp.fetched_at as data_age
FROM social_profiles sp
WHERE sp.fetched_at < NOW() - INTERVAL '7 days'
   OR sp.follower_count IS NULL;

-- Verify follower count consistency
SELECT sp.id, sp.username, sp.service,
       sp.follower_count,
       sp.fetched_at,
       sp.updated_at
FROM social_profiles sp
WHERE sp.follower_count = 0 
   OR sp.follower_count IS NULL;
```

4. **Authentication Method Verification**
```sql
-- Check user auth methods
SELECT u.email, 
       mam.method_key,
       uam.is_enabled,
       uam.enabled_at
FROM users.users u
JOIN user_auth_methods uam ON u.id = uam.user_id
JOIN master_auth_methods mam ON uam.auth_method_id = mam.id
WHERE u.register_source IN (1,2,3,4,5);

-- Find users without any auth method
SELECT u.* FROM users.users u
LEFT JOIN user_auth_methods uam ON u.id = uam.user_id
WHERE uam.id IS NULL;
```

### Common Issues & Resolution Steps

1. **Stuck Registrations**
- Issue: Users in 'requested' state > 24 hours
- Check: Review OTP verification logs
- Action: Clean up or complete registration

2. **OAuth Data Sync**
- Issue: Missing social profile data
- Check: OAuth token validity
- Action: Trigger manual refresh

3. **Data Consistency**
- Issue: Mismatched registration source vs auth methods
- Check: Run verification queries above
- Action: Align auth methods with register_source

4. **Performance Optimization**
```sql
-- Check indexes usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read
FROM pg_stat_user_indexes
WHERE schemaname = 'users'
ORDER BY idx_scan DESC;

-- Find slow queries related to influencer data
SELECT query, calls, total_exec_time, mean_exec_time
FROM pg_stat_statements
WHERE query ILIKE '%influencer%'
ORDER BY mean_exec_time DESC
LIMIT 10;
```

### Cache Verification

1. **Redis Health Check**
```bash
# Check Bloom filter accuracy
redis-cli --stat "BF.INFO CreatorVerse:email:bloom"

# Check OTP distribution
redis-cli --scan --pattern "CreatorVerse:otp:*" | wc -l

# Verify session consistency
redis-cli --scan --pattern "CreatorVerse:session:*" | wc -l

# NEW: Check email validation cache hit rates
redis-cli --scan --pattern "CreatorVerse:email_validation:domain_mx:*" | wc -l
```

2. **Cache Hit Rates**
```sql
-- Compare cache vs DB lookups
SELECT date_trunc('hour', query_start) as hour,
       count(*) FILTER (WHERE cardinality(wait_event_type) = 0) as cache_hits,
       count(*) FILTER (WHERE cardinality(wait_event_type) > 0) as cache_misses
FROM pg_stat_activity
WHERE query ILIKE '%influencer%'
GROUP BY 1
ORDER BY 1 DESC;
```

### Recommended Monitoring Metrics

1. **Registration Flow**
- OTP generation rate
- Verification success rate
- Average time to complete registration
- OAuth conversion rate
- **NEW**: Email validation performance metrics

2. **Data Quality**
- % of complete profiles
- Social data freshness
- Follower count variance
- **NEW**: Reserved domain block rates

3. **System Health**
- Cache hit rates
- Average query performance
- Index efficiency
- Transaction rollback rate
- **NEW**: Redis pipeline success rates

### Emergency Response Guide

1. **Registration Issues**
- Check Redis connectivity
- Verify email service
- Review OTP logs
- Check Bloom filter false positives
- **NEW**: Verify role validation cache

2. **OAuth Problems**
- Verify provider API status
- Check token refresh mechanism
- Review rate limits

3. **Data Inconsistency**
- Lock affected accounts
- Review transaction logs
- Execute repair queries
- Update cache

4. **Performance Problems**
- Check query plans
- Review index usage
- Clear problem caches
- Scale resources if needed
- **NEW**: Check Redis pipeline operations

## OAuth Authentication Flow

### 1. OAuth Initiation (POST /v1/common/oauth/initiate)
**Request Schema**: `OAuthInitiateRequest` 
```json
{
  "provider": "google",
  "redirect_uri": "https://app.creatorverse.com/auth/callback",
  "user_type": "influencer"
}
```

**Flow Steps**:
1. User Type Validation
   - Converts string to UserType enum
   - Validates against allowed types

2. Provider Validation
   - Currently supports "google" and "instagram"
   - Returns 400 error for unsupported providers

3. CSRF State Generation
   - Generates secure random token
   - Uses `generate_csrf_state()` function 

4. State Storage (Redis Operation)
   - Key: `CreatorVerse:oauth:state:{state}`
   - Value: `{provider}:{user_type}` (e.g., "google:influencer")
   - TTL: 300 seconds (5 minutes)
   - Fallback: In-memory store if Redis unavailable

5. OAuth URL Generation
   - For Google: Includes YouTube scopes
   - For Instagram: Includes user_profile and user_media scopes
   - Returns auth URL and state to client

**Response**:
```json
{
  "auth_url": "https://accounts.google.com/o/oauth2/v2/auth?...",
  "state": "random-state-token"
}
```

### 2. OAuth Callback (GET /v1/common/oauth/callback)
**Query Parameters**:
- `code`: Authorization code from provider
- `state`: Original CSRF state token

**Flow Steps**:
1. State Validation
   - Key: `CreatorVerse:oauth:state:{state}`
   - Retrieves and deletes the stored state
   - Extracts provider and user_type
   - Fails if state invalid/expired

2. Token Exchange
   - Exchanges code for provider tokens (access_token, refresh_token)
   - Handles network errors with retry logic
   - Different flows for Google vs Instagram

3. User Info Retrieval
   - Fetches user profile from provider API
   - Extracts email, name, picture, etc.

4. User Creation/Update
   - **Critical Method**: `create_or_update_user_from_oauth()`
   - Flow:
     1. Domain validation for brand users
     2. Role UUID lookup from Redis cache
        - Key: `CreatorVerse:rbac:roles`
        - Matches user_type to role UUID
        - Falls back to database if not in cache
     3. Auth method identification
     4. User existence check (cache-aside pattern)
        - Key: `RedisKeys.user_by_email(user_email)`
     5. User record creation if new:
        - Sets name, profile image, status, etc.
        - Marks email as verified
        - Sets register_source to provider enum value
     6. OAuth account linkage
     7. User role assignment with specific role_uuid
     8. For influencers with Google: YouTube profile fetching
     9. Social profile creation

5. Session & Token Management
   - Creates user session
   - Issues JWT tokens with:
     - User ID in `sub` claim
     - Session ID in `ssid` claim
     - Role names in `roles` claim
     - Permissions in `permissions` claim
   - Caches session in Redis
     - Key: `CreatorVerse:session:{user_id}:{session_id}`
   - Caches refresh token
     - Key: `CreatorVerse:refresh:{user_id}:{refresh_token}`

**Response**:
```json
{
  "access_token": "jwt-token",
  "refresh_token": "refresh-token",
  "token_type": "bearer",
  "expires_in": 1800,
  "message": "Login successful!" or "Registration successful!"
}
```

### 3. YouTube Profile Enrichment (For Google OAuth Only)
When an influencer authenticates with Google OAuth, the system automatically:

1. Uses the access token to fetch YouTube channel data
2. Creates a YouTube social profile with:
   - Subscriber count
   - View count
   - Channel URL
   - Description
   - Thumbnail image
3. Links this profile to the OAuth account
4. Updates this data on subsequent logins

### Role UUID Handling in OAuth Flow
The `role_uuid` is handled throughout the OAuth flow as follows:

1. Storage in OAuth State
   - `user_type` string stored in Redis under `CreatorVerse:oauth:state:{state}`
   - Later mapped back to appropriate role UUID

2. Role UUID Lookup
   - First checks Redis cache using `RedisKeys.rbac_roles()`
   - Falls back to database query for `MasterRole` if not in cache
   - Default roles used as last resort:
     ```
     "********-1b15-4724-b176-270dff840ef7": "influencer"
     "9798e242-f889-497f-8a25-65479a57f7fa": "org_owner"  
     "4d0810d1-17be-444c-9376-add7822ce349": "org_admin"
     "2389b63c-b9c1-4e81-b786-8ba2eaa58bf3": "brand_user"
     ```

3. Role Assignment
   - UUID stored in `users.user_roles` table
   - Role name used in token `roles` claim array
   - Role permissions fetched from Redis key `RedisKeys.rbac_role_permissions(role_id)`

### Error Handling & Edge Cases

1. **Redis Unavailability**
   - Falls back to in-memory state store for CSRF state
   - Falls back to default roles for role validation
   - Graceful degradation with logging

2. **Invalid Tokens**
   - Proper HTTP 400 responses
   - Detailed error messages
   - Audit logging for security events

3. **Network Issues**
   - Timeout handling (30 seconds for external APIs)
   - Retry logic with exponential backoff
   - Fallbacks to default behavior when possible

4. **Session Management**
   - Enforces maximum sessions per user
   - Handles token revocation
   - Tracks user sessions across devices

### Performance Considerations

1. **Cache-Aside Pattern**
   - Used for roles, users, and permissions
   - Reduces database load
   - Improves response times

2. **Connection Pooling**
   - Database connections reused
   - HTTP clients maintain connection pools
   - Redis connections pooled

3. **Asynchronous Operations**
   - Profile enrichment happens asynchronously
   - Email sending non-blocking
   - Error logging asynchronous

4. **Batch Redis Operations**
   - Uses pipelines for multiple operations
   - Reduces network roundtrips
   - Improves throughput
